<template>
  <view>
    <navigationBar path="/pages/mine/index" />
    <view class="collect-search">
      <uni-search-bar
        v-model="searchValue"
        class="searchBar"
        radius="50"
        placeholder="请输入标题"
        clearButton="auto"
        cancelButton="none"
        @confirm="getCourseData"
      />
      <view class="searchButton" @click="getCourseData">搜索</view>
    </view>
    <view class="collect-dropdown">
      <zb-dropdown-menu style="width: 100%">
        <zb-dropdown-item
          name="one"
          :options="catalogueTableData"
          v-model="value1"
          @change="getCourseData"
        ></zb-dropdown-item>
        <zb-dropdown-item
          name="two"
          :options="courseTypeData"
          v-model="value2"
          @change="getCourseData"
        ></zb-dropdown-item>
      </zb-dropdown-menu>
    </view>
    <view v-show="courseList.length" class="collect-list">
      <view class="collect-list-content" v-for="item in courseList" :key="item.courseId">
        <view class="collect-list-content-img">
          <image :src="item.courseImage" @click="jumpTo(item)"></image>
        </view>
        <view class="collect-list-content-info">
          <view class="collect-list-content-name" @click="jumpTo(item)">{{ item.courseName }}</view>

          <view class="tag-view">
            <uni-tag :circle="true" :text="item.catalogueName" type="primary" />
            <button @click="cancelCollect(item)">取消收藏</button>
          </view>
        </view>
      </view>
    </view>
    <xw-empty :isShow="!courseList.length" text="暂无课程" textColor="#777777"></xw-empty>
  </view>
</template>

<script>
  import { catalogueType } from "@/utils/constant.js"
  import { getCatalogueList } from "@/api/system/catalogue.js"
  import { listCourse } from "@/api/course"
  import xwEmpty from "@/components/xw-empty/xw-empty"
  import { editFavoriteOrNot } from "@/api/course/user-link"

  export default {
    components: { xwEmpty },
    onLoad() {
      this.getCourseData("onLoad")
      this.fetchCatalogueData()
      this.getCourseTypeList()
    },

    onReachBottom() {
      if (this.courseList.length !== this.dataTotal) {
        this.pageNum += 1
        this.getCourseData("onReachBottom")
      }
    },

    data() {
      return {
        searchValue: "",
        pageNum: 1,
        pageSize: 12,
        dataTotal: 0,
        value1: undefined,
        value2: undefined,
        catalogueTableData: [],
        courseTypeData: [],
        courseList: []
      }
    },

    methods: {
      async getCourseData(flag) {
        if (flag !== "onReachBottom") {
          this.pageNum = 1
        }
        let queryData = {
          pageSize: this.pageSize,
          pageNum: this.pageNum,
          catalogueId: this.value1,
          courseType: this.value2,
          courseName: this.searchValue,
          requiredCourse: this.$store.state.user.userId,
          favoriteOrNot: true
        }
        const { rows, total } = await listCourse(queryData)
        this.dataTotal = total
        this.courseList = flag === "onReachBottom" ? this.courseList.concat(rows) : rows
      },

      async fetchCatalogueData() {
        getCatalogueList({ catalogueType: catalogueType.COURSE_CATALOGUE }).then(response => {
          this.catalogueTableData = response.rows.map(({ catalogueId, catalogueName }) => ({
            text: catalogueName,
            value: catalogueId
          }))
          if (this.catalogueTableData[0].text === "全部目录") return
          this.catalogueTableData.unshift({
            text: "全部目录",
            value: undefined
          })
        })
      },

      async getCourseTypeList() {
        this.courseTypeData = await this.$getDict("course_type")
        if (this.courseTypeData[0].text === "全部类型") return
        this.courseTypeData.unshift({
          text: "全部类型",
          value: undefined
        })
      },

      jumpTo(item) {
        uni.navigateTo({
          url: "/pages/course/detail/index?id=" + item.courseId
        })
      },

      //取消收藏
      async cancelCollect(item) {
        let queryData = {
          courseId: item.courseId,
          userId: this.$store.state.user.userId,
          favoriteOrNot: false
        }
        await editFavoriteOrNot(queryData)
        uni.showToast({
          title: "取消收藏成功",
          icon: "success"
        })
        this.getCourseData("onLoad")
      }
    }
  }
</script>

<style lang="scss" scoped>
  ::v-deep .uni-searchbar__box {
    justify-content: unset;
  }
  .collect-search {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #fff;
    z-index: 999;
    .searchBar {
      width: 100%;
    }
  }

  .collect-list {
    background-color: #fff;
    display: flex;
    flex-direction: column;
    .collect-list-content {
      display: flex;
      padding: 10px;
      border-bottom: 1px solid #f1f1f1;
      .collect-list-content-img {
        width: 30%;
        > image {
          height: 100px;
        }
      }
      .collect-list-content-info {
        width: 100%;
        display: flex;
        flex-direction: column;
        margin-left: 10px;
        margin-top: 10px;
        .collect-list-content-name {
          width: 200px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          margin-bottom: 20px;
        }

        .tag-view {
          display: flex;
          align-items: center;
          justify-content: space-between;
          > button {
            font-size: 12px;
            margin-left: 0;
            margin-right: 0;
          }
        }
      }
    }
  }

  .searchButton {
    position: absolute;
    right: 30px;
    color: #1e8ffd;
  }
</style>

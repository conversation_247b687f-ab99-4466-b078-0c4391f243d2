<!--
 * @Description: 课程详情-下方tabs
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-10-26 11:07:37
 * @LastEditTime: 2024-12-20 14:09:58
-->

<template>
  <view class="detail-body">
    <custom-tabs type="a1" :value="activeTab">
      <!-- =========================== 章节相关 ================================= -->
      <custom-tab-pane label="目录" name="a1_0">
        <view class="bg-white p-5" v-for="(item, index) in passageList" :key="item.coursePassageId">
          <view class="text-lg font-bold truncate w-full">
            {{ index + 1 }} . {{ item.coursePassageName }}
          </view>
          <view
            class="border-b border-b-dotted border-gray-200 p-2"
            v-if="item.videoFileList && item.videoFileList.length > 0"
          >
            <view
              class="flex text-base py-2.5 ml-5"
              v-for="(item2, index2) in item.videoFileList"
              :key="item2.passageId"
              :class="{
                'text-blue-500': currentPlayItem.passageId === item2.passageId
              }"
              @click="setIndex(item2, index, index2)"
            >
              {{ item2.fileName || getFileName(item2.fileUrl) }}
            </view>
          </view>
        </view>
      </custom-tab-pane>
      <!-- =========================== 详情相关 ================================= -->
      <custom-tab-pane label="详情" name="a1_1">
        <view>
          <view class="bg-white mb-1.5">
            <view class="flex justify-between p-5 pb-0">
              <view>收藏人数：{{ courseInfo.favoriteCounts || 0 }}</view>
              <view>讲师：{{ courseInfo.lecturer }}</view>
              <view>学分：{{ courseInfo.credits || 0 }}</view>
              <view>学时：{{ courseInfo.creditHours || 0 }}</view>
            </view>
            <o-divider dashed lineColor="d7d7d7"></o-divider>
            <view class="flex justify-between px-5 pb-5">
              <view class="flex items-center" @click="handleRate">
                评分：
                <uni-rate
                  @change="changeRate"
                  allow-half
                  v-model="courseInfo.selfCourseGrade"
                  :disabled="!learnedStatus || !!this.courseInfo.selfCourseGrade"
                />
                <view class="ml-2.5">{{
                  (courseInfo.selfCourseGrade && courseInfo.selfCourseGrade.toFixed(2)) || 0
                }}</view>
              </view>
              <view class="flex items-center cursor-pointer" @click="collectCourse">
                <image
                  class="w-5 h-5"
                  :src="
                    courseInfo.favoriteOrNot == true
                      ? '/static/images/mine/favor3.png'
                      : '/static/images/mine/favor2.png'
                  "
                ></image>
                <view class="ml-1">收藏</view>
              </view>
            </view>
          </view>

          <view class="bg-white">
            <view class="p-5">
              <view class="text-base font-medium">课程介绍</view>
              <view class="mt-4 leading-6">{{ courseInfo.desc || "暂无" }}</view>
            </view>
            <o-divider dashed lineColor="d7d7d7"></o-divider>
            <view class="p-5">
              <view class="text-base font-medium">课程大纲</view>
              <view class="mt-4 leading-6">{{ courseInfo.curriculum || "暂无" }}</view>
            </view>
            <o-divider dashed lineColor="d7d7d7"></o-divider>
            <view class="p-5">
              <view class="text-base font-medium">课程目标</view>
              <view class="mt-4 leading-6">{{ courseInfo.courseObjectives || "暂无" }}</view>
            </view>
          </view>
        </view>
      </custom-tab-pane>
      <!-- =========================== 评论相关 ================================= -->
      <custom-tab-pane :label="`评论(${dataTotal})`" name="a1_2">
        <view class="h-[calc(100vh-388px)] bg-gray-100 relative w-full">
          <!-- 评论列表区域 -->
          <scroll-view class="h-[calc(100%-50px)] w-full" scroll-y @scrolltolower="loadmore">
            <view v-if="discussList.length > 0" class="pb-[60px] w-full">
              <!-- 评论项 -->
              <view
                v-for="item in discussList"
                :key="item.traningCourseDiscussId"
                class="flex p-3 bg-white border-b border-gray-100 w-full"
              >
                <image
                  :src="item.discussPortrait || '/static/images/avatar.png'"
                  class="w-[30px] h-[30px] rounded-full flex-shrink-0"
                />
                <view class="flex-1 ml-3 overflow-hidden">
                  <view class="flex justify-between items-center">
                    <text class="text-[12px] text-gray-800">{{ item.discussBy }}</text>
                    <text class="text-[11px] text-gray-400 ml-2 flex-shrink-0">{{
                      item.createTime
                    }}</text>
                  </view>
                  <view class="mt-1 text-[12px] text-gray-600 break-all leading-normal">{{
                    item.discussContent
                  }}</view>
                  <view
                    v-if="item.discussById === $store.state.user.userId"
                    class="mt-1 flex justify-end"
                  >
                    <text
                      class="text-[11px] text-gray-400 hover:text-blue-500 cursor-pointer"
                      @click="removeDiscuss(item.traningCourseDiscussId)"
                      >删除</text
                    >
                  </view>
                </view>
              </view>

              <!-- 加载状态 -->
              <view
                v-if="status === 'loading'"
                class="py-3 text-center text-[12px] text-gray-400 w-full"
              >
                加载中...
              </view>
              <view
                v-if="status === 'noMore' && discussList.length > 0"
                class="py-3 text-center text-[12px] text-gray-400 w-full"
              >
                没有更多数据了
              </view>
            </view>

            <!-- 空状态 -->
            <view
              v-else
              class="h-[calc(100vh-498px)] flex items-center justify-center text-[12px] text-gray-400 w-full"
            >
              暂无评论
            </view>
          </scroll-view>

          <!-- 评论输入区域 -->
          <view class="absolute bottom-0 left-0 right-0 bg-white border-t border-gray-200 w-full">
            <view class="flex items-center px-3 h-[50px]">
              <input
                type="text"
                v-model="discussContent"
                placeholder="文明评论"
                :always-embed="true"
                :adjust-position="true"
                cursor-spacing="20"
                class="flex-1 h-[32px] px-4 bg-gray-50 rounded-md text-[12px]"
              />
              <button
                @click="releaseContent"
                :disabled="!discussContent.length || addFlag"
                :style="{
                  backgroundColor: !discussContent.length || addFlag ? '#a3a3a3' : '#4677fc'
                }"
                class="ml-3 w-[70px] h-[32px] text-white text-[12px] rounded-md flex items-center justify-center"
              >
                发布
              </button>
            </view>
          </view>
        </view>
      </custom-tab-pane>
      <!-- =========================== 试题库相关 ================================= -->
      <custom-tab-pane
        name="a1_3"
        :label="`试题库(${courseInfo.courseQuestionLinkList.length})`"
        v-if="courseInfo.courseQuestionLinkList && courseInfo.courseQuestionLinkList.length > 0"
      >
        <view v-if="questionList && questionList.length" class="bg-white min-h-300px relative p-5">
          <button
            class="absolute right-8 top-8 bg-blue-500 text-white text-sm px-4 py-2 rounded"
            @click="showOrHide"
          >
            {{ showAnswer ? "隐藏答案" : "显示答案" }}
          </button>

          <view class="text-center mt-5">
            第<text class="text-blue-500 font-bold mx-1">{{ questionIndex + 1 }}</text
            >/{{ questionList.length }}题
          </view>

          <view class="mt-5">
            <view class="font-medium mb-5">
              <span class="font-bold">{{ questionIndex + 1 }}.</span>
              ({{ qsMap[questionList[questionIndex].questionType] }})
              {{ questionList[questionIndex].questionName }}
            </view>

            <view v-if="!showAnswer">
              <!-- 单选/判断 -->
              <u-radio-group
                v-model="inputValue"
                v-if="
                  questionList[questionIndex].questionType === 'S' ||
                  questionList[questionIndex].questionType === 'J'
                "
                class="flex flex-col"
              >
                <u-radio
                  v-if="questionList[questionIndex][`item${choice}`]"
                  v-for="choice in 11"
                  :name="String.fromCharCode(64 + choice)"
                  :key="choice"
                  :label="`${String.fromCharCode(64 + choice)}  ${
                    questionList[questionIndex][`item${choice}`]
                  }`"
                  class="mb-4"
                >
                </u-radio>
              </u-radio-group>

              <!-- 多选题 -->
              <u-checkbox-group
                v-model="inputValue"
                v-else-if="questionList[questionIndex].questionType === 'M'"
                class="flex flex-col"
              >
                <u-checkbox
                  v-for="multiChoice in 11"
                  v-if="questionList[questionIndex][`item${multiChoice}`]"
                  :key="multiChoice"
                  :label="`${String.fromCharCode(64 + multiChoice)}  ${
                    questionList[questionIndex][`item${multiChoice}`]
                  }`"
                  :name="String.fromCharCode(64 + multiChoice)"
                  class="mb-4"
                >
                </u-checkbox>
              </u-checkbox-group>

              <!-- 填空 -->
              <view v-else class="mb-8">
                <view
                  class="flex mb-5 w-70% items-center"
                  v-for="blankChoice in questionList[questionIndex].blankCount"
                  :key="blankChoice"
                >
                  <view class="mr-2">填空{{ blankChoice }}：</view>
                  <uni-easyinput
                    v-model="blankValue[blankChoice - 1]"
                    placeholder="请输入内容"
                    :clearable="false"
                  ></uni-easyinput>
                </view>
              </view>
            </view>

            <view v-else>
              <!-- 单选/多选/判断题的情况 -->
              <view v-if="questionList[questionIndex].questionType !== 'F'" class="space-y-4">
                <view v-for="mindex in 11" :key="mindex">
                  <view v-if="questionList[questionIndex][`item${mindex}`]" class="mb-4">
                    <view class="flex items-center">
                      <image
                        v-if="
                          isCorrect(questionList[questionIndex], String.fromCharCode(64 + mindex))
                        "
                        class="w-5 h-5 mr-2"
                        src="/static/images/competition/correct.png"
                      />
                      <image
                        v-else-if="
                          isWrong(questionList[questionIndex], String.fromCharCode(64 + mindex))
                        "
                        class="w-5 h-5 mr-2"
                        src="/static/images/competition/error.png"
                      />
                      <text
                        v-else
                        class="w-4 h-4 border border-gray-500 rounded-full text-xs flex-center mr-2"
                      >
                        {{ String.fromCharCode(64 + mindex) }}
                      </text>
                      <text>{{ questionList[questionIndex][`item${mindex}`] }}</text>
                    </view>
                  </view>
                </view>
              </view>

              <!-- 填空题的情况 -->
              <view v-else class="mb-8">
                <view
                  v-for="findex in questionList[questionIndex].blankCount"
                  :key="findex"
                  class="mb-5"
                >
                  <view class="flex mb-5 w-70% items-center">
                    <view class="mr-2">填空{{ findex }}：</view>
                    <uni-easyinput
                      v-model="blankValue[findex - 1]"
                      disabled
                      class="flex-1"
                    ></uni-easyinput>
                    <image
                      class="w-5 h-5 ml-5"
                      v-if="
                        blankValue[findex - 1] ===
                        questionList[questionIndex].answer.split('::')[findex - 1]
                      "
                      src="/static/images/competition/correct.png"
                    />
                    <image class="w-5 h-5 ml-5" v-else src="/static/images/competition/error.png" />
                  </view>

                  <view
                    v-if="
                      blankValue[findex - 1] !==
                      questionList[questionIndex].answer.split('::')[findex - 1]
                    "
                    class="mb-5"
                  >
                    正确答案：{{ questionList[questionIndex].answer.split("::")[findex - 1] }}
                  </view>
                </view>
              </view>
            </view>
          </view>

          <!-- 切换题目 -->
          <view class="flex justify-center space-x-4 mt-8">
            <button
              v-if="questionIndex !== 0"
              class="bg-green-500 text-white px-4 py-2 rounded text-sm"
              @click="changeQuestionIndex('previous')"
            >
              上一题
            </button>
            <button
              v-if="questionIndex !== questionList.length - 1"
              class="bg-red-500 text-white px-4 py-2 rounded text-sm"
              @click="changeQuestionIndex('next')"
            >
              下一题
            </button>
          </view>
        </view>
      </custom-tab-pane>
    </custom-tabs>
  </view>
</template>

<script>
  import { getFileName, showConfirm } from "@/utils/common"
  import { editFavoriteOrNot } from "@/api/course/user-link.js"
  import { listDiscuss, addCourseDiscuss, deleteCourseDiscuss } from "@/api/course/discuss.js"
  import { editCourseGrade } from "@/api/course/grade.js"
  import { questionType as qsMap } from "@/utils/constant.js"
  export default {
    emits: ["playItemChange", "refreshCourseData"],
    inject: ["finalLearnedStatus"],
    data() {
      return {
        activeTab: 0,
        questionIndex: 0,
        questionList: [],
        qsMap,
        inputValue: undefined,
        showAnswer: false,
        blankValue: [],
        addFlag: false,
        removeFlag: false,
        discussContent: "",
        pageNum: 1,
        pageSize: 10,
        dataTotal: 0,
        status: "more",
        discussList: []
      }
    },
    mounted() {
      this.fetchDiscussData()
    },
    props: {
      courseInfo: {
        type: Object,
        default: () => ({})
      },
      passageList: {
        type: Array,
        default: () => []
      },
      currentPlayItem: {
        type: Object,
        default: () => ({})
      }
    },
    computed: {
      learnedStatus() {
        return this.finalLearnedStatus()
      }
    },
    methods: {
      getFileName,
      // =========================== 章节相关 =================================
      // 切换章节
      async setIndex(item, index, index2) {
        this.$emit("playItemChange", { item, index, index2 })
      },

      // =========================== 详情相关 =================================
      // 评分
      handleRate() {
        if (!this.learnedStatus) {
          uni.showToast({
            title: "请在课程学习完成后评价",
            icon: "error"
          })
        } else if (this.courseInfo.selfCourseGrade) {
          uni.showToast({
            title: "您已评价过该课程",
            icon: "error"
          })
        }
      },
      async changeRate(e) {
        if (this.learnedStatus && !this.courseInfo.selfCourseGrade) {
          let queryData = {
            courseId: this.courseInfo.courseId,
            grade: this.courseInfo.selfCourseGrade
          }
          await editCourseGrade(queryData)
          uni.showToast({
            title: "感谢您的评分！",
            icon: "success"
          })
        }
        this.$emit("refreshCourseData")
      },
      // 收藏课程
      async collectCourse() {
        if (this.courseInfo.favoriteOrNot == true) {
          let queryData = {
            courseId: this.courseInfo.courseId,
            userId: this.$store.state.user.userId,
            favoriteOrNot: false
          }
          await editFavoriteOrNot(queryData)
          uni.showToast({
            title: "取消收藏成功！",
            icon: "success"
          })
        } else {
          let queryData = {
            courseId: this.courseInfo.courseId,
            userId: this.$store.state.user.userId,
            favoriteOrNot: true
          }
          await editFavoriteOrNot(queryData)
          uni.showToast({
            title: "收藏成功！",
            icon: "success"
          })
        }
        this.$emit("refreshCourseData")
      },

      // =========================== 评论相关 =================================
      // 获取评论
      async fetchDiscussData() {
        let queryData = {
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          courseId: this.courseInfo.courseId
        }
        const discussListData = await listDiscuss(queryData)
        this.discussList = this.discussList.concat(discussListData.rows)
        this.dataTotal = discussListData.total
        if (this.pageNum * this.pageSize < this.dataTotal) {
          this.status = "more"
        } else {
          this.status = "noMore"
        }
      },
      // 发布评论
      async releaseContent() {
        this.addFlag = true
        let addCourseDiscussData = await addCourseDiscuss({
          courseId: this.courseInfo.courseId,
          discussContent: this.discussContent,
          discussById: this.$store.state.user.userId,
          discussPortrait: this.$store.state.user.avatarUrl,
          discussBy: this.$store.state.user.nickName
        })
        if (addCourseDiscussData.code === 200) {
          this.pageNum = 1
          const discussListData = await listDiscuss({
            pageNum: this.pageNum,
            pageSize: this.pageSize,
            courseId: this.courseInfo.courseId
          })
          this.discussList = discussListData.rows
          this.dataTotal = discussListData.total
          this.discussContent = ""
        }
        this.addFlag = false
      },
      // 删除评论(评论只能一条条删除)
      async removeDiscuss(id) {
        if (this.removeFlag || !id) return
        const res = await showConfirm("是否确定删除该评论?")
        if (!res.confirm) return
        this.removeFlag = true
        await deleteCourseDiscuss({ id })
        const discussListData = await listDiscuss({
          courseId: this.courseInfo.courseId
        })
        this.discussList = discussListData.rows
        this.dataTotal = discussListData.total
        this.removeFlag = false
      },
      // 下拉加载更多评论
      async loadmore() {
        this.status = "loading"
        if (this.dataTotal < this.pageNum * this.pageSize) {
          this.status = "noMore"
        } else {
          this.pageNum += 1
          await this.fetchDiscussData()
        }
      },

      // =========================== 试题库相关 =================================
      changeQuestionIndex(type) {
        this.inputValue = undefined
        this.showAnswer = false
        this.blankValue = []
        if (type === "previous") this.questionIndex--
        else if (type === "next") this.questionIndex++
      },

      showOrHide() {
        if (this.showAnswer) this.showAnswer = false
        else this.showAnswer = true
      },

      // 正确的选项
      isCorrect(item, choice) {
        if (item.questionType === "S" || item.questionType === "J") {
          return item.answer === choice
        } else if (item.questionType === "M") {
          if (item.answer.includes(",")) {
            return item.answer.split(",").indexOf(choice) > -1
          }
          return item.answer === choice
        }
        return false
      },

      // 错误的选项
      isWrong(item, choice) {
        if (!this.inputValue) return false
        if (item.questionType === "S" || item.questionType === "J") {
          return this.inputValue === choice && this.inputValue !== item.answer
        } else if (item.questionType === "M") {
          return this.inputValue.includes(choice) && !item.answer.includes(choice)
        }
        return false
      }
    }
  }
</script>

<style lang="scss" scoped>
  :deep(.uni-list-chat__content-title) {
    font-size: 30rpx;
  }

  :deep(.uni-ellipsis) {
    white-space: normal;
    line-height: 20px;
  }

  :deep(.u-radio__text),
  :deep(.u-checkbox) {
    span > br {
      display: none;
    }
  }
</style>

import request from "@/utils/request"

// 查询资料列表
export function listManage(params) {
  return request({
    url: "/course/manage/list",
    method: "get",
    params
  })
}

// 点击查看/点击下载埋点接口
export function editRelateNumber(data) {
  return request({
    url: "/course/manage/updateNumber",
    method: "post",
    data
  })
}

// 查询资料详细
export function getMeans(paperId) {
  return request({
    url: "/course/manage/" + paperId,
    method: "get"
  })
}

<!--
 * @Description: 考试结果模块
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-12-13 09:27:25
 * @LastEditTime: 2024-12-13 11:56:34
-->

<template>
  <view class="flex flex-col min-h-screen bg-gray-100">
    <!-- 导航栏 -->
    <navigationBar
      title="考试结果"
      :showPersonal="false"
      :path="`/pages/mine/exam/prepare/index?baseId=${baseId}&arrangeId=${arrangeId}`"
    />

    <!-- 内容区域 -->
    <view class="flex-1 px-4 py-4">
      <!-- 成绩卡片 -->
      <view class="bg-white rounded-3xl shadow-md overflow-hidden">
        <!-- 蓝色背景区域 -->
        <view class="relative px-6 pt-10 pb-24 rounded-t-2xl blue_wave_bg">
          <!-- 分数信息 -->
          <view class="flex justify-between items-start text-white">
            <view class="space-y-2">
              <view class="text-lg font-medium">
                总分
                <span class="ml-4 font-bold text-xl">{{ userPaperScore }}分</span>
              </view>
              <view class="text-base">
                本次考试用时
                <span class="ml-4 font-bold text-xl">{{ examDuration }}</span>
              </view>
            </view>
            <view class="flex gap-2 items-end mr-4">
              <span class="text-6xl font-bold">{{ score }}</span>
              <span class="self-end">分</span>
            </view>
          </view>

          <!-- 波浪效果 -->
          <view
            class="absolute left-0 right-0 bottom-0 h-10 bg-[url('data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 1440 320\'%3E%3Cpath fill=\'%23fff\' fill-opacity=\'1\' d=\'M0,96L48,112C96,128,192,160,288,160C384,160,480,128,576,112C672,96,768,96,864,112C960,128,1056,160,1152,160C1248,160,1344,128,1392,112L1440,96L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z\'%3E%3C/path%3E%3C/svg%3E')] bg-bottom bg-cover"
          ></view>
        </view>

        <!-- 奖牌图片 -->
        <view class="flex justify-center mt-10 mb-20">
          <image
            src="https://training-voc.obs.cn-north-4.myhuaweicloud.com/medal.png"
            mode="aspectFit"
            class="w-55 h-55"
          />
        </view>
      </view>

      <!-- 查看解析按钮 -->
      <view class="mt-6">
        <button
          class="w-full bg-[#1e91fc] text-white py-3 rounded-full text-lg font-medium border-none"
          @click="goToAnalysis"
        >
          查看解析
        </button>
      </view>
    </view>
  </view>
</template>

<script>
  export default {
    data() {
      return {
        userPaperScore: 0,
        score: 0,
        examDuration: "",
        baseId: "",
        arrangeId: "",
        paperAnswerId: ""
      }
    },

    onLoad(options) {
      this.userPaperScore = options.userPaperScore || 0
      this.score = options.score || 0
      this.examDuration = this.handleDuration(options.duration)
      this.baseId = options.baseId
      this.arrangeId = options.arrangeId
      this.paperAnswerId = options.paperAnswerId
    },

    methods: {
      goToAnalysis() {
        uni.navigateTo({
          url: `/pages/mine/exam/examed/index?baseId=${this.baseId}&arrangeId=${this.arrangeId}&paperAnswerId=${this.paperAnswerId}`
        })
      },

      handleDuration(duration) {
        if (!duration) return "0分0秒"

        const minutes = Math.floor(Number(duration) / 60)
        const seconds = Number(duration) % 60
        return `${minutes}分${seconds}秒`
      }
    }
  }
</script>

<style lang="scss" scoped>
  page {
    background-color: #f3f4f6;
  }
  .blue_wave_bg {
    background-image: url(https://training-voc.obs.cn-north-4.myhuaweicloud.com/blue_wave_bg.png);
    background-size: 100% 100%;
    background-position: center;
    background-repeat: no-repeat;
  }
</style>

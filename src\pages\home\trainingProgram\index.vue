<template>
  <view>
    <navigationBar path="/pages/index" />
    <view
      v-for="(item, index) in newsListData"
      :key="item.newsId"
      class="training-program"
    >
      <view class="training-program-list">
        <view class="training-program-list-title">
          {{ item.newsTitle }}
        </view>
        <view class="training-program-list-body">
          <view class="training-program-list-body-info">
            {{ item.newsContent }}
          </view>
          <view class="training-program-list-body-pic">
            <img :src="item.newsCover" alt="" />
          </view>
        </view>
      </view>
    </view>
    <o-divider
      v-if="newsListData.length !== 0"
      lineColor="#d7d7d7"
      textColor="#c4c4c6"
      margin="30rpx 100rpx"
      >到底了</o-divider
    >

    <xw-empty
      :isShow="newsListData.length === 0"
      text="暂无培训项目"
      textColor="#777777"
    ></xw-empty>
  </view>
</template>

<script>
import { list } from "@/api/devops/news.js"
export default {
  data() {
    return {
      pageNum: 1,
      pageSize: 5,
      pageType: 2,
      dataTotal: 0,
      newsListData: [],
      scrollTop: 0,
      status: "loadmore",
      loaded: false
    }
  },

  onShow() {
    this.fetchData()
  },
  //   onPageScroll(e) {
  //     this.scrollTop = e.scrollTop
  //     if (this.scrollTop === 0) {
  //       this.pageNum = 1
  //       this.dataTotal = 0
  //       this.status = "loadmore"
  //     }
  //   },
  onReachBottom() {
    if (this.newsListData.length === this.dataTotal) {
      setTimeout(() => {
        this.status = "noMore"
      }, 1000)
    } else {
      this.pageNum += 1
      this.status = "loading"
      this.fetchData()
    }
  },

  methods: {
    async fetchData() {
      let queryData = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        dataType: this.pageType
      }
      const { rows, total } = await list(queryData)
      this.dataTotal = total
      if (this.newsListData === 0) {
        this.newsListData = rows || []
      } else {
        this.newsListData = this.newsListData.concat(rows)
      }
      this.loaded = true
    }
  }
}
</script>

<style lang="scss">
.training-program {
  background-color: #f5f5f9;
}

.training-program-list {
  margin-bottom: 10rpx;
  margin: 30rpx;
  height: 300rpx;
  background-color: #fff;
  padding: 50rpx;

  .training-program-list-title {
    display: flex;
    margin-bottom: 10rpx;
    color: #d01f25;
    font-size: 35rpx;
  }

  .training-program-list-body {
    display: flex;
    align-items: center;
    font-size: 22rpx;
    line-height: 45rpx;

    .training-program-list-body-info {
      width: 70%;
      word-wrap: break-word;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 3;
    }

    .training-program-list-body-pic {
      display: flex;

      > img {
        margin-left: 40rpx;
        width: 150rpx;
        height: 150rpx;
      }
    }
  }
}
</style>

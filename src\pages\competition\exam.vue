<!--
 * @Description: 测试准备页面
 * @Author: <PERSON><PERSON><PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-09-19 17:32:35
 * @LastEditTime: 2024-07-19 14:24:37
-->
<template>
  <view
    class="con"
    :class="{ 'eduxd-bg': domainName === 'eduxd' }"
    :style="
      domainName === 'eduxd'
        ? `background-image: url('${assetsPrefixUrl}eduxd-bg.jpg') !important;`
        : ''
    "
  >
    <view>
      <view
        class="exam-con"
        :style="domainName !== 'eduxd' ? 'background-color: #fff' : ''"
        v-if="examDetailInfo.examConfig"
      >
        <view class="title"> {{ examDetailInfo.baseName }} </view>
        <view class="content time-range">
          <text class="sub-title">测试时间：</text>
          <text class="sub-content" v-if="examRecordInfo.startTime">
            {{ examRecordInfo.startTime }}~{{ examRecordInfo.endTime }}
          </text>
        </view>
        <view class="content">
          <text class="sub-title">测试时长：</text>
          <text class="sub-content">
            {{
              !examDetailInfo.examConfig.examDurationLimit ||
              examDetailInfo.examConfig.examDurationLimit === 0
                ? "不限时"
                : `${examDetailInfo.examConfig.examDurationLimit}分钟`
            }}
          </text>
        </view>
        <view class="content">
          <text class="sub-title">测试次数：</text>
          <text class="sub-content" v-if="examDetailInfo.examConfig.limitNumber === 0">
            无限次
          </text>
          <text class="sub-content" v-else>
            还有
            <text class="highlight">{{ remainExamTimes }} </text>
            次机会
          </text>
        </view>
        <view class="learn-btn">
          <button
            v-if="
              examDetailInfo.examConfig.limitNumber === 0 ||
              examDetailInfo.examConfig.limitNumber === null ||
              remainExamTimes > 0
            "
            size="mini"
            @click="jumpTo"
          >
            开始测试
          </button>
          <button class="disable" v-else>已无测试次数</button>
        </view>
      </view>
      <view class="exam-list" :style="domainName !== 'eduxd' ? 'background-color: #f5f5f9' : ''">
        <view class="exam-log">测试记录</view>
        <template v-if="completedExamRecordList.length > 0">
          <view class="exam-detail" v-for="(item, index) in completedExamRecordList" :key="index">
            <view class="content">
              <text class="sub-title">测试时间：</text>
              <text class="sub-content">{{ item.startTime }}</text>
            </view>
            <view class="content">
              <text class="sub-title">交卷时间：</text>
              <text class="sub-content">{{ item.submitTime }}</text>
            </view>
            <view class="content"
              ><text class="sub-title">测试成绩：</text
              ><text class="sub-content highlight">
                {{ item.userPaperScore }}
              </text></view
            >
            <view class="view-btn">
              <button size="mini" @click="handelExamDetail(item)" style="font-size: 15px">
                查看详情
              </button>
            </view>
          </view>
        </template>
        <view v-else class="nodata">
          <image src="@/static/images/competition/no-data.png"> </image>
          <text class="nodata-text">暂无测试详情</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
  import { getExam, getArrange, getXdExamInfo } from "@/api/exam/base.js"
  import { mapGetters } from "vuex"
  import { assetsPrefixUrl } from "@/utils/constant.js"
  export default {
    data() {
      return {
        assetsPrefixUrl,
        examDetailInfo: {},
        examRecordInfo: {},
        baseId: "2057",
        arrangeId: "2177",
        completedExamRecordList: [] // 已完成考试列表
      }
    },

    computed: {
      ...mapGetters({
        domainName: "domainName"
      }),
      remainExamTimes() {
        if (!this.completedExamRecordList) return 0
        return (
          Number(this.examDetailInfo.examConfig.limitNumber) -
          Number(this.completedExamRecordList.length)
        )
      }
    },
    methods: {
      handelExamDetail(item) {
        uni.navigateTo({
          url: `/pages/competition/examDetail?baseId=${this.baseId}&arrangeId=${this.arrangeId}&paperAnswerId=${item.paperAnswerId}`
        })
      },
      async loadData() {
        const { data } = await getExam(this.baseId)
        this.examDetailInfo = data
      },
      async loadRecordData() {
        const { data } = await getArrange(this.arrangeId)
        this.examRecordInfo = data
        // 已完成的考试才显示在考试记录中
        if (!data.examPaperAnswers || data.examPaperAnswers.length === 0) return
        this.completedExamRecordList = data.examPaperAnswers
          .map(item => {
            if (item.examStatus === "2") {
              return item
            }
          })
          .filter(ite => typeof ite !== "undefined")
      },

      jumpTo() {
        uni.navigateTo({
          url: `/pages/mine/exam/examing/index?baseId=${this.baseId}&arrangeId=${
            this.arrangeId
          }&submintMinimumNumber=${this.examDetailInfo.examConfig.submintMinimumNumber || 0}`
        })
      },
      async eduxdGetArrangeId() {
        const { data } = await getXdExamInfo({ examId: this.baseId })
        this.arrangeId = data?.[0]?.arrangeId || "2177"
      }
    },
    async mounted() {
      if (this.domainName === "eduxd") {
        await this.eduxdGetArrangeId()
      }
      this.loadData()
      this.loadRecordData()
    }
  }
</script>

<style lang="scss" scoped>
  .disable {
    background: #d7d7d7 !important;
  }
  .nodata {
    padding-top: 50rpx;
    line-height: 60px;
    font-size: 14px;
    color: #aaaaaa;
    text-align: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    image {
      width: 300rpx;
      height: 300rpx;
    }
  }
  .highlight {
    color: #d01f25 !important;
    font-weight: bold;
    margin: 0 6rpx;
  }
  .exam-detail {
    background: #fff;
    border: 1px solid #d7d7d7;
    padding: 20rpx;
    margin: 20rpx 0;
    border-radius: 10rpx;
  }
  .exam-log {
    color: #e79930;
    border-bottom: 1px solid #e79930;
    line-height: 60rpx;
    font-weight: bold;
    font-size: 16px;
  }
  .exam-con {
    padding: 10rpx 30rpx 0rpx 30rpx;
  }
  .exam-list {
    padding: 10rpx 30rpx 30rpx 30rpx;
    min-height: calc(100vh - 200px);
  }
  .con {
    min-height: 100vh;
    border-top: 1px solid #f2f2f2;

    .learn-btn {
      margin-top: 0rpx;
      font-size: 12px;
      text-align: center;
      padding-bottom: 10rpx;
      display: flex;
      justify-content: center;
      align-items: center;

      > button {
        font-size: 32rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        background: #ed9d32;
        color: #fff;
        height: 70rpx;
        margin: 30rpx 0;
        width: 70%;
      }
    }
    .view-btn {
      font-size: 12px;
      text-align: center;
      margin-top: 10rpx;
      > button {
        background: #ed9d32;
        color: #fff;
        width: 100%;
      }
    }
    .title {
      font-size: 20px;
      font-weight: bold;
      line-height: 80rpx;
      text-align: center;
    }
    .sub-title {
      font-size: 16px;
      font-weight: bold;
      min-width: 160rpx;
    }

    .content {
      display: flex;
      height: 36px;
      font-size: 16px;
    }
    .time-range {
      margin-bottom: 12px;
    }
  }
  .eduxd-bg {
    background-repeat: no-repeat !important;
    background-size: 100% 100% !important;
    background-position: top !important;
  }
</style>

<template>
  <view class="normal-login-container">
    <image src="/static/images/competition/logbg_1.png" mode="widthFix" style="width: 100%"></image>
    <view class="logincon">
      <image :src="topImageSrc" mode="widthFix" style="width: 100%; margin-top: 20px"></image>
      <view class="bgcon">
        <view class="login-form-content">
          <view class="input-item flex align-center">
            <view class="iconfont icon-user icon"></view>
            <input
              v-model="loginForm.mobile"
              class="input"
              type="text"
              @focus="fphMobile"
              @blur="bphMobile"
              :placeholder="mobileHolder"
              maxlength="11"
              @input="checkCode"
            />
          </view>
          <view class="input-item flex align-center">
            <view class="iconfont icon-password icon"></view>
            <input
              v-model="loginForm.code"
              class="input"
              @focus="fphCode"
              @blur="bphCode"
              :placeholder="codeHolder"
              maxlength="20"
            />
            <view
              @click="getCode"
              style="width: 260rpx; margin-right: 20rpx; color: #3b7ffc"
              :class="{ active: !canCode }"
              >{{ codeMsg }}</view
            >
          </view>

          <view class="action-btn">
            <button @click="handleLogin" class="login-btn cu-btn block bg-blue lg round">
              立即登录
            </button>
          </view>
        </view>

        <view class="xieyi text-center">
          <text class="text-grey1">未注册的手机号将默认开通平台账号</text>
        </view>
      </view>
    </view>

    <view class="tip-container"> 本次活动由上海柏科咨询管理有限公司提供技术支持 </view>

    <uni-popup ref="popup" background-color="#fff" :is-mask-click="false" class="pop-con">
      <view class="popup-content">
        <view>活动尚未开始 </view>
        <view>敬请期待！</view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
  import { isContactWay } from "@/utils/validate"
  import { sendCode } from "@/api/competition/index"
  import { querySysTenantInfo } from "@/api/system/login"

  export default {
    data() {
      return {
        loginForm: {
          mobile: "",
          code: ""
        },
        canCode: false,
        codeMsg: "获取验证码",
        interval: null,
        mobileHolder: "手机号码",
        codeHolder: "短信验证码",
        startTime: null,
        endTime: null
      }
    },
    computed: {
      topImageSrc() {
        return this.domainName === "unep"
          ? "/static/images/competition/logbg_2.png"
          : "/static/images/competition/yingji_home.png"
      }
    },
    created() {},
    mounted() {
      querySysTenantInfo().then(res => {
        this.startTime = res.data.activityInfo.startTime
        this.endTime = res.data.activityInfo.endTime
        if (new Date() < new Date(this.startTime)) {
          this.$refs.popup.open()
        }
      })
    },
    methods: {
      fphMobile() {
        this.mobileHolder = ""
      },
      bphMobile() {
        this.mobileHolder = "手机号码"
      },
      fphCode() {
        this.codeHolder = ""
      },
      bphCode() {
        this.codeHolder = "短信验证码"
      },
      // 判断验证码是否可点击
      checkCode(e) {
        let val = e.detail.value
        if (val && isContactWay(val)) {
          this.canCode = true
        } else {
          this.canCode = false
        }
      },
      // 获取验证码
      async getCode() {
        if (this.canCode) {
          let queryData = {
            mobile: this.loginForm.mobile,
            scene: 1
          }

          await sendCode(queryData)

          this.$modal.msgSuccess("验证码发送成功！")
          clearInterval(this.interval)
          this.canCode = false
          this.sec = 60
          this.codeMsg = this.sec + "s后重试"
          this.interval = setInterval(() => {
            this.sec -= 1
            this.codeMsg = this.sec + "s后重试"
            if (this.sec === 0) {
              this.codeMsg = "重新获取"
              this.canCode = true
              clearInterval(this.interval)
            }
          }, 1000)
        }
      },
      // 登录方法
      async handleLogin() {
        if (this.loginForm.mobile === "") {
          this.$modal.msgError("请输入您的手机号")
        } else if (this.loginForm.code === "") {
          this.$modal.msgError("请输入您的验证码")
        } else {
          this.$modal.loading("登录中，请耐心等待...")
          this.pwdLogin()
        }
      },
      // 登录
      pwdLogin() {
        this.loginForm.password = this.loginForm.code
        this.$store.dispatch("unepLogin", this.loginForm).then(() => {
          // if (new Date() > new Date(this.startTime) && new Date() < new Date(this.endTime)) {
          this.$tab.reLaunch("/pages/competition/index")
          // } else if (new Date() > new Date(this.endTime)) {
          //   this.$tab.reLaunch("/pages/competition/complete")
          // }
        })
      }
    }
  }
</script>

<style lang="scss">
  page {
    background-color: #ffffff;
  }
  .popup-content {
    /* display: flex; */
    text-align: center;
    align-items: center;
    justify-content: center;
    padding: 40px 30px;
    line-height: 40px;
    background-color: #fff;
    font-size: 20px;
    border-radius: 6px;
  }

  ::v-deep .uni-popup__wrapper {
    border-radius: 6px;
  }
  .bgcon {
    height: calc(100vh - 150px);
    background: url("/static/images/competition/logbg_3.png") no-repeat;
    background-size: 100% auto;
    background-position: 0 -60px;
  }
  .active {
    color: #888 !important;
  }
  .normal-login-container {
    width: 100%;
    height: 100vh;
    overflow: hidden;
    .logo-content {
      /*   background: url("/static/images/competition/logbg1.png") no-repeat;
      background-size: 100% auto; */
      image {
        border-radius: 4px;
      }

      .title {
        margin-top: 10px;
        font-size: 18px;
        font-weight: 600;
      }
    }

    .login-form-content {
      text-align: center;
      margin: 10px auto 10px auto;
      padding-top: 10%;
      width: 80%;

      .input-item {
        margin: 20px auto;
        background-color: #f5f6f7;
        height: 45px;
        border-radius: 20px;
        background: #fff;
        box-shadow: 0 0 10rpx rgba(0, 0, 0, 0.1);
        .icon {
          font-size: 38rpx;
          margin-left: 10px;
          color: #999;
        }

        .input {
          width: 100%;
          font-size: 16px;
          line-height: 20px;
          text-align: left;
          padding-left: 15px;
        }
      }

      .login-btn {
        margin-top: 40px;
        height: 45px;
      }

      .xieyi {
        color: #333;
        font-size: 14px;
      }

      .login-code {
        height: 38px;
        float: right;

        .login-code-img {
          height: 38px;
          position: absolute;
          margin-left: 10px;
          width: 200rpx;
        }
      }
    }
  }

  .xieyi {
    font-size: 12px;
  }

  .tip-container {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    bottom: 20rpx;
    font-size: 12px;
  }
</style>

/*
 * @Description:
 * @Author: <PERSON><PERSON><PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-09-20 17:03:20
 * @LastEditTime: 2024-02-04 10:48:02
 */
import request from "@/utils/request"
// 发送验证码
export function sendCode(data) {
  return request({
    url: "/auth/send-sms-code",
    method: "post",
    data: data
  })
}

// unep登录
export function handleUnepLogin(data) {
  return request({
    url: "/auth/sms-login",
    headers: {
      isToken: false
    },
    method: "post",
    data: data
  })
}

// 课程列表
export function unepList(params) {
  return request({
    url: "/course/base/unepList",
    method: "get",
    params
  })
}

// 获取积分排行
export function getUnepStudyRank(params) {
  return request({
    url: "/system/user/getUnepStudyRank",
    method: "get",
    params
  })
}

// 查询已考完的试卷详情
export function completedPaperDetail(data) {
  return request({
    url: "/exam/paper-answer/completedPaperDeta<PERSON>",
    method: "put",
    data
  })
}
// 获取学习数
export function getStudyCount(params) {
  return request({
    url: "/course/passage/getStudyCount",
    method: "get",
    params
  })
}

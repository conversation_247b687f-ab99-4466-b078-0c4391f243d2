<template>
  <view class="min-h-screen bg-gray-50">
    <navigationBar path="/pages/index" />

    <!-- 空状态 -->
    <xw-empty :isShow="!newsListData.length" text="暂无新闻咨询" textColor="#777777"></xw-empty>

    <!-- 新闻列表 -->
    <view v-show="newsListData.length" class="px-4 py-2">
      <!-- 头部横幅 -->
      <view class="bg-white rounded-lg shadow-sm overflow-hidden mb-4">
        <view
          class="relative w-full aspect-[10/3] bg-center bg-contain bg-no-repeat"
          :style="{
            backgroundImage: 'url(https://img.traingo.cn/data/home/<USER>'
          }"
        >
          <view
            class="absolute bottom-2 right-3 bg-white/80 backdrop-blur px-6 py-1.5 rounded-full text-sm"
          >
            社会热点 ▶
          </view>
        </view>
      </view>

      <!-- 新闻列表 -->
      <view
        v-for="item in newsListData"
        :key="item.newsId"
        class="bg-white rounded-lg shadow-sm mb-4 p-4"
      >
        <!-- 标题 -->
        <view class="relative pl-8 mb-3">
          <view class="absolute left-0 top-0 w-2 h-full bg-red-500 rounded"></view>
          <view class="text-lg font-bold text-gray-800">{{ item.newsTitle }}</view>
        </view>

        <!-- 内容 -->
        <view class="text-gray-600 leading-relaxed break-words">
          {{ item.newsContent }}
        </view>

        <!-- 分隔线 -->
        <o-divider lineColor="#d7d7d7" textColor="#c4c4c6" margin="30rpx">
          <view class="w-2 h-2 rounded-full bg-red-500"></view>
        </o-divider>
      </view>
    </view>
  </view>
</template>

<script>
  import { list } from "@/api/devops/news.js"

  export default {
    data() {
      return {
        pageNum: 1,
        pageSize: 5,
        pageType: 1,
        dataTotal: 0,
        newsListData: [],
        status: "loadmore",
        loaded: false
      }
    },

    onShow() {
      // 重置数据
      this.resetData()
      this.fetchData()
    },

    onReachBottom() {
      if (this.newsListData.length >= this.dataTotal) {
        this.status = "noMore"
        return
      }

      this.pageNum++
      this.status = "loading"
      this.fetchData()
    },

    methods: {
      // 重置数据
      resetData() {
        this.pageNum = 1
        this.newsListData = []
        this.dataTotal = 0
        this.status = "loadmore"
      },

      // 获取新闻列表数据
      async fetchData() {
        try {
          const params = {
            pageNum: this.pageNum,
            pageSize: this.pageSize,
            dataType: this.pageType
          }

          const { rows, total } = await list(params)
          this.dataTotal = total

          // 第一页直接赋值，其他页追加
          if (this.pageNum === 1) {
            this.newsListData = rows || []
          } else {
            this.newsListData = [...this.newsListData, ...(rows || [])]
          }

          // 更新加载状态
          this.status = this.newsListData.length >= total ? "noMore" : "loadmore"
          this.loaded = true
        } catch (error) {
          console.error("获取新闻列表失败:", error)
          uni.showToast({
            title: "获取新闻列表失败",
            icon: "none"
          })
        }
      }
    }
  }
</script>

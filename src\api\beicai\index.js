/*
 * @Description: 北蔡防灾减灾相关API
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-02-28 14:17:33
 * @LastEditTime: 2024-03-05 16:42:01
 */
import request from "@/utils/request"
//--------------------------------------- 居委 -------------------------------------------

// 社区学习情况
// queryTimeFrom queryTimeTo
export function towncommDist(params) {
  return request({
    url: "/course/study-log/commDist",
    method: "get",
    params
  })
}

// 居委学习情况
export function towncountryDist(params) {
  return request({
    url: "/course/study-log/countryDist",
    method: "get",
    params
  })
}

// 居委学习排名
export function getTownCommDistRank(params) {
  return request({
    url: "/system/dept/getTownCommDistRank",
    method: "get",
    params
  })
}

// 数据一览
export function townDashboard() {
  return request({
    url: "/course/task/townDashboard",
    method: "get"
  })
}

//--------------------------------------- 居民 -------------------------------------------

//数据一览
export function residentDashboard(params) {
  return request({
    url: "/reportforms/report/townDashboard",
    method: "get",
    params
  })
}

//社区学习情况
export function commResident(params) {
  return request({
    url: "/reportforms/report/comm-resident",
    method: "get",
    params
  })
}

//居民学习情况
export function countryResident(params) {
  return request({
    url: "/reportforms/report/country-resident",
    method: "get",
    params
  })
}

//学习排名
export function getCountryResidentRank(params) {
  return request({
    url: "/reportforms/report/getCountryResidentRank",
    method: "get",
    params
  })
}

//--------------------------------------- 商铺 -------------------------------------------

//社区下属商铺学习情况
export function commShops(params) {
  return request({
    url: "/reportforms/report/comm-shops",
    method: "get",
    params
  })
}

//居委下属商铺学习情况
export function countryShops(params) {
  return request({
    url: "/reportforms/report/country-shops",
    method: "get",
    params
  })
}

//学习排名
export function getCountryShopsRank(params) {
  return request({
    url: "/reportforms/report/getCountryShopsRank",
    method: "get",
    params
  })
}

// 扫描登录的课程接口
export function townCourseList(params) {
  return request({
    url: "/course/base/townList",
    method: "get",
    params
  })
}

// 非首次登录用户通过手机号获取个人信息
export function getInfoByMobile(params) {
  return request({
    url: "/system/user/getInfoByMobile",
    method: "get",
    params
  })
}

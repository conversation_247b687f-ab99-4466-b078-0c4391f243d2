/*
 * @Description:
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-11-26 16:01:01
 * @LastEditTime: 2024-12-18 14:40:41
 */
import request from "@/utils/request"

// 培训课程统计
export function getTrainingCourseList(params) {
  return request({
    url: "/reportforms/report/course-list",
    method: "get",
    params
  })
}
// 培训考试统计
export function getTrainingExamList(params) {
  return request({
    url: "/reportforms/report/exam-list",
    method: "get",
    params
  })
}
// 培训问卷统计
export function getTrainingQuestionnaireList(params) {
  return request({
    url: "/reportforms/report/questionnaire-list",
    method: "get",
    params
  })
}

// 通过 ID 获取任务详情
export function getTaskInfoById(taskId, params) {
  return request({
    url: "/course/task/" + taskId,
    method: "get",
    params
  })
}

// 我的培训任务列表
export function fetchTaskMyList(query) {
  return request({
    url: "/course/task/myList",
    method: "get",
    params: query
  })
}

// 通过 ID 获取项目信息
export function getTaskById(taskId, params) {
  return request({
    url: "/course/task/taskDetail/" + taskId,
    method: "get",
    params
  })
}

// 最近学习
export function getRecentStudy(params) {
  return request({
    url: "/reportforms/report/getRecentLearning",
    method: "get",
    params
  })
}

// 我的轨迹
export function studyLogList(params) {
  return request({
    url: "/course/study-log/list",
    method: "get",
    params
  })
}

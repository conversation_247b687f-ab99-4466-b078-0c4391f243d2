<!--
 * @Description: 学习tab
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-06-25 17:09:38
 * @LastEditTime: 2025-03-04 14:13:37
-->

<template>
  <view class="min-h-screen bg-[#f8f8f8]">
    <RecentStudyPopup />
    <GlobalHeader />

    <template v-if="taskList.length">
      <!-- 温馨提示横幅 -->
      <view
        v-if="!isExist"
        class="flex items-center gap-2 mx-2 my-2 p-2 bg-orange-50 rounded-md text-14px text-orange-500"
      >
        <text class="i-material-symbols-info"></text>
        <text>{{ tipContent }}</text>
      </view>

      <!-- 培训任务横向滚动列表 -->
      <scroll-view scroll-x class="whitespace-nowrap px-2 my-2">
        <view
          v-for="(item, index) in taskList"
          :key="item.taskId"
          class="inline-block mr-2 p-4 bg-[#1e91fc] text-white rounded-lg min-w-150px"
          :class="{ 'bg-opacity-60': currentTaskId !== item.taskId }"
          @click="switchTask(item)"
        >
          <view class="text-16px truncate h-20px">{{ item.taskName }}</view>
          <view class="text-12px mt-2">截止日期：{{ formatDate(item.endTime) }}</view>
        </view>
      </scroll-view>

      <!-- 进度展示区域 -->
      <view class="flex mx-2 mt-4 bg-white rounded-lg p-3">
        <!-- 总进度 -->
        <view class="flex-1 flex items-center justify-center border-r border-gray-100">
          <view class="flex flex-col items-center">
            <view class="text-14px font-medium text-[#333333] mb-1">总进度</view>
            <view class="w-25 h-25">
              <qiun-data-charts
                type="arcbar"
                :opts="totalArcOpts"
                :chartData="totalProgressData"
                :canvas2d="true"
                canvasId="totalArcId"
              />
            </view>
            <view class="text-14px text-gray-600 mt-1">
              已学完： <text class="font-bold mr-1">{{ completedCount }}</text> / {{ totalCount }}
            </view>
          </view>
        </view>
        <!-- 今日进度 -->
        <view class="flex-1 flex items-center justify-center">
          <view class="flex flex-col items-center">
            <view class="text-14px font-medium text-[#333333] mb-1">今日进度</view>
            <view class="w-25 h-25">
              <qiun-data-charts
                type="arcbar"
                :opts="arcOpts"
                :chartData="todayProgressData"
                :canvas2d="true"
                canvasId="todayArcId"
              />
            </view>
            <view class="text-14px text-gray-600 mt-1">
              今日完成：<text class="font-bold">{{ formatStudyTime(todayStudyTime) }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 学习安排列表 -->
      <view class="m-2 bg-white rounded-lg p-3">
        <view class="text-16px font-medium mb-3 text-[#333333]">学习安排</view>
        <study-arrangement :study-list="studyList" :current-task-id="String(currentTaskId)" />
      </view>
    </template>
    <xw-empty :isShow="!taskList.length" text="暂无学习任务" textColor="#777777" />
  </view>
</template>

<script>
  import { fetchTaskMyList, getTaskById } from "@/api/trainingTask"
  import { getStudyTime } from "@/api/statistics"
  import xwEmpty from "@/components/xw-empty/xw-empty"
  import GlobalHeader from "@/components/GlobalHeader/index.vue"
  import StudyArrangement from "@/components/StudyArrangement/index.vue"
  import RecentStudyPopup from "@/components/RecentStudyPopup/index.vue"
  import dayjs from "dayjs"

  export default {
    components: {
      xwEmpty,
      GlobalHeader,
      StudyArrangement,
      RecentStudyPopup
    },

    data() {
      return {
        taskList: [], // 培训任务列表
        currentTaskId: "", // 当前选中的任务ID
        studyList: [], // 学习安排列表
        todayStudyTime: 0, // 今日学习时长
        tipContent: "", // 温馨提示内容
        completedCount: 0, // 已完成数量
        totalCount: 0, // 总数量
        totalArcOpts: {
          type: "arcbar",
          width: 60,
          height: 60,
          animation: true,
          title: {
            name: "0%",
            color: "#25b692",
            fontSize: 16,
            offsetY: -2
          },
          subtitle: {
            show: false
          },
          extra: {
            arcbar: {
              type: "default",
              width: 4,
              backgroundColor: "#F6F6F6",
              startAngle: 0.75,
              endAngle: 0.25,
              gap: 2,
              linearType: "none",
              color: "#25b692",
              max: 1
            }
          },
          legend: {
            show: false
          },
          background: "#FFFFFF",
          padding: [0, 0, 0, 0],
          enableScroll: false
        },
        arcOpts: {
          type: "arcbar",
          width: 60,
          height: 60,
          title: {
            name: "0分",
            color: "#fb923c",
            fontSize: 16,
            offsetY: -2
          },
          subtitle: {
            show: false
          },
          extra: {
            arcbar: {
              type: "default",
              width: 4,
              backgroundColor: "#F6F6F6",
              startAngle: 0.75,
              endAngle: 0.25,
              gap: 2,
              linearType: "none",
              color: "#fb923c",
              max: 100
            }
          },
          legend: {
            show: false
          },
          background: "#FFFFFF",
          padding: [0, 0, 0, 0],
          enableScroll: false
        },
        totalProgressData: {},
        todayProgressData: {},
        isExist: false // 是否存在过期项
      }
    },

    onShow() {
      this.fetchTaskList()
    },

    methods: {
      formatDate(date) {
        return date ? dayjs(date).format("YYYY-MM-DD") : "--"
      },

      formatStudyTime(seconds) {
        if (!seconds) return "0分钟"
        if (seconds < 60) return "1分钟"
        const hours = Math.floor(seconds / 3600)
        const minutes = Math.floor((seconds % 3600) / 60)
        if (hours > 0) {
          return `${hours}小时${minutes > 0 ? minutes + "分钟" : ""}`
        }
        return `${minutes}分钟`
      },

      // 获取培训任务列表
      async fetchTaskList() {
        try {
          const queryData = {
            completionStatus: "0,1",
            pageSize: 999
          }
          const { rows } = await fetchTaskMyList(queryData)
          this.taskList = rows
          if (rows.length > 0) {
            this.switchTask(rows[0])
          } else {
            this.updateTotalProgress(0)
            this.fetchStudyTime()
          }

          // 处理过期提示
          const { showWarning, warningMessage } = this.handleExpireWarnings(rows)
          this.isExist = !showWarning
          this.tipContent = warningMessage
        } catch (error) {
          console.error("获取培训任务列表失败:", error)
        }
      },

      // 获取学习时长
      async fetchStudyTime() {
        try {
          if (!this.currentTaskId) return
          const res = await getStudyTime({ taskId: this.currentTaskId, onlyToday: true })
          this.todayStudyTime = res.data || 0
          // 更新今日进度图表数据
          this.updateTodayProgress()
        } catch (error) {
          console.error("获取学习时长失败:", error)
        }
      },

      // 切换培训任务
      async switchTask(task) {
        this.currentTaskId = task.taskId
        // 获取任务详情
        try {
          const { rows } = await getTaskById(this.currentTaskId, {
            pageSize: 999
          })
          // 获取任务看板数据
          this.studyList = this.formatStudyList(rows)

          // 更新总进度
          const progress = (task.rateLearning || 0) * 100
          this.totalCount = task.subTaskCount || 0
          this.completedCount = Math.round((progress * this.totalCount) / 100)
          this.updateTotalProgress(progress)

          // 获取今日学习时长
          this.fetchStudyTime()
        } catch (error) {
          console.error("获取任务详情失败:", error)
        }
      },

      // 格式化学习列表数据
      formatStudyList(data) {
        if (!data) return []

        return data.map(item => {
          const type = this.getItemType(item)
          return {
            ...item,
            id: item.fieldId,
            type,
            name: item.courseName || item.questionnaireName || item.manageName || item.examName,
            endTime: item.endTime,
            progress: item.rateLearning ? item.rateLearning * 100 : 0,
            status: item.rateLearning >= 1 ? "completed" : "ongoing"
          }
        })
      },

      // 获取项目类型
      getItemType(item) {
        if (item.fieldType === "0") return "course"
        if (item.fieldType === "1") return "exam"
        if (item.fieldType === "2") return "questionnaire"
        if (item.fieldType === "3") return "resource"
        return "course"
      },

      // 更新总进度图表
      updateTotalProgress(progress) {
        // 将百分比转换为0-1之间的小数
        const value = Math.min(Math.max(progress, 0), 100) / 100
        this.$nextTick(() => {
          this.totalProgressData = {
            series: [
              {
                name: "总进度",
                data: value,
                color: "#25b692"
              }
            ]
          }
          this.totalArcOpts = {
            ...this.totalArcOpts,
            title: {
              ...this.totalArcOpts.title,
              name: `${Math.floor(progress)}%`
            },
            extra: {
              ...this.totalArcOpts.extra,
              arcbar: {
                ...this.totalArcOpts.extra.arcbar,
                max: 1 // 修改最大值为1
              }
            }
          }
        })
      },

      // 更新今日进度图表
      updateTodayProgress() {
        this.$nextTick(() => {
          this.todayProgressData = {
            series: [
              {
                name: "今日进度",
                data: this.todayStudyTime ? 1 : 0,
                color: "#fb923c"
              }
            ]
          }
          this.arcOpts = {
            ...this.arcOpts,
            title: {
              ...this.arcOpts.title,
              name: this.todayStudyTime ? "100%" : "0%"
            },
            extra: {
              ...this.arcOpts.extra,
              arcbar: {
                ...this.arcOpts.extra.arcbar,
                max: 1 // 修改最大值为1
              }
            }
          }
        })
      },

      // 处理过期提示信息
      handleExpireWarnings(valList) {
        // 检查是否所有任务都没有过期项
        const hasExpiredTasks = valList.some(task => task.expireList?.length > 0)

        if (!hasExpiredTasks) {
          return {
            showWarning: false,
            warningMessage: ""
          }
        }

        // 生成过期提示信息
        const expireMessages = valList
          .filter(task => task.expireList?.length > 0)
          .map(task => {
            const expireItems = task.expireList
              .map(item => {
                const daysText =
                  item.daysUntilExpire >= 0
                    ? `还有${item.daysUntilExpire}天过期`
                    : `已超期${Math.abs(item.daysUntilExpire)}天`
                return `${item.name}${daysText}`
              })
              .join("、")

            return `您的${task.taskName}任务下的${expireItems}`
          })
          .join("，")

        return {
          showWarning: true,
          warningMessage: expireMessages
        }
      }
    }
  }
</script>

<style lang="scss" scoped>
  ::-webkit-scrollbar {
    display: none;
    width: 0;
    height: 0;
    color: transparent;
  }
</style>

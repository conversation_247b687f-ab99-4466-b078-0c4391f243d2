<!--
 * @Description: 知识普及、历年回顾列表页面
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-04-15 14:36:29
 * @LastEditTime: 2024-07-19 13:40:06
-->
<template>
  <view
    class="manage-container"
    :style="`background-image: url('${assetsPrefixUrl}fzjz/fzjz-bg.jpg');`"
  >
    <view class="iconfont icon-fanhui icon" @click="back"></view>
    <view class="page-title">【{{ flag === "knowledge" ? "知识图文" : "历年回顾" }}】</view>
    <view class="manage-search">
      <uni-search-bar
        v-model="searchValue"
        class="searchBar"
        placeholder="请输入名称"
        radius="8"
        clearButton="none"
        cancelButton="none"
        @confirm="fetchData"
      />
      <view class="searchButton iconfont icon-Search icon" @click="fetchData"></view>
    </view>

    <!-- <view class="knowledge-dropdown">
      <zb-dropdown-menu ref="dropDownMenuRef" style="width: 100%">
        <zb-dropdown-item
          ref="dropDownItemRef"
          name="one"
          :options="catalogueTableData"
          v-model="catalogueId"
          @change="fetchData"
        ></zb-dropdown-item>
      </zb-dropdown-menu>
    </view> -->
    <view v-show="manageList.length" class="manage-list">
      <view
        class="manage-list-content"
        v-for="item in manageList"
        :key="item.manageId"
        @click="jumpTo(item)"
      >
        <view class="img-con">
          <image class="manage-list-content-img" :src="item.cover || item.manageAddress"></image>
        </view>
      </view>
    </view>
    <xw-empty :isShow="!manageList.length" text="暂无数据" textColor="#777777"></xw-empty>
  </view>
</template>

<script>
  import { listManage } from "@/api/course/manage"
  import { catalogueType, assetsPrefixUrl } from "@/utils/constant.js"
  import { getCatalogueList } from "@/api/system/catalogue.js"
  import { mapGetters } from "vuex"

  export default {
    onLoad(option) {
      this.flag = option.flag
      this.catalogueId = option.catalogueId
        ? Number(option.catalogueId)
        : this.flag === "knowledge"
        ? 2176
        : 2169
      this.fetchData("onLoad")
      this.fetchCatalogueData()
    },
    onPullDownRefresh() {
      this.fetchData()
      uni.stopPullDownRefresh()
    },
    onReachBottom() {
      if (this.manageList.length !== this.total) {
        this.pageNum += 1
        this.fetchData("onReachBottom")
      }
    },
    onPageScroll(e) {
      if (this.flag === "knowledge" && this.$refs.dropDownItemRef.showWrap) {
        this.$refs.dropDownMenuRef.close()
      }
    },

    data() {
      return {
        assetsPrefixUrl,
        searchValue: "",
        pageNum: 1,
        pageSize: 6,
        total: 0,
        manageList: [],
        catalogueId: undefined,
        catalogueTableData: [],
        flag: ""
      }
    },
    computed: {
      ...mapGetters({
        domainName: "domainName"
      })
    },

    methods: {
      async fetchData(flag) {
        if (flag !== "onReachBottom") {
          this.pageNum = 1
        }
        let queryData = {
          pageSize: this.pageSize,
          pageNum: this.pageNum,
          sortField: "manage_id",
          sortOrder: "asc",
          catalogueId: this.catalogueId,
          manageName: this.searchValue
        }
        const res = await listManage(queryData)
        this.total = res.total
        this.manageList = flag === "onReachBottom" ? this.manageList.concat(res.rows) : res.rows
      },

      // 跳转详情·
      jumpTo(item) {
        uni.navigateTo({
          url: `/pages/fzjz/detail?flag=${this.flag}&detailInfo=${encodeURIComponent(
            JSON.stringify(item)
          )}&catalogueId=${this.catalogueId}`
        })
      },
      async fetchCatalogueData() {
        getCatalogueList({
          catalogueType: catalogueType.MEANS_CATALOGUE,
          parentId: this.flag === "knowledge" ? 2176 : 2169
        }).then(response => {
          this.catalogueTableData = response.rows.map(({ catalogueId, catalogueName }) => ({
            text: catalogueName,
            value: catalogueId
          }))
          this.catalogueTableData.unshift({
            text: "全部目录",
            value: this.flag === "knowledge" ? 2176 : 2169
          })
        })
      },
      back() {
        uni.navigateTo({
          url: "/pages/fzjz/index"
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
  ::v-deep .uni-searchbar__box-icon-search {
    display: none;
  }
  ::v-deep .uni-searchbar__box {
    justify-content: unset;
    padding: 0 20rpx;
    border: 1px solid #ccc;
  }
  ::v-deep .uni-searchbar__text-placeholder {
    font-size: 30rpx;
  }
  ::v-deep .zb-dropdown-menu__bar {
    background-color: #f0f1f5;
    box-shadow: none;
  }

  ::v-deep .zb-cell {
    background-color: #f0f1f5;
  }
  .manage-container {
    position: relative;
    background-repeat: no-repeat;
    background-size: 100% auto;
    background-position: top;
    height: 100vh;
    padding-top: 46%;
    background-color: #f0f1f5;
    .icon-fanhui {
      position: absolute;
      left: 25rpx;
      top: 260rpx;
      font-weight: bold;
      font-size: 40rpx;
      color: #4a4c47;
    }

    .page-title {
      color: #2d9188;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 32rpx;
      font-weight: bold;
    }
    .manage-search {
      padding: 0 60rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      .searchBar {
        width: 100%;
      }
      .searchButton {
        position: absolute;
        right: 90rpx;
        color: #b3b3b3;
        font-size: 45rpx;
      }
    }
    .manage-list {
      background-color: #f0f1f5;
      padding: 28rpx 50rpx;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      .manage-list-content {
        width: 45%;
        margin-bottom: 80rpx;
        border-radius: 10rpx;
        overflow: hidden;

        .img-con {
          position: relative;
          width: 100%;
        }
      }
    }
  }
</style>

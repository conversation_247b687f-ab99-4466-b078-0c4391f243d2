<!--
 * @Description: 考后分析页面
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-10-12 09:02:22
 * @LastEditTime: 2025-06-17 17:16:16
-->
<template>
  <view class="flex flex-col min-h-screen bg-gray-100">
    <!-- 导航栏 -->
    <navigationBar
      :path="`/pages/mine/exam/index?showNavBar=${showNavBar}`"
      :showPersonal="false"
    />

    <!-- 题目内容区域 -->
    <view class="flex-1 px-4 pb-4 mt-4" v-if="examQuestionList[examIndex]">
      <view class="bg-white rounded-lg shadow-md p-4">
        <!-- 顶部信息栏 -->
        <view class="flex items-center justify-between mb-4">
          <view class="flex items-center gap-2" @click="showList">
            <view
              class="i-material-symbols:format-list-numbered text-xl text-gray-600 cursor-pointer"
            ></view>
            <view class="flex items-center gap-2">
              <text class="text-base">第</text>
              <text class="text-blue-500 text-base">{{ examIndex + 1 }}</text>
              <text class="text-base"> / {{ examQuestionList.length }} 题</text>
            </view>
          </view>
        </view>
        <!-- 题目内容 -->
        <view class="mb-6">
          <text
            class="text-sm px-2 py-0.5 rounded w-150px mr-1"
            :class="{
              'bg-green-100 text-green-600': examQuestionList[examIndex].questionType === 'S',
              'bg-orange-100 text-orange-600': examQuestionList[examIndex].questionType === 'M',
              'bg-[#f5e6d3] text-[#7b4d12]': examQuestionList[examIndex].questionType === 'J',
              'bg-purple-100 text-[#8400ff]': examQuestionList[examIndex].questionType === 'K',
              'bg-blue-100 text-blue-600': examQuestionList[examIndex].questionType === 'Q'
            }"
          >
            {{ questionType[examQuestionList[examIndex].questionType] }}
          </text>
          <text class="text-base text-gray-700 break-words whitespace-pre-wrap leading-relaxed block">{{
            examQuestionList[examIndex].questionName
          }}</text>
        </view>

        <!-- 单选/判断题选项 -->
        <view
          v-if="
            examQuestionList[examIndex].questionType === 'S' ||
            examQuestionList[examIndex].questionType === 'J'
          "
        >
          <view
            v-for="choice in 11"
            v-if="examQuestionList[examIndex][`item${choice}`]"
            :key="choice"
            class="flex items-center gap-2 p-3"
          >
            <!-- 选项标记 -->
            <view class="flex-shrink-0 option-mark">
              <view
                class="w-24px h-24px leading-5 text-center rounded-full flex items-center justify-center"
                :class="{
                  'correct-answer-border': isCorrectAnswer(
                    examQuestionList[examIndex],
                    String.fromCharCode(64 + choice)
                  ),
                  'border border-gray-300': !isCorrectAnswer(
                    examQuestionList[examIndex],
                    String.fromCharCode(64 + choice)
                  )
                }"
              >
                <template
                  v-if="examQuestionList[examIndex].userAnswer === String.fromCharCode(64 + choice)"
                >
                  <image
                    v-if="
                      isCorrectAnswer(examQuestionList[examIndex], String.fromCharCode(64 + choice))
                    "
                    class="w-27px h-27px"
                    src="/static/images/competition/correct.png"
                    mode="aspectFit"
                  />
                  <image
                    v-else
                    class="w-27px h-27px"
                    src="/static/images/competition/error.png"
                    mode="aspectFit"
                  />
                </template>
                <text v-else>{{ String.fromCharCode(64 + choice) }}</text>
              </view>
            </view>
            <!-- 选项内容 -->
            <text class="flex-1 break-words whitespace-pre-wrap leading-relaxed">{{ examQuestionList[examIndex][`item${choice}`] }}</text>
          </view>
        </view>

        <!-- 多选题选项 -->
        <view v-else-if="examQuestionList[examIndex].questionType === 'M'">
          <view
            v-for="multiChoice in 11"
            v-if="examQuestionList[examIndex][`item${multiChoice}`]"
            :key="multiChoice"
            class="flex items-center gap-2 p-3"
          >
            <!-- 选项标记 -->
            <view class="flex-shrink-0 option-mark">
              <view
                class="w-24px h-24px leading-5 text-center rounded-full flex items-center justify-center"
                :class="{
                  'correct-answer-border':
                    examQuestionList[examIndex].answer &&
                    examQuestionList[examIndex].answer.includes(
                      String.fromCharCode(64 + multiChoice)
                    ),
                  'border border-gray-300':
                    examQuestionList[examIndex].answer &&
                    !examQuestionList[examIndex].answer.includes(
                      String.fromCharCode(64 + multiChoice)
                    )
                }"
              >
                <template
                  v-if="
                    examQuestionList[examIndex].userAnswer &&
                    examQuestionList[examIndex].userAnswer.includes(
                      String.fromCharCode(64 + multiChoice)
                    )
                  "
                >
                  <image
                    v-if="
                      isCorrectMultiAnswer(
                        examQuestionList[examIndex],
                        String.fromCharCode(64 + multiChoice)
                      )
                    "
                    class="w-27px h-27px"
                    src="/static/images/competition/correct.png"
                    mode="aspectFit"
                  />
                  <image
                    v-else
                    class="w-27px h-27px"
                    src="/static/images/competition/error.png"
                    mode="aspectFit"
                  />
                </template>
                <text v-else>{{ String.fromCharCode(64 + multiChoice) }}</text>
              </view>
            </view>
            <!-- 选项内容 -->
            <text class="flex-1 break-words whitespace-pre-wrap leading-relaxed">{{ examQuestionList[examIndex][`item${multiChoice}`] }}</text>
          </view>
        </view>

        <!-- 简答题 -->
        <view v-else-if="examQuestionList[examIndex].questionType === 'Q'" class="space-y-4">
          <view class="p-3 bg-gray-50 rounded-lg">
            <text class="font-bold text-gray-700 text-base block mb-2">你的答案：</text>
            <text class="text-gray-600 text-base leading-relaxed break-words whitespace-pre-wrap">
              {{ examQuestionList[examIndex].userAnswer || "未作答" }}
            </text>
          </view>
          <view class="p-3 bg-gray-50 rounded-lg">
            <text class="font-bold text-gray-700 text-base block mb-2">参考答案：</text>
            <text class="text-gray-600 text-base leading-relaxed break-words whitespace-pre-wrap">
              {{ examQuestionList[examIndex].answer || "暂无参考答案" }}
            </text>
          </view>
        </view>

        <!-- 答题结果提示 -->
        <view
          class="mt-4 font-bold text-18px"
          :class="{
            ' text-green-600': isAnswerCorrect,
            ' text-red-600': !isAnswerCorrect
          }"
        >
          {{ isAnswerCorrect ? "回答正确" : "回答错误" }}
        </view>

        <!-- 答案和解析 -->
        <view class="mt-4 space-y-3">
          <view class="p-3 bg-gray-50 rounded-lg" v-if="!isAnswerCorrect">
            <text class="font-bold text-gray-700">你的答案：</text>
            <text class="text-gray-600">
              {{ examQuestionList[examIndex].userAnswer || "未选择" }}
            </text>
          </view>
          <view class="p-3 bg-gray-50 rounded-lg">
            <text class="font-bold text-gray-700">正确答案：</text>
            <text class="text-gray-600">{{ examQuestionList[examIndex].answer }}</text>
          </view>
          <!-- <view class="p-3 bg-gray-50 rounded-lg">
            <text class="font-bold text-gray-700">解析：</text>
            <text class="text-gray-600 break-words whitespace-pre-wrap leading-relaxed">{{
              examQuestionList[examIndex].analysis || "暂无解析"
            }}</text>
          </view> -->
        </view>
      </view>
    </view>

    <!-- 底部操作栏 -->
    <view class="flex justify-between items-center px-4 py-3 bg-white border-t border-gray-200">
      <button
        v-if="examIndex !== 0"
        class="flex-1 mx-2 py-2 rounded-full bg-gray-500 text-white"
        @click="prevQuestion"
      >
        上一题
      </button>
      <button
        v-if="examQuestionList.length !== examIndex + 1"
        class="flex-1 mx-2 py-2 rounded-full bg-orange-500 text-white"
        @click="nextQuestion"
      >
        下一题
      </button>
    </view>

    <!-- 题目列表弹窗 -->
    <uni-popup ref="popup" background-color="#fff" @maskClick="maskClick">
      <view class="bg-white w-full max-h-[70vh] rounded-t-lg p-4">
        <view class="flex justify-between items-center mb-4">
          <text class="text-sm text-gray-600">共{{ examQuestionList.length }}题</text>
        </view>
        <scroll-view scroll-y="true" class="max-h-[calc(70vh-4rem)]">
          <view class="grid grid-cols-7 gap-3 px-2">
            <view
              v-for="(item, index) in examQuestionList"
              :key="index"
              class="h-9 w-9 flex items-center justify-center rounded-md cursor-pointer transition-colors text-[15px]"
              :class="['bg-blue-500 text-white hover:bg-blue-400 hover:text-white']"
              @click="changeIndex(index)"
            >
              {{ index + 1 }}
            </view>
          </view>
        </scroll-view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
  import { completedPaperDetail } from "@/api/competition/index"
  import { mapGetters } from "vuex"
  import { noNeedExamScoreDomainList } from "@/utils/constant"

  export default {
    data() {
      return {
        noNeedExamScoreDomainList,
        examIndex: 0,
        examQuestionList: [],
        questionType: {
          S: "单选",
          M: "多选",
          J: "判断",
          K: "填空",
          Q: "简答"
        },
        showNavBar: "",
        specialTenantList: []
      }
    },
    computed: {
      ...mapGetters({
        domainName: "domainName"
      }),
      isAnswerCorrect() {
        const currentQuestion = this.examQuestionList[this.examIndex]
        if (!currentQuestion) return false

        if (currentQuestion.questionType === "M") {
          // 多选题答案比较
          const userAnswerArray = currentQuestion.userAnswer?.split(",").sort()
          const correctAnswerArray = currentQuestion.answer?.split(",").sort()
          return JSON.stringify(userAnswerArray) === JSON.stringify(correctAnswerArray)
        } else if (currentQuestion.questionType === "Q") {
          // 简答题暂时不做自动判断，显示为正确（实际需要人工评分）
          return true
        } else {
          // 单选、判断题答案比较
          return currentQuestion.userAnswer === currentQuestion.answer
        }
      }
    },

    async onLoad(option) {
      if (option.showNavBar == "false") {
        this.showNavBar = option.showNavBar
      }
      const res = await completedPaperDetail({
        baseId: option.baseId,
        paperAnswerId: option.paperAnswerId,
        arrangeId: option.arrangeId
      })
      const topicList = res.data.paperTactics
      const quesList = []
      for (let item of topicList) {
        for (let mitem of item.examQuestions) {
          quesList.push(mitem)
        }
      }
      this.examQuestionList = quesList
      this.examQuestionList.forEach(item => {
        if (item.questionType === "F") {
          item.fillAnswerList = []
          if (item.userAnswer) {
            item.fillAnswerList = item.userAnswer.split("::") || []
          }
        }
      })
    },
    methods: {
      showList() {
        this.$refs.popup.open("top")
      },
      maskClick() {
        this.$refs.popup.close()
      },
      changeIndex(index) {
        this.$refs.popup.close()
        this.examIndex = index
      },
      prevQuestion() {
        if (this.examIndex > 0) {
          this.examIndex--
        }
      },
      nextQuestion() {
        if (this.examIndex < this.examQuestionList.length - 1) {
          this.examIndex++
        }
      },
      // 单选题判断方法
      isCorrectAnswer(item, choice) {
        return item.answer === choice
      },
      isWrongAnswer(item, choice) {
        return item.userAnswer === choice && item.userAnswer !== item.answer
      },

      // 多选题判断方法
      isCorrectMultiAnswer(item, choice) {
        return item.userAnswer?.includes(choice) && item.answer.includes(choice)
      },
      isWrongMultiAnswer(item, choice) {
        return item.userAnswer?.includes(choice) && !item.answer.includes(choice)
      },
      isMissedAnswer(item, choice) {
        return !item.userAnswer?.includes(choice) && item.answer.includes(choice)
      }
    }
  }
</script>

<style scoped>
  page {
    background-color: #f3f4f6;
  }

  /* 题目列表样式优化 */
  .grid-cols-7 {
    grid-template-columns: repeat(7, minmax(0, 1fr));
    justify-items: center;
  }

  /* 确保弹窗内容居中 */
  ::v-deep .uni-popup .uni-popup__wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* 修复滚动区域样式 */
  ::v-deep .uni-popup .uni-scroll-view {
    width: 100%;
  }
  ::v-deep .text-base {
    > span {
      > br {
        display: none;
      }
    }
  }
  ::v-deep .option-mark {
    uni-text {
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  /* 移除按钮内文字的左边距 */
  button {
    padding: 0;
  }
  button > text {
    margin: 0;
  }

  /* 选项图标样式 */
  .w-5 {
    width: 1.25rem;
  }
  .h-5 {
    height: 1.25rem;
  }
  .leading-5 {
    line-height: 1.25rem;
  }

  /* 正确答案边框样式 */
  .correct-answer-border {
    position: relative;
    &::before {
      content: "";
      position: absolute;
      inset: -4px;
      border-radius: 50%;
      background: conic-gradient(
        from -120deg,
        rgba(3, 173, 99, 1) 0%,
        rgba(3, 173, 99, 0.8) 40%,
        rgba(3, 173, 99, 0.6) 60%,
        rgba(3, 173, 99, 0.1) 80%,
        rgba(3, 173, 99, 0) 100%
      );
      z-index: -1;
    }
    &::after {
      content: "";
      position: absolute;
      inset: 0px;
      border-radius: 50%;
      background: white;
      z-index: -1;
    }
  }

  /* 选项标记容器基础样式 */
  .option-mark {
    position: relative;
    margin: 0 4px;
    .w-24px {
      background-color: white;
      border: 1px solid #e5e7eb;
      z-index: 1;
    }
  }
</style>

<template>
  <view class="course">
    <view class="course-search">
      <uni-search-bar
        v-model="searchValue"
        class="searchBar"
        radius="50"
        placeholder="搜索您想要的"
        clearButton="none"
        cancelButton="none"
        @confirm="getCourseData(false)"
      />
      <view class="searchButton" @click="getCourseData(false)">搜索</view>
    </view>
    <view class="course-tip" v-if="unepTenantList.includes(domainName)">
      <view class="course-tipinfo">看完一条视频可得3分。不能拖拽，不能加速观看。</view>
      <view class="course-tipnum"
        >共<text class="weight-num">{{ chapTotal }}条</text>视频，已观看<text class="weight-num"
          >{{ haveView }} 条</text
        >
        视频，共<text class="weight-num">{{ score }}</text> 分。</view
      >
    </view>

    <view class="course-dropdown">
      <zb-dropdown-menu style="width: 100%">
        <zb-dropdown-item
          name="one"
          :options="catalogueTableData"
          v-model="value1"
          @change="getCourseData"
        ></zb-dropdown-item>
      </zb-dropdown-menu>
    </view>

    <view v-show="courseList.length" class="course-list">
      <view
        class="course-list-content"
        v-for="item in courseList"
        :key="item.courseId"
        @click="jumpTo(item)"
      >
        <view v-if="item.learnStatus === '2'">
          <view class="img-con">
            <image class="course-list-content-img" :src="item.courseImage"></image>
            <view class="bg"></view>
            <view class="logo">已完成</view>
          </view>
          <view style="padding: 6rpx 20rpx 20rpx 20rpx">
            <view class="course-list-content-name">{{ item.courseName }}</view>
            <view class="btn-group"><button class="havelearn" size="mini">已学习</button></view>
          </view>
        </view>
        <view v-else>
          <view class="img-con">
            <image class="course-list-content-img" :src="item.courseImage"></image>
          </view>
          <view style="padding: 6rpx 20rpx 20rpx 20rpx">
            <view class="course-list-content-name">{{ item.courseName }}</view>
            <view class="btn-group"><button class="tolearn" size="mini">立即学习</button></view>
          </view>
        </view>
      </view>
    </view>
    <xw-empty :isShow="!courseList.length" text="暂无课程" textColor="#777777"></xw-empty>
  </view>
</template>

<script>
  import { unepTenantList } from "@/utils/constant.js"
  import { catalogueType } from "@/utils/constant.js"
  import { unepList, getStudyCount } from "@/api/competition/index"
  import { getCatalogueList } from "@/api/system/catalogue.js"
  import { townCourseList } from "@/api/beicai/index.js"
  import { mapGetters } from "vuex"

  export default {
    onLoad() {
      this.getCourseData("onLoad")
      this.fetchCatalogueData()
      this.studyCount()
    },
    onPullDownRefresh() {
      this.getCourseData(false)
      this.studyCount()
      uni.stopPullDownRefresh()
    },
    onReachBottom() {
      if (this.courseList.length !== this.total) {
        this.pageNum += 1
        this.getCourseData("onReachBottom")
      }
    },

    data() {
      return {
        searchValue: "",
        pageNum: 1,
        pageSize: 12,
        total: 0,
        chapTotal: 0,
        courseList: [],
        score: 0,
        haveView: 0,
        value1: undefined,
        catalogueTableData: [],
        userType: localStorage.getItem("userType"),
        unepTenantList
      }
    },
    computed: {
      ...mapGetters({
        domainName: "domainName"
      })
    },

    methods: {
      studyCount() {
        // 查询已观看视频
        getStudyCount({}).then(res => {
          this.chapTotal = res.data.total
          this.haveView = res.data.already_learned
          this.score = this.haveView * 3
          if (this.score > 45) this.score = 45
        })
      },

      async getCourseData(flag) {
        if (flag !== "onReachBottom") {
          this.pageNum = 1
        }
        let queryData = {
          pageSize: this.pageSize,
          pageNum: this.pageNum,
          courseName: this.searchValue,
          catalogueId: this.value1
        }
        let res
        if (this.userType) {
          res = await townCourseList({ ...queryData, userType: this.userType })
        } else {
          res = await unepList(queryData)
        }
        this.total = res.total
        this.courseList = flag === "onReachBottom" ? this.courseList.concat(res.rows) : res.rows
      },

      // 跳转详情·
      jumpTo(item) {
        uni.navigateTo({
          url: "/pages/course/detail/index?id=" + item.courseId
        })
      },
      async fetchCatalogueData() {
        getCatalogueList({ catalogueType: catalogueType.COURSE_CATALOGUE }).then(response => {
          this.catalogueTableData = response.rows.map(({ catalogueId, catalogueName }) => ({
            text: catalogueName,
            value: catalogueId
          }))
          if (this.catalogueTableData[0].text === "全部目录") return
          this.catalogueTableData.unshift({
            text: "全部目录",
            value: undefined
          })
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
  .tolearn {
    background: #ed8941;
    color: #fff;
  }
  .havelearn {
    background: #f2f2f2;
  }
  .btn-group {
    display: flex;

    margin-top: 16rpx;
    > button {
      width: 100%;
      font-size: 16px;
      line-height: 2.2;
      /* margin-right:0px; */
    }
  }
  .weight-num {
    font-weight: bold;
    margin: 0 10rpx;
  }
  .course-tip {
    font-size: 15px;
    line-height: 20px;
    padding: 10rpx 30rpx 0 30rpx;
    background: #fff;
    padding-left: 14px;
  }
  .course-tipinfo {
    color: #2ea7da;
    margin-bottom: 4rpx;
  }
  .course-search {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #fff;
    z-index: 999;
    .searchBar {
      width: 100%;
    }
  }
  .img-con {
    position: relative;
    width: 100%;
    height: 100px;
    overflow: hidden;
    .logo {
      position: absolute;
      right: -54rpx;
      transform: rotate(40deg);
      background: #d01f25;
      color: #fff;
      top: 20rpx;
      line-height: 22px;
      font-size: 16px;
      padding: 0 30px;
    }
    .bg {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.3);
    }
  }
  .course-list {
    background-color: #fff;
    padding: 14px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    .course-list-content {
      width: 48%;
      margin-bottom: 14px;
      box-shadow: 0 0px 4px 0 rgba(95, 101, 105, 0.3);
      border-radius: 10rpx;
      overflow: hidden;
      .course-list-content-img {
        width: 100%;
        height: 100%;
      }
      .course-list-content-name {
        margin-top: 10rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 16px;
      }
    }
  }

  .searchButton {
    position: absolute;
    right: 30px;
    color: #1e8ffd;
  }

  ::v-deep .uni-searchbar__box {
    justify-content: unset;
  }
</style>

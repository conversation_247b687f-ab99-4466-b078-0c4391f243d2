<!--
 * @Description: 个人信息页面
 * @Author: <PERSON>
 * @LastEditors: sun<PERSON><PERSON>
 * @Date: 2023-04-10 10:29:54
 * @LastEditTime: 2023-09-07 16:04:53
-->
<template>
  <view class="container">
    <navigationBar path="/pages/mine/index" />
    <uni-list>
      <uni-list-item showExtraIcon="true" title="头像">
        <template v-slot:footer>
          <button
            class="avatar"
            open-type="chooseAvatar"
            @chooseavatar="onChooseAvatar"
          >
            <image :src="avatarUrl"></image>
          </button>
        </template>
      </uni-list-item>
      <uni-list-item showExtraIcon="true" title="昵称">
        <template v-slot:footer>
          <input
            type="nickname"
            class="weui-input"
            placeholder="请输入昵称"
            v-model="nickName"
            @blur="onNickName"
          />
        </template>
      </uni-list-item>
    </uni-list>
    <view class="buttonList">
      <button class="popup-content-btn" @click="handleSave">保存</button>
      <button v-show="logBtn" class="popup-logout-btn" @click="handleLogout">
        退出登录
      </button>
    </view>

    <uni-popup ref="logOutPopupRef">
      <view class="log-out">
        <view class="log-out-title">退出登录</view>
        <view style="border-top: 1px solid #d7d7d7; width: 100%"></view>
        <view class="log-out-content">确定退出登录？</view>
        <view class="log-out-button">
          <view @click="$refs.logOutPopupRef.close()">取消</view>
          <view @click="logOut" class="border-left">确定</view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import { fileUpload, editWxUser } from "@/api/system/user.js";
import { mapGetters } from "vuex";

export default {
  data() {
    return {
      avatarUrl: "",
      nickName: "",
    };
  },
  computed: {
    ...mapGetters({
      logBtn: "logBtn",
    }),
  },
  onLoad() {
    this.getUser();
  },
  methods: {
    getUser() {
      if (uni.getStorageSync("Admin-Token")) {
        this.avatarUrl =
          uni.getStorageSync("avatarUrl") || "/static/images/mine/user.png";
        this.nickName = uni.getStorageSync("nickName");
      }
    },
    onChooseAvatar(e) {
      this.uploadFilePromise(e.detail.avatarUrl);
    },
    onNickName(e) {
      this.$store.commit("SET_NICK_NAME", e.detail.value);
      this.nickName = e.detail.value;
    },

    async uploadFilePromise(avatarUrl) {
      // 调后台接口上传图片 这里的avatarUrl拿到的是微信地址
      const res = await fileUpload(avatarUrl);
      this.$store.commit("SET_AVATAR_URL", res.data.url);
      this.avatarUrl = res.data.url;
    },

    // 接收 avatarUrl / nickName 调接口完善头像或者昵称信息
    async completeMemberInfo() {
      let requestData = {
        nickName: this.nickName,
        avatarUrl: this.avatarUrl,
        userId: uni.getStorageSync("userId"),
      };
      await editWxUser(requestData);
    },

    handleSave() {
      uni.reLaunch({
        url: "/pages/mine/index",
      });
    },
    handleLogout() {
      this.$refs.logOutPopupRef.open();
    },
    async logOut() {
      try {
        await this.$store.dispatch('LogOut')
        this.$refs.logOutPopupRef.close()
        // 使用 reLaunch 确保清除所有页面栈
        uni.reLaunch({
          url: '/pages/login'
        })
      } catch (error) {
        console.error('退出登录失败:', error)
        uni.showToast({
          title: '退出登录失败，请重试',
          icon: 'none'
        })
      }
    },
  },
};
</script>

<style lang="scss">
page {
  background-color: #ffffff;
}

button::after {
  border: none;
}

.avatar {
  display: flex;
  margin-right: 10rpx;
  background-color: transparent;
}

.avatar > image {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
}

.weui-input {
  width: 190rpx;
  text-align: center;
}
text {
  margin-right: 10rpx;
  font-weight: bold;
}

.popup-content-btn {
  background-color: #ed9d32;
  border-radius: 50px;
  color: #fff;
  width: 85%;
  margin-top: 50rpx;
}

.popup-logout-btn {
  background-color: #4e8cee;
  border-radius: 50px;
  color: #fff;
  width: 85%;
  margin-top: 30rpx;
}

.log-out {
  display: flex;
  flex-direction: column;
  height: 400rpx;
  width: 550rpx;
  background-color: #fff;
  align-items: center;
  padding: 20rpx;
  font-size: 35rpx;
  border-radius: 10px;

  .log-out-title {
    height: 25%;
    display: flex;
    align-items: center;
  }

  .log-out-content {
    height: 50%;
    display: flex;
    align-items: center;
  }

  .log-out-button {
    height: 25%;
    display: flex;
    align-items: center;
    flex-direction: row;
    border-top-color: #d7d7d7;
    border-top-style: solid;
    border-top-width: 1px;
    width: 100%;
    justify-content: space-evenly;

    > view {
      display: flex;
      flex: 1;
      flex-direction: row;
      justify-content: center;
      align-items: center;
    }

    .border-left {
      border-left-color: #d7d7d7;
      border-left-style: solid;
      border-left-width: 1px;
      height: 80rpx;
      color: #ed9d32;
    }
  }
}

.buttonList {
  width: 100%;
  position: absolute;
  bottom: 30px;
}
</style>

/*
 * @Description: constant
 * @Author: <PERSON><PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-04-12 08:48:30
 * @LastEditTime: 2025-01-02 14:37:57
 */

export const ruleType = {
  LOGIN_PLATFORM_IPOINTS_RULE: "loginPlatformIPointsRule",
  SIGN_IN_POINTS_RULE: "signInIPointsRule",
  completingTheCourseIPointsRule: "completingTheCourseIPointsRule",
  otherIPointsRule: "otherIPointsRule"
}

export const catalogueType = {
  COURSE_CATALOGUE: "C",
  SUBJECT_CATALOGUE: "S",
  PAPER_CATALOGUE: "P",
  QUESTIONNAIRE_CATALOGUE: "Q",
  MEANS_CATALOGUE: "M",
  NEWS_CATALOGUE: "N"
}

export const questionType = {
  S: "单选题",
  M: "多选题",
  J: "判断题",
  F: "填空题",
  Q: "简答题"
}

export const specialTenantList = ["unep", "town", "cjaq", "yingji", "eduxd", "jinwaitan"]

export const unepTenantList = ["yingji", "jinwaitan"]

export const assetsPrefixUrl = "https://training-voc.obs.cn-north-4.myhuaweicloud.com:443/"

// 不需要展示考试成绩/分数相关信息的租户
export const noNeedExamScoreDomainList = ["lhsr"]

// 课程类型
export const COURSE_TYPE = {
  VIDEO_COURSE: "S", // 视频课程
  WORD_COURSE: "W", // 文档课程
  "2D_COURSE": "2", // 2D课程
  "3D_COURSE": "3", // 3D课程
  RECORED_COURSE: "4", // 录播课程
  XJ_COURSE: "X" // 宣教课程
}
// 课程类型对应的文件类型
export const FILE_COURSE_TYPE_MAP = {
  [COURSE_TYPE.VIDEO_COURSE]: ["wmv", "mp4", "flv", "avi", "rmvb", "mpg", "m4v"],
  [COURSE_TYPE.WORD_COURSE]: ["doc", "docx", "pdf", "ppt", "pptx"]
}

// 任务类型
export const TASK_ITEM_LEARNED_TYPE = {
  0: {
    typeName: "课程",
    field: "course",
    iconClass: "i-material-symbols:menu-book",
    color: "bg-green-500",
    actionText: "学习了"
  },
  1: {
    typeName: "考试",
    field: "exam",
    iconClass: "i-material-symbols:quiz",
    color: "bg-blue-500",
    actionText: "参加了"
  },
  2: {
    typeName: "资料",
    field: "manage",
    iconClass: "i-material-symbols-folder",
    color: "bg-orange-500",
    actionText: "浏览了"
  },
  3: {
    typeName: "问卷",
    field: "questionnaire",
    iconClass: "i-material-symbols-assignment",
    color: "bg-purple-500",
    actionText: "参加了"
  }
}

// 添加一个用于UnoCSS静态分析的注释，确保这些图标类名被识别，不要删除！！！！！！！！！！！���！！！！
/* 
  i-material-symbols:menu-book
  i-material-symbols:quiz
  i-material-symbols-folder
  i-material-symbols-assignment
  i-material-symbols:school-outline
*/

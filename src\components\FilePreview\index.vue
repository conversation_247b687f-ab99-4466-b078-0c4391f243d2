<!--
 * @Description: 文件预览
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-05-10 13:14:50
 * @LastEditTime: 2023-10-18 15:58:50
-->
<script>
  import { Base64 } from "js-base64"
  export default {
    data() {
      return {
        title: "文件预览",
        iframeUrl: "",
        kkFileURL: process.env.VUE_APP_KK_URL
      }
    },

    methods: {
      fileLoad(docUrl) {
        // console.log("docUrl", docUrl)
        this.iframeUrl = `${this.kkFileURL}/onlinePreview?url=${encodeURIComponent(
          Base64.encode(docUrl)
        )}`
        // console.log("this.iframeUrl", this.iframeUrl)
      }
    }
  }
</script>

<template>
  <web-view :fullscreen="false" :src="iframeUrl" style="height: 100vh; width: 100vw"></web-view>
</template>

<style scoped lang="scss"></style>

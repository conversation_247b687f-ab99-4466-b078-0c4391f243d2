<!--
 * @Description: 
 * @Author: sun<PERSON><PERSON>
 * @LastEditors: sunyunwu
 * @Date: 2023-09-19 17:32:35
 * @LastEditTime: 2023-10-08 08:57:12
-->
<template>
  <view class="con">
    <image
      src="/static/images/competition/home.png"
      mode="widthFix"
      style="width: 100%; margin-top: 0px"
    ></image>
    <view class="time">活动截止时间: 2023年10月11日 12:00</view>
    <view class="split-con">
      <view class="line"></view>
      <view class="title">2023年防灾减灾知识达人排名</view>
      <view class="line"></view>
    </view>
    <view class="btn">
      <button class="rank-num" size="default">
        您的成绩：第
        <text style="margin: 0 10rpx">{{ rank }}</text
        >名
      </button>
    </view>
    <view class="rank-table">
      <uni-table emptyText="暂无更多数据" :stripe="false">
        <!-- 表头行 -->
        <uni-tr class="t-head">
          <uni-th width="56" align="center">排名</uni-th>
          <uni-th width="60" align="center">用户名</uni-th>
          <uni-th width="50" align="center">分数</uni-th>
          <uni-th align="center">获得时间</uni-th>
        </uni-tr>
        <!-- 表格数据行 -->
        <uni-tr
          :class="['t-body', item.ranking === rank ? 'check-item' : '']"
          v-for="item in dataList"
          :key="item.ranking"
        >
          <uni-td align="center" style="padding: 0">
            <image
              v-if="item.ranking === 1"
              class="top-img"
              src="/static/images/competition/one.png"
            ></image>
            <image
              class="top-img"
              v-else-if="item.ranking === 2"
              src="/static/images/competition/two.png"
            ></image>
            <image
              class="top"
              v-else-if="item.ranking === 3"
              src="/static/images/competition/three.png"
            ></image>
            <text v-else>{{ item.ranking }}</text>
            <!-- <view v-if="item.ranking === rank" class="isuser">我</view> -->
          </uni-td>
          <uni-td align="center">{{ item.mobile }}</uni-td>
          <uni-td align="center">{{ item.score }}</uni-td>
          <uni-td align="center" style="font-size: 15px">{{ item.loginTime }}</uni-td>
        </uni-tr>
      </uni-table>
      <!--   <view class="more"
        ><text v-if="dataList.length !== allList.length" @click="handleMore">查看更多</text>
        <text v-else @click="handleLess">收起</text>
      </view> -->
    </view>
    <view class="completeList">
      <view>感谢您的关注</view>
      <view>本次活动已结束，敬请期待下次活动。</view>
    </view>
  </view>
</template>

<script>
  import { getUnepStudyRank } from "@/api/competition/index"
  export default {
    data() {
      return {
        rank: null,
        showMore: false,
        allList: [],
        dataList: []
      }
    },
    created() {
      // 获取名次
      getUnepStudyRank({ userId: this.$store.state.user.userId }).then(res => {
        this.rank = res.rows[0]["ranking"]
      })
      // 获取列表
      getUnepStudyRank({ pageSize: 20 }).then(res => {
        this.allList = res.rows
        this.dataList = res.rows.slice(0, 20)
      })
    },
    methods: {
      handleMore() {
        this.dataList = this.allList
      },
      handleLess() {
        this.dataList = this.allList.slice(0, 10)
      }
    }
  }
</script>

<style lang="scss" scoped>
  ::v-deep .uni-table {
    border-radius: 6px;
    overflow: hidden;
  }
  .top {
    width: 54rpx;
    height: 54rpx;
    margin-top: 12rpx;
  }
  .top-img {
    width: 48rpx;
    height: 48rpx;
    margin-top: 12rpx;
  }
  .split-con {
    display: flex;
    margin-bottom: 30rpx;
    .title {
      width: 260px;
      font-size: 16px;
      font-weight: bold;
      text-align: center;
      color: #ec8920;
    }
    .line {
      border-bottom: 1px dashed #ec8920;
      height: 0;
      flex: 1;
      margin-top: 12px;
    }
  }

  .time {
    line-height: 80rpx;
    font-size: 15px;
    font-weight: bold;
    text-align: center;
  }
  .more {
    text-align: center;
    margin: 20rpx 0 30rpx 0;
  }

  .t-head {
    background: #ec8920;
    > th {
      color: #fff;
    }
  }
  .check-item {
    > td {
      color: #d31f25 !important;
      font-weight: bold;
      position: relative;
      .isuser {
        position: absolute;
        width: 40rpx;
        height: 40rpx;
        border-radius: 50%;
        top: 50%;
        transform: translateY(-50%);
        right: -16rpx;
        background: #ec8920;
        color: #fff;
        font-size: 12px;
        z-index: 100;
      }
    }
  }
  .t-body {
    background: #fbecd3;
    td {
      border-bottom: none;
      font-size: 16px;
      background: #fbecd3 !important;
    }
  }
  .rank-table {
    margin-top: 20rpx;
    width: 100%;
  }
  .rank-num {
    background: linear-gradient(to bottom, #03a7f0, #bf80ff);
    color: #fff;

    width: 400rpx;
    display: inline-block;
    font-weight: bold;
    font-size: 16px;
  }

  .btn {
    text-align: center;
  }
  .con {
    min-height: 100vh;
    background: url(/static/images/competition/logbg_3.png) no-repeat;
    background-size: 100% 100%;
    padding: 20rpx;
  }

  .completeList {
    text-align: center;
    /*  margin-top: 100rpx; */
    font-size: 20px;
    line-height: 40px;
    font-weight: bold;
    color: #f58335;
    margin: 40rpx 0;
  }
</style>

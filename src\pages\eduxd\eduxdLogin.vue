<!--
 * @Description: 西电登录页面
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-05-09 11:38:12
 * @LastEditTime: 2024-12-17 09:42:29
-->

<template>
  <view
    class="normal-login-container"
    :style="`background-image: url('${assetsPrefixUrl}eduxd_login_bg.jpg');`"
  >
    <view class="logincon">
      <view class="login-form-container">
        <custom-tabs type="a1" :value="activeTab" @change="tabChange" backgroundColor="transparent">
          <custom-tab-pane label="登录" name="a1_1"> </custom-tab-pane>
          <custom-tab-pane label="注册" name="a1_2"> </custom-tab-pane>
        </custom-tabs>
        <view class="login-form-content">
          <uni-forms
            v-if="activeTab === 0"
            ref="loginFormRef"
            :rules="loginFormRule"
            :modelValue="loginForm"
          >
            <uni-forms-item name="username">
              <uni-easyinput v-model="loginForm.username" placeholder="请输入您的手机号" />
            </uni-forms-item>
            <uni-forms-item name="password">
              <uni-easyinput
                type="password"
                v-model="loginForm.password"
                placeholder="请输入密码"
              />
            </uni-forms-item>
            <view class="action-btn">
              <image
                @click="handleLogin"
                src="/static/images/eduxd/login-btn.png"
                mode="widthFix"
              ></image>
            </view>
          </uni-forms>
          <uni-forms
            v-else
            ref="registerFormRef"
            :rules="registerFormRule"
            :modelValue="registerForm"
          >
            <uni-forms-item name="countryId">
              <uniCombox
                labelKey="label"
                valueKey="id"
                :candidates="countryList"
                placeholder="请选择企业"
                @update:modelValue="selectChange"
              ></uniCombox>
            </uni-forms-item>
            <uni-forms-item name="deptName">
              <uni-easyinput v-model="registerForm.deptName" placeholder="请输入部门名称" />
            </uni-forms-item>
            <uni-forms-item name="userCode">
              <uni-easyinput v-model="registerForm.userCode" placeholder="请输入员工编号" />
            </uni-forms-item>
            <uni-forms-item name="userName">
              <uni-easyinput v-model="registerForm.userName" placeholder="请输入姓名" />
            </uni-forms-item>
            <uni-forms-item name="mobile">
              <uni-easyinput
                @input="checkCode"
                maxlength="11"
                v-model="registerForm.mobile"
                placeholder="请输入手机号"
              />
            </uni-forms-item>

            <uni-forms-item name="code">
              <uni-easyinput v-model="registerForm.code" placeholder="请输入验证码">
                <template #right>
                  <view
                    @click="getCode"
                    style="margin-right: 40rpx; color: #3b7ffc"
                    :class="{ active: !canCode }"
                  >
                    {{ codeMsg }}
                  </view>
                </template>
              </uni-easyinput>
            </uni-forms-item>
            <view class="action-btn">
              <image
                @click="handleRegister"
                src="/static/images/eduxd/register-btn.png"
                mode="widthFix"
              ></image>
            </view>
          </uni-forms>
        </view>
      </view>
    </view>
    <yangr-msg
      v-if="yangrMsgShow"
      :title="title"
      :info="info"
      :type="type"
      :btn="btn"
      @yangrMsgEvent="confirmRegister"
    ></yangr-msg>
  </view>
</template>

<script>
  import { setToken } from "@/utils/auth"
  import uniCombox from "@/components/uni-combox/index.vue"
  import yangrMsg from "@/components/yangr-msg/yangr-msg.vue"
  import { eduxdRegister } from "@/api/system/login.js"
  import { isContactWay } from "@/utils/validate"
  import { sendCode } from "@/api/competition/index"
  import { mapGetters } from "vuex"
  import { deptTreeSelect } from "@/api/system/user"
  import { assetsPrefixUrl } from "@/utils/constant.js"

  export default {
    components: {
      uniCombox,
      yangrMsg
    },
    data() {
      return {
        assetsPrefixUrl,
        loginForm: {
          username: "",
          password: ""
        },
        registerForm: {
          countryId: "",
          deptName: "",
          userCode: "",
          userName: "",
          mobile: "",
          code: ""
        },
        canCode: false,
        codeMsg: "获取验证码",
        interval: null,
        activeTab: 0,
        countryList: [],
        loginFormRule: {
          username: {
            rules: [
              {
                required: true,
                errorMessage: "手机号不能为空"
              }
            ]
          },
          password: {
            rules: [
              {
                required: true,
                errorMessage: "密码不能为空"
              }
            ]
          }
        },
        registerFormRule: {
          countryId: {
            rules: [
              {
                required: true,
                errorMessage: "企业名称不能为空"
              }
            ]
          },
          deptName: {
            rules: [
              {
                required: true,
                errorMessage: "部门名称不能为空"
              }
            ]
          },
          userCode: {
            rules: [
              {
                required: true,
                errorMessage: "员工编号不能为空"
              }
            ]
          },
          userName: {
            rules: [
              {
                required: true,
                errorMessage: "姓名不能为空"
              }
            ]
          },
          mobile: {
            rules: [
              {
                required: true,
                errorMessage: "手机号不能为空"
              }
            ]
          },
          code: {
            rules: [
              {
                required: true,
                errorMessage: "验证码不能为空"
              }
            ]
          }
        },
        title: "",
        info: "",
        type: "",
        yangrMsgShow: false,
        btn: ""
      }
    },
    created() {
      this.getCountryList()
    },
    mounted() {
      this.$nextTick(() => {
        this.$refs.loginFormRef.setRules(this.loginFormRule)
        // this.$refs.registerFormRef.setRules(this.registerFormRule)
      })
    },
    computed: {
      ...mapGetters({
        domainInfo: "domainInfo"
      })
    },
    methods: {
      // 判断验证码是否可点击
      checkCode(val) {
        if (val && isContactWay(val)) {
          this.canCode = true
        } else {
          this.canCode = false
        }
      },
      // 获取验证码
      async getCode() {
        if (this.canCode) {
          let queryData = {
            mobile: this.registerForm.mobile,
            scene: 1
          }
          clearInterval(this.interval)
          this.canCode = false
          this.sec = 60
          this.codeMsg = this.sec + "s后重试"
          await sendCode(queryData)
          this.$modal.msgSuccess("验证码发送成功！")
          this.interval = setInterval(() => {
            this.sec -= 1
            this.codeMsg = this.sec + "s后重试"
            if (this.sec === 0) {
              this.codeMsg = "重新获取"
              this.canCode = true
              clearInterval(this.interval)
            }
          }, 1000)
        }
      },
      // 注册
      handleRegister() {
        this.$refs["registerFormRef"].validate().then(async () => {
          const res = await eduxdRegister(this.registerForm)
          if (res.code === 200) {
            setToken(res.data.access_token)
            this.title = "注册成功，您的密码为："
            this.info = "xd123456"
            this.type = "success"
            this.btn = "知道了"
            this.yangrMsgShow = true
          }
        })
      },
      // 登录
      async handleLogin() {
        this.$refs["loginFormRef"]
          .validate()
          .then(() => {
            this.$store.dispatch("Login", this.loginForm).then(() => {
              this.loginSuccess()
            })
          })
          .catch(err => {
            this.$modal.msgError("必填项不能为空")
          })
      },
      // 登录成功后，处理函数
      loginSuccess() {
        // 设置用户信息
        this.$store.dispatch("GetInfo").then(res => {
          this.$tab.reLaunch("/pages/competition/index")
        })
      },
      tabChange(item) {
        this.activeTab = item.value
        this.$nextTick(() => {
          if (this.activeTab === 0) {
            // this.$refs.registerFormRef.clearValidate()
            this.$refs.loginFormRef.setRules(this.loginFormRule)
          } else {
            // this.$refs.loginFormRef.clearValidate()
            this.$refs.registerFormRef.setRules(this.registerFormRule)
          }
        })
      },
      async getCountryList() {
        const res = await deptTreeSelect({ parentId: 100 })
        this.countryList = res.data || []
      },
      selectChange(val) {
        this.registerForm.countryId = val
      },
      confirmRegister() {
        this.yangrMsgShow = false
        this.loginSuccess()
      }
    }
  }
</script>

<style lang="scss">
  ::v-deep .uni-forms-item__label {
    display: none;
  }

  ::v-deep .uni-combox {
    border: 2px solid #e7e8e8;
    .uni-input-placeholder {
      font-size: 32rpx !important;
      padding-left: 28rpx !important;
    }
    .uni-input-wrapper {
      text-align: left;
      padding-left: 28rpx !important;
    }
    .uni-combox__selector-item {
      text-align: left;
      font-size: 26rpx;
    }
  }
  ::v-deep .uni-easyinput__content {
    border-radius: 10rpx;
    height: 70rpx;
    .uni-easyinput__placeholder-class,
    .uni-input-input {
      font-size: 32rpx !important;
      padding-left: 28rpx !important;
    }
  }
  ::v-deep .fixed-msg-info {
    font-size: 58rpx;
    font-weight: bold;
    color: #333;
  }
  page {
    background-color: #f2f2f2;
  }
  .active {
    color: #888 !important;
  }
  .normal-login-container {
    background-repeat: no-repeat;
    background-size: 100% auto;
    background-position: top;
    width: 100%;
    height: 100vh;
    overflow: hidden;
    .logincon {
      padding: 0 60rpx;
      margin-top: 300rpx;
      display: flex;
      justify-content: center;
      flex-direction: column;
      width: 100%;
      align-items: center;
      .tenant-icon {
        height: 150rpx;
      }
      .title {
        text-align: center;
        margin: 20rpx 0 40rpx 0;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 42rpx;
        font-weight: 700;
        margin-bottom: 80rpx;
      }
      .login-form-container {
        width: 100%;
        padding: 0 20rpx;
        .login-form-content {
          padding: 40rpx 0;

          .code-container {
            display: flex;
            align-items: center;
          }
        }
      }
    }

    .login-form-content {
      text-align: center;
      margin: 10px auto 10px auto;
      width: 80%;

      .login-btn {
        background-color: #3a89e6;
        margin-top: 40rpx;
        height: 45px;
      }

      .xieyi {
        color: #333;
        font-size: 14px;
      }

      .login-code {
        height: 38px;
        float: right;

        .login-code-img {
          height: 38px;
          position: absolute;
          margin-left: 10px;
          width: 200rpx;
        }
      }
    }
  }

  .xieyi {
    font-size: 12px;
  }
</style>

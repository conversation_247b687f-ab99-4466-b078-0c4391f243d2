<template>
  <view class="min-h-screen bg-gray-50">
    <navigationBar path="/pages/mine/index" />
    <!-- 搜索区域 -->
    <view class="flex items-center justify-center bg-white relative">
      <uni-search-bar
        v-model="searchValue"
        class="flex-1"
        radius="50"
        placeholder="请输入标题"
        clearButton="auto"
        cancelButton="none"
        @confirm="getCourseData"
      />
      <view class="absolute right-8 text-blue-500 cursor-pointer" @click="getCourseData">搜索</view>
    </view>

    <!-- 下拉菜单 -->
    <view class="w-full">
      <zb-dropdown-menu class="w-full">
        <zb-dropdown-item
          name="one"
          :options="learnTypeData"
          v-model="courseType"
          @change="getCourseData"
        ></zb-dropdown-item>
        <zb-dropdown-item
          name="two"
          :options="completeStatusData"
          v-model="learnStatus"
          @change="getCourseData"
        ></zb-dropdown-item>
      </zb-dropdown-menu>
    </view>

    <!-- 课程列表 -->
    <view v-show="courseList.length" class="bg-white p-2.5">
      <view
        v-for="item in courseList"
        :key="`${item.taskId}${item.courseLearnedId}`"
        class="py-5 px-2 flex border-b border-gray-200"
        @click="jumpTo(item)"
      >
        <!-- 封面图 -->
        <view class="w-[120px] aspect-[16/9] flex-shrink-0 rounded-lg overflow-hidden">
          <image
            :src="
              item.courseImage ||
              'https://training-voc.obs.cn-north-4.myhuaweicloud.com/default_course_cover.webp'
            "
            class="w-full h-full object-cover"
            mode="aspectFill"
          />
        </view>

        <!-- 内容区域 -->
        <view class="ml-2.5 flex-1 overflow-hidden">
          <!-- 顶部标题区域 -->
          <view class="flex items-center mb-1.5">
            <view
              v-if="item.courseType"
              class="w-10 h-5 px-1 rounded flex items-center justify-center text-xs text-white mr-1.5"
              :class="item.courseType === '0' ? 'bg-[#f53223]' : 'bg-[#45b757]'"
            >
              {{ item.courseType === "0" ? "必修" : "选修" }}
            </view>
            <view class="text-base text-gray-900 truncate">{{ item.courseName }}</view>
          </view>

          <!-- 中间信息区域 -->
          <view class="text-gray-500">
            <template v-if="item.taskId">
              <view>任务名称：{{ item.taskName }}</view>
              <view>开始时间：{{ item.startTime }}</view>
              <view>结束时间：{{ item.endTime }}</view>
            </template>
            <template v-else>
              <view>上架时间：{{ item.startTime }}</view>
              <view>下架时间：{{ item.endTime }}</view>
            </template>
          </view>

          <!-- 底部状态 -->
          <view class="text-gray-500">
            状态：{{ item.learnStatus === "2" ? "已完成" : "未完成" }}
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <xw-empty :isShow="!courseList.length" text="暂无课程" textColor="#777777"></xw-empty>
  </view>
</template>

<script>
  import { myCourseList } from "@/api/course"
  import xwEmpty from "@/components/xw-empty/xw-empty"
  export default {
    components: { xwEmpty },
    onShow() {
      this.getCourseData("onLoad")
    },

    onReachBottom() {
      if (this.courseList.length !== this.dataTotal) {
        this.pageNum += 1
        this.getCourseData("onReachBottom")
      }
    },

    data() {
      return {
        searchValue: "",
        pageNum: 1,
        pageSize: 12,
        dataTotal: 0,
        courseType: undefined,
        learnStatus: undefined,
        courseList: [],
        learnTypeData: [
          {
            text: "全部类型",
            value: undefined
          },
          {
            text: "必修",
            value: "0"
          },
          {
            text: "选修",
            value: "1"
          }
        ],
        completeStatusData: [
          {
            text: "全部状态",
            value: undefined
          },
          {
            text: "已完成",
            value: 2
          },
          {
            text: "未完成",
            value: 3
          }
        ]
      }
    },

    methods: {
      async getCourseData(flag) {
        if (flag !== "onReachBottom") {
          this.pageNum = 1
        }
        let queryData = {
          pageSize: this.pageSize,
          pageNum: this.pageNum,
          courseType: this.courseType,
          learnStatus: this.learnStatus,
          courseName: this.searchValue
        }
        const { rows, total } = await myCourseList(queryData)
        this.dataTotal = total
        this.courseList = flag === "onReachBottom" ? this.courseList.concat(rows) : rows
      },

      jumpTo(item) {
        uni.navigateTo({
          url: "/pages/course/detail/index?id=" + item.courseId
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
  ::v-deep .uni-searchbar__box {
    justify-content: unset !important;
  }
</style>

<template>
  <view class="flex flex-col items-center">
    <navigationBar v-if="showNavBar != 'false'" :path="backPath" />
    <view class="w-full">
      <zb-dropdown-menu class="w-full">
        <zb-dropdown-item
          name="one"
          :options="examStatusList"
          v-model="examStatus"
          @change="fetchExamList"
        ></zb-dropdown-item>
      </zb-dropdown-menu>
    </view>
    <view class="my-2.5">
      {{ $store.state.user.nickName }}同学，您有{{ todoNum }}个考试项目待完成,加油！
    </view>
    <view
      v-for="item in examList"
      :key="`${item.baseId}-${item.arrangeId}`"
      class="w-full bg-white mb-2.5 p-5 pb-2.5 relative"
    >
      <view class="text-[20px] font-bold text-[#333] mb-2.5">{{ item.baseName }}</view>
      <view class="flex items-center mb-2.5">
        <text class="text-[#7f7f7f]">状态：</text>
        <view
          class="px-4 py-1 rounded text-white text-sm"
          :class="{
            'bg-blue-500': item.examStatus === '0',
            'bg-orange-500': item.examStatus === '1',
            'bg-[#ec808d]': item.examStatus === '3',
            'bg-green-500': item.examStatus === '2'
          }"
        >
          {{ item.examStatusDesc }}
        </view>
      </view>
      <view class="text-[#7f7f7f] text-14px mb-2.5">
        考试时间：
        <text class="text-[#535353]">{{ item.startTime }} - {{ item.endTime }}</text>
      </view>
      <view v-if="item.examStatus === '2'" class="absolute right-10 top-10px">
        <view
          class="w-full h-full !text-70px"
          :class="[
            'iconfont',
            item.isPass == '1' ? 'icon-yitongguo text-[#5ac3b5]' : 'icon-weitongguo text-[#e34d65]'
          ]"
        ></view>
      </view>
      <view class="flex items-center border-t border-[#f2f2f2] pt-2.5">
        <button
          class="w-[100px] text-base text-white rounded ml-auto mr-0"
          :class="['0', '1'].includes(item.examStatus) ? 'bg-orange-500' : 'bg-gray-500'"
          @click="jumpTo(item)"
        >
          {{ ["0", "1"].includes(item.examStatus) ? "进入考场" : "查看详情" }}
        </button>
      </view>
    </view>
    <xw-empty :isShow="examList.length === 0" text="暂无考试" textColor="#777777"></xw-empty>
    <yangr-msg
      v-if="yangrMsgShow"
      :title="title"
      :type="type"
      btn="跳转至学习"
      @yangrMsgEvent="pushToStudy"
    ></yangr-msg>
  </view>
</template>

<script>
  import { getNeedExamList, checkTaskCompleted } from "@/api/exam/arrange.js"
  import { mapGetters } from "vuex"

  export default {
    async onLoad(options) {
      if (options.showNavBar == "false") {
        this.showNavBar = options.showNavBar
      }
      if (options.backPath) {
        this.backPath = options.backPath
      }
      this.getFileTypeList()
      await this.fetchExamList()
      await this.fetchEndedExam()
    },

    onReachBottom() {
      if (this.examList.length !== this.dataTotal) {
        this.pageNum += 1
        this.fetchExamList("onReachBottom")
      }
    },

    data() {
      return {
        yangrMsgShow: false,
        title: "",
        type: "",
        showNavBar: "",
        examList: [],
        todoNum: 0, // 待完成考试数量
        pageNum: 1,
        pageSize: 10,
        dataTotal: 0,
        examStatus: undefined,
        examStatusList: [],
        backPath: "/pages/mine/index"
      }
    },

    computed: {
      ...mapGetters({
        domainName: "domainName"
      })
    },
    methods: {
      async fetchExamList(flag) {
        if (flag !== "onReachBottom") {
          this.pageNum = 1
        }
        let queryData = {
          pageSize: this.pageSize,
          pageNum: this.pageNum,
          examStatus: this.examStatus
        }
        const { rows, total } = await getNeedExamList(queryData)
        this.dataTotal = total
        this.examList = flag === "onReachBottom" ? this.examList.concat(rows) : rows
      },
      async fetchEndedExam() {
        let queryData = {
          pageSize: this.pageSize,
          pageNum: this.pageNum,
          examStatus: "2"
        }
        const { total } = await getNeedExamList(queryData)
        this.todoNum = this.dataTotal - total
      },
      async jumpTo(item) {
        if (["0", "1"].includes(item.examStatus) && item.remark) {
          const res = await checkTaskCompleted({ taskId: item.remark })
          if (!res.data) {
            if (this.domainName === "yygf") {
              this.title =
                "提醒！本次工伤预防项目，需要学员首先完成“我的课程”里所有课程，才能进入“我的考试”完成考试！"
            } else {
              this.title = "提醒！学员首先完成“我的课程”里所有课程，才能进入“我的考试”完成考试！"
            }
            this.type = "error"
            this.yangrMsgShow = true
            return
          }
        }
        uni.navigateTo({
          url: `/pages/mine/exam/prepare/index?baseId=${item.baseId}&arrangeId=${item.arrangeId}&showNavBar=${this.showNavBar}`
        })
      },
      pushToStudy() {
        uni.switchTab({ url: "/pages/learn/index" })
      },
      async getFileTypeList() {
        this.examStatusList = await this.$getDict("exam_status")
        if (this.examStatusList[0].text === "全部") return
        this.examStatusList.unshift({
          text: "全部",
          value: undefined
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
  /* 移除按钮内文字的左边距 */
  button {
    padding: 0;
  }
  button > span {
    margin: 0;
  }
</style>

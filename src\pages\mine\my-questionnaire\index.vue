<!--
 * @Description: 我的问卷页面
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-12-13 11:09:38
 * @LastEditTime: 2024-12-27 17:03:59
-->

<template>
  <view class="min-h-screen bg-gray-50">
    <navigationBar path="/pages/mine/index" />

    <!-- 搜索栏 -->
    <view class="flex items-center justify-between px-4 py-3 bg-white sticky top-0 z-50">
      <uni-search-bar
        v-model="searchValue"
        class="flex-1 mr-3"
        radius="50"
        placeholder="请输入标题"
        clearButton="auto"
        cancelButton="none"
        @confirm="getQuestionData"
      />
      <view
        class="px-4 py-2 bg-blue-500 text-white rounded-full text-sm active:bg-blue-600"
        @click="getQuestionData"
      >
        搜索
      </view>
    </view>

    <!-- 下拉菜单 -->
    <view class="w-full bg-white border-b border-gray-100">
      <zb-dropdown-menu class="w-full">
        <zb-dropdown-item
          name="one"
          :options="questionStatusData"
          v-model="value1"
          @change="getQuestionData"
        ></zb-dropdown-item>
        <zb-dropdown-item
          name="two"
          :options="byTime"
          v-model="value2"
          @change="getQuestionData"
        ></zb-dropdown-item>
      </zb-dropdown-menu>
    </view>

    <!-- 问卷列表 -->
    <view v-show="questionList.length" class="bg-white">
      <view
        v-for="item in questionList"
        :key="item.questionnaireIssuedId"
        class="flex items-center p-4 border-b border-gray-100 hover:bg-gray-50"
      >
        <!-- 封面图 -->
        <view class="w-[80px] aspect-[5/6] flex-shrink-0 rounded-lg overflow-hidden">
          <image
            :src="item.questionnaireCover || '/static/images/deault_means.png'"
            class="w-full h-full object-cover"
            mode="aspectFill"
          />
        </view>

        <!-- 内容区 -->
        <view class="flex-1 ml-4">
          <text class="text-lg font-medium text-gray-900 line-clamp-1 mb-2">
            {{ item.surveyTitle }}
          </text>
          <view class="text-base text-gray-500 mb-2"> 分享者：{{ item.createBy }} </view>
          <view class="space-y-1">
            <view class="text-sm text-gray-400">开始时间：{{ item.queryStartTime }}</view>
            <view class="text-sm text-gray-400">结束时间：{{ item.queryEndTime }}</view>
          </view>
        </view>

        <!-- 按钮 -->
        <view class="flex items-center ml-4">
          <button
            v-if="item.queryStatus !== '1'"
            @click.stop="jumpTo(item)"
            class="w-20 h-10 bg-blue-500 text-white rounded-full text-sm hover:bg-blue-600 transition-colors flex items-center justify-center"
          >
            参与问卷
          </button>
          <button
            v-else
            disabled
            class="w-20 h-10 bg-gray-200 text-gray-500 rounded-full text-sm cursor-not-allowed flex items-center justify-center"
          >
            已参加
          </button>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <xw-empty :isShow="!questionList.length" text="暂无问卷" textColor="#777777" />
  </view>
</template>

<script>
  import { myList } from "@/api/course/questionnaire"
  import xwEmpty from "@/components/xw-empty/xw-empty"

  export default {
    components: { xwEmpty },

    onLoad() {
      this.getQuestionData("onLoad")
    },

    onReachBottom() {
      if (this.questionList.length !== this.dataTotal) {
        this.pageNum += 1
        this.getQuestionData("onReachBottom")
      }
    },
    data() {
      return {
        searchValue: "",
        value1: undefined,
        value2: "desc",
        questionStatusData: [
          {
            text: "全部状态",
            value: undefined
          },
          {
            text: "已参加",
            value: 1
          },
          {
            text: "未参加",
            value: 0
          }
        ],
        byTime: [
          {
            text: "时间降序",
            value: "desc"
          },
          {
            text: "时间升序",
            value: "asc"
          }
        ],
        questionList: [],
        pageSize: 12,
        pageNum: 1,
        dataTotal: 0
      }
    },

    methods: {
      async getQuestionData(flag) {
        if (flag !== "onReachBottom") {
          this.pageNum = 1
        }
        const queryData = {
          pageSize: this.pageSize,
          pageNum: this.pageNum,
          queryStatus: this.value1,
          sortField: "create_time",
          sortOrder: this.value2,
          surveyTitle: this.searchValue
        }
        const { rows, total } = await myList(queryData)
        this.dataTotal = total || 0
        this.questionList = flag === "onReachBottom" ? this.questionList.concat(rows) : rows
      },

      jumpTo(item) {
        if (item.queryStatus === "1") return
        uni.navigateTo({
          url: `/pages/mine/my-questionnaire/doQuestionnaire/index?questionnaireId=${item.questionnaireId}&questionnaireIssuedId=${item.questionnaireIssuedId}`
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
  ::v-deep .uni-searchbar__box {
    justify-content: flex-start !important;
  }

  .hover\:bg-gray-50:hover {
    background-color: rgba(249, 250, 251, 0.8);
  }

  button {
    &:active {
      transform: scale(0.98);
    }
  }
</style>

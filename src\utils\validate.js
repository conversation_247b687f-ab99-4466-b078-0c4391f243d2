/*
 * @Description: validate
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-09-20 16:49:43
 * @LastEditTime: 2025-06-23 08:51:31
 */
/**
 * @description: 联系方式验证（固定电话 + 手机）
 * @author: du<PERSON>jiefei
 */
export function isContactWay(str) {
	// 空值则校验通过，必填会有必填校验
	if (!str) return true
	// 固话
	const fixedLineTel = new RegExp(
			'^(0[0-9]{2,3}-)?([2-9][0-9]{6,7})+(-[0-9]{1,4})?$'
		)

	return fixedLineTel.test(str) || isMobile(str)
}

/**
 * @description: 手机号码验证（更宽松的规则，支持166等更多号段）
 * @author: <PERSON>
 */
export function isMobile(str) {
	// 空值则校验通过，必填会有必填校验
	if (!str) return true
	// 手机号：1开头，第二位是3-9，总共11位数字
	// 这个正则支持所有可能的手机号段，包括166、198、199等
	const mobile = /^1[3-9]\d{9}$/
	return mobile.test(str)
}
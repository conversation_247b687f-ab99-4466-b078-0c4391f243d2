<!--
 * @Description: 
 * @Author: <PERSON><PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-05-23 16:30:42
 * @LastEditTime: 2024-12-27 17:03:20
-->
<template>
  <view class="min-h-screen bg-gray-50">
    <navigationBar :path="fromPath || '/pages/index'" />
    <!-- 搜索栏 -->
    <view class="flex items-center justify-between px-4 py-3 bg-white sticky top-0 z-50">
      <uni-search-bar
        v-model="searchValue"
        class="flex-1 mr-3"
        radius="50"
        placeholder="请输入标题"
        clearButton="auto"
        cancelButton="none"
        @confirm="getResourceData"
      />
      <view
        class="px-4 py-2 bg-blue-500 text-white rounded-full text-sm active:bg-blue-600"
        @click="getResourceData"
      >
        搜索
      </view>
    </view>

    <!-- 下拉菜单 -->
    <view class="w-full bg-white border-b border-gray-100">
      <zb-dropdown-menu class="w-full">
        <zb-dropdown-item
          name="one"
          :options="catalogueTableData"
          v-model="value1"
          @change="getResourceData"
        ></zb-dropdown-item>
        <zb-dropdown-item
          name="two"
          :options="fileTypeOpts"
          v-model="value2"
          @change="getResourceData"
        ></zb-dropdown-item>
      </zb-dropdown-menu>
    </view>

    <!-- 资料列表 -->
    <view v-if="resourceList && resourceList.length > 0" class="p-4">
      <view class="text-gray-600 mb-4">共有资料 {{ dataTotal }} 份</view>

      <!-- 资料卡片 -->
      <view class="space-y-4">
        <view
          v-for="item in resourceList"
          :key="item.manageId"
          class="bg-white rounded-lg shadow-md p-4 hover:shadow-lg transition-shadow duration-300"
        >
          <!-- 卡片上半部分 -->
          <view class="flex items-center justify-between mb-4">
            <!-- 左侧内容 -->
            <view class="flex items-start flex-1">
              <view class="w-[70px] aspect-[5/6] flex-shrink-0 rounded-lg overflow-hidden">
                <image
                  src="/static/images/deault_means.png"
                  class="w-full h-full object-cover"
                  mode="aspectFill"
                />
              </view>
              <view class="ml-4 flex-1 flex flex-col justify-between">
                <view class="text-lg font-medium text-gray-900 line-clamp-1 mb-2">
                  {{ item.manageName }}
                </view>
                <view class="text-sm text-gray-500 mb-1"> 分享者：{{ item.createBy }} </view>
                <view class="text-sm text-gray-400">
                  上传时间：{{ dayjs(item.createTime).format("YYYY-MM-DD") }}
                </view>
              </view>
            </view>

            <!-- 右侧按钮 -->
            <view class="ml-4">
              <button
                class="px-5 h-9 bg-blue-500 text-white rounded-full text-sm hover:bg-blue-600 transition-colors flex items-center justify-center"
                @click.stop="jumpTo(item)"
              >
                查阅资料
              </button>
            </view>
          </view>

          <!-- 卡片下半部分：数据统计 -->
          <view class="grid-cols-3 text-center pt-3 border-t border-gray-100 text-base">
            <view class="flex items-center justify-center text-gray-500">
              <view class="i-material-symbols:visibility-outline mr-1"></view>
              <text>{{ item.viewNumber || 0 }}</text>
            </view>
            <view class="flex items-center justify-center text-gray-500">
              <view class="i-material-symbols:download mr-1"></view>
              <text>{{ item.downloadNumber || 0 }}</text>
            </view>
            <view class="flex items-center justify-center text-gray-500">
              <view class="i-material-symbols:thumb-up-outline mr-1"></view>
              <text>{{ item.likeNumber || 0 }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <xw-empty
      :isShow="!resourceList.length"
      text="暂无资料"
      textColor="#777777"
    />
  </view>
</template>

<script>
  import { getCatalogueList } from "@/api/system/catalogue.js"
  import { catalogueType } from "@/utils/constant.js"
  import { listManage } from "@/api/course/manage.js"
  import xwEmpty from "@/components/xw-empty/xw-empty"
  import dayjs from "dayjs"
  export default {
    components: { xwEmpty },
    data() {
      return {
        fromPath: '', // 来源页面路径
        value1: undefined,
        value2: undefined,
        catalogueTableData: [],
        fileTypeOpts: [],
        searchValue: "",
        pageNum: 1,
        pageSize: 6,
        dataTotal: 0,
        resourceList: [],
        status: "loadmore"
      }
    },

    onLoad(options) {
      // 获取来源页面路径
      if (options.from) {
        this.fromPath = decodeURIComponent(options.from)
      }
    },

    onShow() {
      this.getCatalogueList()
      this.getFileTypeList()
      this.getResourceData("onLoad")
    },

    onReachBottom() {
      if (this.resourceList.length !== this.dataTotal) {
        this.pageNum += 1
        this.getResourceData("onReachBottom")
      }
    },

    methods: {
      dayjs,
      //获取目录信息并重构数组
      getCatalogueList() {
        getCatalogueList({ catalogueType: catalogueType.MEANS_CATALOGUE }).then(response => {
          this.catalogueTableData = response.rows.map(({ catalogueId, catalogueName }) => ({
            text: catalogueName,
            value: catalogueId
          }))
          if (this.catalogueTableData[0].text === "全部目录") return
          this.catalogueTableData.unshift({
            text: "全部目录",
            value: undefined
          })
        })
      },

      async getFileTypeList() {
        this.fileTypeOpts = await this.$getDict("sys_flie_type")
        if (this.fileTypeOpts[0].text === "全部类型") return
        this.fileTypeOpts.unshift({
          text: "全部类型",
          value: undefined
        })
      },

      async getResourceData(flag) {
        if (flag !== "onReachBottom") {
          this.pageNum = 1
        }
        let queryData = {
          pageSize: this.pageSize,
          pageNum: this.pageNum,
          catalogueId: this.value1,
          manageType: this.value2,
          manageName: this.searchValue
        }
        const { rows, total } = await listManage(queryData)
        this.dataTotal = total
        this.resourceList = flag === "onReachBottom" ? this.resourceList.concat(rows) : rows
      },

      jumpTo(item) {
        uni.navigateTo({
          url: "/pages/home/<USER>/resource-detail/index?id=" + item.manageId
        })
        // // #ifdef H5
        // window.open(item.manageAddress)
        // // #endif

        // // #ifndef H5
        // uni.downloadFile({
        //   url: item.manageAddress,
        //   success: res => {
        //     if (res.statusCode === 200) {
        //       uni.saveFile({
        //         tempFilePath: res.tempFilePath,
        //         success: function (res) {
        //           uni.showToast({
        //             title: "下载成功",
        //             icon: "success"
        //           })
        //         }
        //       })
        //     }
        //   },
        //   fail: err => {
        //     uni.showToast({
        //       title: "下载失败",
        //       icon: "none"
        //     })
        //   }
        // })
        // // #endif
      }
    }
  }
</script>

<style lang="scss" scoped>
  ::v-deep .uni-searchbar__box {
    justify-content: flex-start !important;
  }

  button {
    &:active {
      transform: scale(0.98);
    }
  }

  .i-material-symbols {
    &:visibility-outline,
    &:download-outline,
    &:thumb-up-outline {
      font-size: 1.2em;
    }
  }
</style>

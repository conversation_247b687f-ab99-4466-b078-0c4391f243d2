<template>
  <view class="login" :style="`background-image: url('${assetsPrefixUrl}aqsc_login_bg.jpg');`">
    <view class="login-body">
      <view class="login-form">
        <view class="login-form-one">
          <view class="login-form-one-tel">
            <uni-easyinput
              prefixIcon="person-filled"
              :inputBorder="false"
              v-model="tel"
              :styles="stylesTel"
              placeholderStyle="font-size:16px"
            ></uni-easyinput>
          </view>
          <view class="login-form-one-code">
            <uni-easyinput
              prefixIcon="locked-filled"
              :inputBorder="false"
              v-model="code"
              :styles="styles"
              placeholderStyle="font-size:16px"
              primaryColor="white"
            ></uni-easyinput>
            <view class="code" :class="{ disabled: isDisabled }" @click="getCode">{{
              codeText
            }}</view>
          </view>
        </view>
        <img
          src="@/static/images/fzjz/login-button.png"
          class="login-button"
          @click="login"
          alt=""
        />
      </view>
      <!-- <view class="login-tip">未注册的手机号将默认开通平台账号</view> -->
    </view>
  </view>
</template>

<script>
  import { sendCode } from "@/api/competition/index"
  import { assetsPrefixUrl } from "@/utils/constant.js"

  export default {
    data() {
      return {
        assetsPrefixUrl,
        tel: "",
        code: "",
        isDisabled: false,
        countDownSecond: 60,
        timer: null,
        codeText: "获取验证码",
        stylesTel: {
          borderColor: "#8c8988"
        },
        styles: {
          borderColor: "transparent"
        }
      }
    },
    created() {},
    computed: {},
    methods: {
      async getCode() {
        if (!this.isDisabled) {
          if (!this.tel) {
            return uni.showToast({
              icon: "none",
              title: "手机号不能为空"
            })
          }
          if (!/^1(3|4|5|7|8|9|6)\d{9}$/i.test(this.tel)) {
            return uni.showToast({
              icon: "none",
              title: "请输入正确的手机号码"
            })
          }
          let queryData = {
            mobile: this.tel,
            scene: 1
          }
          const res = await sendCode(queryData)
          this.$modal.msgSuccess("验证码发送成功！")
          this.startCountDown()
        }
      },

      //验证码倒计时
      startCountDown() {
        this.isDisabled = true
        this.countDownSecond = 60
        this.codeText = "60s"
        this.timer = setInterval(() => {
          this.countDownSecond--
          if (this.countDownSecond <= 0) {
            clearInterval(this.timer)
            this.codeText = "获取验证码"
            this.isDisabled = false
          } else {
            this.codeText = `${this.countDownSecond}s`
          }
        }, 1000)
      },

      //登录
      async login() {
        if (!this.tel) {
          return uni.showToast({
            icon: "none",
            title: "手机号不能为空"
          })
        }
        if (!/^1(3|4|5|7|8|9|6)\d{9}$/i.test(this.tel)) {
          return uni.showToast({
            icon: "none",
            title: "请输入正确的手机号码"
          })
        }
        if (!this.code) {
          return uni.showToast({
            icon: "none",
            title: "验证码不能为空"
          })
        }
        let queryData = {
          mobile: this.tel,
          code: this.code
        }
        this.$store.dispatch("unepLogin", queryData).then(() => {
          uni.switchTab({
            url: "/pages/fzjz/index"
          })
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
  .login {
    display: flex;
    flex-direction: column;
    align-items: center;
    background-repeat: no-repeat;
    background-size: 100% auto;
    background-position: top;
    height: 100vh;
    .login-body {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 90%;
      margin-top: 650rpx;
      background-color: #f7f7f7;
      padding: 5px;
      border-radius: 5px;
    }
    .login-form {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 100%;
      background-color: #eef3f6;
      border-radius: 5px;
      padding: 30px;
      .login-form-one {
        .login-form-one-tel {
          border: 1px solid #8c8988;
          background-color: white;
          border-radius: 10px;
        }
        .login-form-one-code {
          background-color: white;
          border-radius: 10px;
          display: flex;
          align-items: center;
          margin-top: 30px;
          border: 1px solid #8c8988;
          .code {
            width: 35%;
            font-size: 14px;
            text-align: center;
            border-left: 1px solid #8c8988;
            margin-left: 10px;
            color: #5b5b5b;
          }
          .disabled {
            color: #aaaaaa;
          }
        }
      }
    }
    .login-button {
      width: 80%;
      margin-top: 60rpx;
      line-height: 40px;
    }
  }

  ::v-deep .uni-easyinput__content-input {
    height: 50px;
    font-size: 18px;
  }
</style>

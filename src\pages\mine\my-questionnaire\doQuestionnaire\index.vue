<!--
 * @Description: 参与问卷
 * @Author: <PERSON><PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-06-05 15:24:21
 * @LastEditTime: 2025-05-26 09:16:27
-->
<template>
  <view class="survey">
    <navigationBar path="/pages/mine/my-questionnaire/index" v-if="showNavBar" />
    <view class="survey-body">
      <view class="survey-title">
        <view>{{ questionnaireData.surveyTitle }}</view>
        <view v-if="domainName === 'zhzg' || domainName === 'yuyuan'" class="zhzg-tips">
          5分：非常满意； 4分：满意；3分：一般；<br />2分：不太满意；1分：非常不满意
        </view>
        <view>
          {{ questionnaireData.surveyDescription }}
        </view>
      </view>
      <view class="survey-content">
        <view
          class="survey-content-list"
          v-for="(item, index) in surveyList"
          :key="item.traningQuestionId"
        >
          <view class="survey-content-list-title"> {{ index + 1 }}.{{ item.questionName }} </view>
          <u-textarea
            v-if="item.questionType === 'Q'"
            v-model="item.userAnswer"
            placeholder="请输入内容"
            @blur="fetchQuestionAnswer(item)"
          ></u-textarea>
          <u-radio-group
            v-if="item.questionType === 'S'"
            v-model="item.userAnswer"
            placement="column"
            @change="fetchQuestionAnswer(item)"
          >
            <u-radio
              :customStyle="{ marginBottom: '10px' }"
              v-for="choice in 11"
              v-if="item[`item${choice}`]"
              :key="choice"
              :label="item[`item${choice}`]"
              :name="String.fromCharCode(64 + choice)"
            >
            </u-radio>
          </u-radio-group>
          <u-checkbox-group
            v-if="item.questionType === 'M'"
            v-model="item.userAnswer"
            placement="column"
            @change="fetchQuestionAnswer(item)"
          >
            <u-checkbox
              :customStyle="{ marginBottom: '10px' }"
              v-for="choice in 11"
              v-if="item[`item${choice}`]"
              :key="choice"
              :label="item[`item${choice}`]"
              :name="String.fromCharCode(64 + choice)"
            >
            </u-checkbox>
          </u-checkbox-group>
        </view>
        <button class="subminButton" @click="submitQusetionnaire">提 交</button>
      </view>
    </view>
    <yangr-msg v-if="yangrMsgShow" :title="title" :type="type"></yangr-msg>
  </view>
</template>

<script>
  import { getQuestionList } from "@/api/course/question"
  import { getQuestionById, submit } from "@/api/course/questionnaire"
  import { addAnswer } from "@/api/course/question-answer"
  import { getExamRange } from "@/api/exam/base"
  import { mapGetters } from "vuex"

  export default {
    onLoad(options) {
      if (
        this.domainName === "fzjz" ||
        this.domainName === "zhzg" ||
        this.domainName === "yuyuan"
      ) {
        this.showNavBar = false
      }
      this.questionnaireId = options.questionnaireId
      this.questionnaireIssuedId = options.questionnaireIssuedId
      this.userType = options.userType
      this.fetchQuestionData()
      this.fetchQuestionnaireData()
    },

    data() {
      return {
        questionnaireId: "",
        questionnaireIssuedId: "",
        userType: "",
        surveyList: [],
        questionnaireData: [],
        showNavBar: true,
        title: "",
        type: "",
        yangrMsgShow: false
      }
    },

    computed: {
      ...mapGetters({
        domainName: "domainName"
      })
    },

    methods: {
      async fetchQuestionData() {
        let queryData = {
          questionnaireId: this.questionnaireId
        }

        const { data } = await getQuestionList(queryData)
        this.surveyList = data
        this.surveyList.forEach(item => {
          if (item.questionType === "S" || item.questionType === "Q") {
            item.userAnswer = ""
          } else if (item.questionType === "M") {
            item.userAnswer = []
          }
        })
      },

      async fetchQuestionnaireData() {
        const { data } = await getQuestionById(this.questionnaireId)
        this.questionnaireData = data
      },

      // 每选一题发送一次
      fetchQuestionAnswer(item) {
        if (!item.userAnswer) return
        this.$nextTick(async () => {
          let queryData = {
            questionId: item.traningQuestionId,
            questionnaireIssuedId: this.questionnaireIssuedId,
            userAnswer: Array.isArray(item.userAnswer) ? item.userAnswer.join() : item.userAnswer
          }
          await addAnswer(queryData)
        })
      },

      async submitQusetionnaire() {
        try {
          this.surveyList.forEach(item => {
            // 问答题暂不校验
            if (item.questionType === "Q") {
              return
            }
            if (!item.userAnswer || item.userAnswer.length === 0) {
              throw new Error("问卷未完成")
            }
          })
        } catch (error) {
          this.title = "请确保每道题都填写完整"
          this.type = "error"
          this.yangrMsgShow = true
          setTimeout(() => {
            this.yangrMsgShow = false
          }, 2000)
          return
        }
        let queryData = {
          questionnaireIssuedId: this.questionnaireIssuedId
        }
        submit(queryData)
          .then(res => {
            if (this.domainName === "fzjz") {
              this.title = "问卷已提交"
              this.type = "success"
              this.yangrMsgShow = true
            } else if (this.domainName === "zhzg" || this.domainName === "yuyuan") {
              this.title = "问卷已完成！"
              this.type = "success"
              this.yangrMsgShow = true
              setTimeout(async () => {
                try {
                  const res = await getExamRange({ userType: this.userType })
                  if (res.code === 200 && res.data) {
                    const { baseId, arrangeId } = res.data
                    uni.navigateTo({
                      url: `/pages/mine/exam/prepare/index?baseId=${baseId}&arrangeId=${arrangeId}&showNavBar=false`
                    })
                  } else {
                    this.$modal.msgError("获取考试信息失败，请稍后再试")
                  }
                } catch (error) {
                  console.error("Error fetching exam range:", error)
                  this.$modal.msgError("获取考试信息异常，请稍后再试")
                }
              }, 2000)
            }
            // else if (this.domainName === "yuyuan") {
            //   this.title = "问卷已提交，即将跳转至考试页面"
            //   this.type = "success"
            //   this.yangrMsgShow = true
            //   setTimeout(() => {
            //     uni.navigateTo({
            //       url: `/pages/mine/exam/prepare/index?baseId=2084&arrangeId=2396&showNavBar=false`
            //     })
            //   }, 2000)
            // }
            else {
              uni.showToast({
                title: "感谢您的提交",
                icon: "success",
                success: () => {
                  setTimeout(() => {
                    uni.navigateTo({
                      url: "/pages/mine/my-questionnaire/index"
                    })
                  }, 2000)
                }
              })
            }
          })
          .catch(err => {
            if (
              this.domainName === "fzjz" ||
              this.domainName === "zhzg" ||
              this.domainName === "yuyuan"
            ) {
              this.title = "问卷提交失败，请稍后再试"
              this.type = "success"
              this.yangrMsgShow = true
              setTimeout(() => {
                this.yangrMsgShow = false
              }, 2000)
            } else {
              uni.showToast({
                title: "提交失败，请稍后再试",
                icon: "error",
                success: () => {
                  setTimeout(() => {
                    uni.navigateTo({
                      url: "/pages/mine/my-questionnaire/index"
                    })
                  }, 2000)
                }
              })
            }
          })
      }
    }
  }
</script>

<style lang="scss" scoped>
  ::v-deep uni-toast {
    font-size: 26px;
  }
  .survey {
    .survey-body {
      background-color: white;
      width: 95%;
      margin: 10px auto;
      padding: 0 20px;

      .survey-title {
        display: flex;
        flex-direction: column;
        padding: 10px 0;
        border-bottom: 1px solid #eeeeee;
        :first-child {
          text-align: center;
          font-size: 30px;
          margin-bottom: 20px;
        }
        :last-child {
          color: #666666;
          font-size: 18px;
        }
      }
      .zhzg-tips {
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #f40;
      }

      .survey-content {
        display: flex;
        flex-direction: column;
        padding: 20rpx 0 80rpx;

        .survey-content-list {
          margin-bottom: 30px;

          .survey-content-list-title {
            margin-bottom: 20px;
            font-size: 16px;
          }

          .survey-content-list-radio {
            font-size: 14px;
            margin-bottom: 10px;
          }
        }
      }
    }
  }

  .subminButton {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 70rpx;
    width: 250rpx;
    font-size: 32rpx;
    background-color: #409eff;
    color: #fff;
  }
</style>

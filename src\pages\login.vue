<!--
 * @Description: 登录页面
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-06-25 17:09:38
 * @LastEditTime: 2025-06-19 10:24:29
-->
<template>
  <view>
    <RankLogin v-if="unepTenantList.includes(domainName)" />
    <TownLogin v-else-if="domainName === 'town'" />
    <CjaqLogin v-else-if="domainName === 'cjaq'" />
    <FzjzLogin v-else-if="domainName === 'fzjz'" />
    <AqscLogin v-else-if="domainName === 'aqsc'" />
    <EduxdLogin v-else-if="domainName === 'eduxd'" />
    <ZhzgLogin v-else-if="domainName === 'zhzg'" />
    <YuyuanLogin v-else-if="domainName === 'yuyuan'" />
    <YygfLogin v-else-if="domainName === 'yygf'" />
    <UnepLogin v-else-if="domainName === 'unep'" />
    <JxyjLogin v-else-if="domainName === 'jxyj'" />
    <CommonLogin v-else />
  </view>
</template>

<script>
  import CommonLogin from "@/pages/commonLogin"
  import RankLogin from "@/pages/rankLogin"
  import TownLogin from "@/pages/town/townLogin"
  import CjaqLogin from "@/pages/cjaq/cjaqLogin"
  import FzjzLogin from "@/pages/fzjz/fzjzLogin"
  import AqscLogin from "@/pages/aqsc/aqscLogin"
  import EduxdLogin from "@/pages/eduxd/eduxdLogin"
  import ZhzgLogin from "@/pages/zhzg/zhzgLogin"
  import YuyuanLogin from "@/pages/yuyuan/yuyuanLogin"
  import YygfLogin from "@/pages/yygf/yygfLogin"
  import UnepLogin from "@/pages/unep/unepLogin"
  import JxyjLogin from "@/pages/jxyj/jxyjLogin"
  import { mapGetters } from "vuex"
  import { unepTenantList } from "@/utils/constant.js"

  export default {
    components: {
      CommonLogin,
      RankLogin,
      TownLogin,
      CjaqLogin,
      FzjzLogin,
      AqscLogin,
      EduxdLogin,
      ZhzgLogin,
      YuyuanLogin,
      YygfLogin,
      UnepLogin,
      JxyjLogin
    },
    computed: {
      ...mapGetters({
        domainName: "domainName"
      })
    },
    created() {},
    data() {
      return {
        unepTenantList
      }
    }
  }
</script>

<style lang="scss"></style>

/*
 * @Description: 华为云上传
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-03-12 11:11:07
 * @LastEditTime: 2024-09-19 15:41:45
 */
const OBSPlugin = require("huawei-obs-plugin")
const path = require("path")
const fs = require("fs")
const [bucket1, bucket2] = process.argv.splice(2)

// 将/public下的txt文件复制粘贴至dist文件目录下
const files = fs.readdirSync("./public")
files.forEach(file => {
  if (file.endsWith(".txt")) {
    fs.copyFileSync(
      path.resolve(__dirname, "./public", file),
      path.resolve(__dirname, "./dist/build/h5", file)
    )
    console.log(`复制文件 ${file}`)
  }
})

// 上传至华为云OBS
new OBSPlugin({
  accessKeyId: "I5CROPRZ7WFRHNCILVMP",
  secretAccessKey: "lcr5k2ereEVEvjp0TQAgmIeugQGHHLz680n2NeoM",
  server: "https://obs.cn-east-3.myhuaweicloud.com",
  bucket: bucket1,
  exclude: null,
  deleteAll: true,
  output: path.resolve(__dirname, "./dist/build/h5"),
  local: true
}).upload()

// 上传至华为云OBS
new OBSPlugin({
  accessKeyId: "I5CROPRZ7WFRHNCILVMP",
  secretAccessKey: "lcr5k2ereEVEvjp0TQAgmIeugQGHHLz680n2NeoM",
  server: "https://obs.cn-east-3.myhuaweicloud.com",
  bucket: bucket2,
  exclude: null,
  deleteAll: true,
  output: path.resolve(__dirname, "./dist/build/h5"),
  local: true
}).upload()

/*
 * @Description: 证书相关接口
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-12-16 14:40:28
 * @LastEditTime: 2024-12-16 16:05:13
 */
import request from "@/utils/request"

// 查询证书列表
export function certificateList(params) {
  return request({
    url: "/devops/template/myList",
    method: "get",
    params
  })
}

// 查询证书详情
export function templateDetail(params) {
  if (!params.certType) {
    // 无证书类型时
    return request({
      url: `/devops/template/getTemplateData/${params.certificateId}`,
      method: "get"
    })
  } else {
    // 有证书类型时
    return request({
      url: `/devops/template/getTemplateData/${params.certificateId}/${params.certType}`,
      method: "get"
    })
  }
}

// 证书图片
// body对象里面需要：
// certDataList(/getTemplateData/2023/c返回的数组)
// studentUserId
// taskId
export function getCertPicture(templateImgUrl, data) {
  return request({
    url: `/course/record/getCertPicture?templateImgUrl=${templateImgUrl}`,
    method: "post",
    data
  })
}

@mixin fixed($l:0, $t:0, $r:auto, $b:auto) { 
	position: fixed;
	left: $l;
	top: $t;
	right: $r;
	bottom: $b;
}
.ray-msg-mask{
	width: 100%;
	height: 100%;
	@include fixed();
	z-index: 9999;
	background: rgba(0,0,0,0.60); 
}
.ray-msg-content{
	width: 590rpx;
	height: auto;
	margin: auto;
	padding: 60rpx;
	@include fixed(50%, 50%);
	z-index: 10000;
	transform: translate(-50%, -50%);
	border-radius: 28rpx;
	background-color: #fff;
	box-sizing: border-box;
	text-align: center;
}
.ray-msg-icon{
	display: inline-block;
	width: 152rpx;
	height: 152rpx;
	mask: url(https://static.jiaoyubao.cn/images/common/icon-check-block.svg) no-repeat;
	mask-size: 100% 100%;
	background-color: #53b883;
	&.ray-msg-error{
		mask: url(https://static.jiaoyubao.cn/images/common/icon-cross-block.svg) no-repeat;
		background-color: #fe1940;
	}
}
.ray-msg-title{
	margin: 40rpx auto 0;
	font-size: 40rpx;
	line-height: 56rpx;
	color: #000000;
}
.fixed-msg-info{
	margin-top: 30rpx;
	padding: 0 12rpx;
	font-size: 28rpx;
	line-height: 40rpx;
	color: #8c8c8c;
}
.ray-msg-btn{
	width: 100%;
	height: 88rpx;
	margin: 60rpx auto 0;
	background: #1a8cff;
	border-radius: 44rpx;
	font-size: 34rpx;
	line-height: 88rpx;
	color: #fff;
	&.ray-msg-error{
		background: #fe1940;
	}
}
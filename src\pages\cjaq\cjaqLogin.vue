<!--
 * @Description: 工会登录页
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-11-09 11:12:27
 * @LastEditTime: 2024-01-15 11:39:08
-->

<template>
  <view class="normal-login-container">
    <image src="/static/images/competition/logbg_1.png" mode="widthFix" style="width: 100%"></image>
    <view class="logincon">
      <!-- <image
        class="tenant-icon"
        :src="
          domainInfo.tenantIcon ||
          'https://training-voc.obs.cn-north-4.myhuaweicloud.com/town_logo.png'
        "
        mode="heightFix"
      ></image> -->
      <view class="title"> 工会促进安全生产学习平台 </view>
      <!-- <u-tabs
        class="tab"
        style="width: 100%; margin-top: 20rpx"
        :list="tabList"
        @change="tabChange"
        :current="activeTab"
        :scrollable="false"
      ></u-tabs> -->
      <view class="bgcon">
        <view class="login-form-content">
          <!-- 企业登录 -->
          <template v-if="activeTab === '0'">
            <view class="input-item flex align-center">
              <view class="iconfont icon-user icon"></view>
              <input
                v-model="loginForm.username"
                class="input"
                type="text"
                placeholder="请输入账号"
                maxlength="30"
              />
            </view>
            <view class="input-item flex align-center">
              <view class="iconfont icon-password icon"></view>
              <input
                v-model="loginForm.password"
                type="password"
                class="input"
                placeholder="请输入密码"
                maxlength="20"
              />
            </view>
          </template>
          <!-- 个人登录 -->
          <template v-else>
            <view class="input-item flex align-center">
              <view class="iconfont icon-user icon"></view>
              <input
                v-model="loginForm.mobile"
                class="input"
                type="text"
                @focus="fphMobile"
                @blur="bphMobile"
                :placeholder="mobileHolder"
                maxlength="11"
                @input="checkCode"
              />
            </view>
            <view class="input-item flex align-center">
              <view class="iconfont icon-password icon"></view>
              <input
                v-model="loginForm.code"
                class="input"
                @focus="fphCode"
                @blur="bphCode"
                :placeholder="codeHolder"
                maxlength="20"
              />
              <view
                @click="getCode"
                style="width: 260rpx; margin-right: 20rpx; color: #3b7ffc"
                :class="{ active: !canCode }"
              >
                {{ codeMsg }}
              </view>
            </view>
          </template>

          <view class="action-btn">
            <button @click="handleLogin" class="login-btn cu-btn block bg-blue lg round">
              立即登录
            </button>
          </view>
        </view>

        <view class="xieyi text-center" v-if="activeTab === '2'">
          <text class="text-grey1">未注册的手机号将默认开通平台账号</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
  import { isContactWay } from "@/utils/validate"
  import { sendCode } from "@/api/competition/index"
  import { mapGetters } from "vuex"

  export default {
    data() {
      return {
        loginForm: {
          mobile: "",
          code: ""
        },
        canCode: false,
        codeMsg: "获取验证码",
        interval: null,
        mobileHolder: "手机号码",
        codeHolder: "短信验证码",
        startTime: null,
        endTime: null,
        tabList: [
          {
            name: "企业登录",
            value: "0"
          },
          {
            name: "个人登录",
            value: "1"
          }
        ],
        activeTab: "0"
      }
    },
    created() {},
    computed: {
      ...mapGetters({
        domainInfo: "domainInfo"
      })
    },
    methods: {
      fphMobile() {
        this.mobileHolder = ""
      },
      bphMobile() {
        this.mobileHolder = "手机号码"
      },
      fphCode() {
        this.codeHolder = ""
      },
      bphCode() {
        this.codeHolder = "短信验证码"
      },
      // 判断验证码是否可点击
      checkCode(e) {
        let val = e.detail.value
        if (val && isContactWay(val)) {
          this.canCode = true
        } else {
          this.canCode = false
        }
      },
      // 获取验证码
      async getCode() {
        if (this.canCode) {
          let queryData = {
            mobile: this.loginForm.mobile,
            scene: 1
          }
          await sendCode(queryData)
          this.$modal.msgSuccess("验证码发送成功！")
          clearInterval(this.interval)
          this.canCode = false
          this.sec = 60
          this.codeMsg = this.sec + "s后重试"
          this.interval = setInterval(() => {
            this.sec -= 1
            this.codeMsg = this.sec + "s后重试"
            if (this.sec === 0) {
              this.codeMsg = "重新获取"
              this.canCode = true
              clearInterval(this.interval)
            }
          }, 1000)
        }
      },
      // 登录方法
      async handleLogin() {
        if (this.activeTab === "0") {
          if (this.loginForm.username === "") {
            this.$modal.msgError("请输入您的账号")
          } else if (this.loginForm.password === "") {
            this.$modal.msgError("请输入您的密码")
          } else {
            this.$modal.loading("登录中，请耐心等待...")
            this.pwdLogin()
          }
        } else {
          if (this.loginForm.mobile === "") {
            this.$modal.msgError("请输入您的手机号")
          } else if (this.loginForm.code === "") {
            this.$modal.msgError("请输入您的验证码")
          } else {
            this.$modal.loading("登录中，请耐心等待...")
            this.personalLogin()
          }
        }
      },
      // 企业登录
      async pwdLogin() {
        this.$store.dispatch("Login", this.loginForm).then(() => {
          this.$modal.closeLoading()
          this.loginSuccess()
        })
      },
      // 登录成功后，处理函数
      loginSuccess() {
        // 设置用户信息
        this.$store.dispatch("GetInfo").then(res => {
          this.$tab.reLaunch("/pages/cjaq/index")
        })
      },
      // 个人登录
      personalLogin() {
        this.loginForm.password = this.loginForm.code
        this.$store.dispatch("unepLogin", this.loginForm).then(() => {
          this.$modal.closeLoading()
          this.loginSuccess()
        })
      },
      tabChange(item) {
        this.activeTab = item.value
      }
    }
  }
</script>

<style lang="scss">
  page {
    background-color: #ffffff;
  }
  .bgcon {
    width: 100%;
    height: calc(100vh - 150px);
    background: url("/static/images/competition/logbg_3.png") no-repeat;
    background-size: 100% auto;
    background-position: 0 -60px;
  }
  .active {
    color: #888 !important;
  }
  .normal-login-container {
    width: 100%;
    height: 100vh;
    overflow: hidden;
    .logo-content {
      image {
        border-radius: 4px;
      }

      .title {
        margin-top: 10px;
        font-size: 18px;
        font-weight: 600;
      }

      .tab {
        width: 100%;
        margin-top: 10px;
      }
    }

    .login-form-content {
      text-align: center;
      margin: 10px auto 10px auto;
      width: 80%;

      .input-item {
        margin: 20px auto;
        background-color: #f5f6f7;
        height: 45px;
        border-radius: 20px;
        background: #fff;
        box-shadow: 0 0 10rpx rgba(0, 0, 0, 0.1);
        .icon {
          font-size: 38rpx;
          margin-left: 10px;
          color: #999;
        }

        .input {
          width: 100%;
          font-size: 16px;
          line-height: 20px;
          text-align: left;
          padding-left: 15px;
        }
      }

      .login-btn {
        background-color: #f59a23;
        margin-top: 40px;
        height: 45px;
      }

      .xieyi {
        color: #333;
        font-size: 14px;
      }

      .login-code {
        height: 38px;
        float: right;

        .login-code-img {
          height: 38px;
          position: absolute;
          margin-left: 10px;
          width: 200rpx;
        }
      }
    }
  }

  .xieyi {
    font-size: 12px;
  }

  .logincon {
    margin-top: 20rpx;
    display: flex;
    justify-content: center;
    flex-direction: column;
    width: 100%;
    align-items: center;
    .tenant-icon {
      height: 150rpx;
    }
    .title {
      margin: 20rpx 0 40rpx 0;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #ee1d23;
      font-size: 42rpx;
      font-weight: 700;
    }
  }
</style>

<!--
 * @Description: navigationBar
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-08-08 11:28:51
 * @LastEditTime: 2023-10-18 16:33:30
-->
<template>
  <view class="navigationBar">
    <div class="left">
      <view class="iconfont icon-fanhui icon" @click="back"></view>
      <view class="info">
        <view class="iconfont icon-shichang icon"></view>
      </view>
      <view class="text">学习课堂</view>
    </div>
    <view class="right" @click="pushToPersonal" v-if="showPersonal">个人中心</view>
  </view>
</template>

<script>
  export default {
    name: "navigationBar",
    props: {
      path: {
        type: String,
        required: true
      },
      showPersonal: {
        type: Boolean,
        default: true
      }
    },
    data() {
      return {}
    },
    created() {},
    methods: {
      back() {
        if (this.path === "back") {
          uni.navigateBack({
            delta: 1
          })
        } else if (this.path.startsWith('/pages/')) {
          uni.redirectTo({
            url: this.path
          })
        } else {
          uni.navigateBack({
            delta: 1
          })
        }
      },
      pushToPersonal() {
        uni.navigateTo({
          url: "/pages/mine/index"
        })
      }
    }
  }
</script>

<style lang="scss">
  .navigationBar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 90rpx;
    width: 100%;
    background-color: #fff;

    .left,
    .right {
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .left {
      margin-left: 50rpx;

      .iconfont {
        font-weight: bold;
      }

      .info {
        margin-left: 20rpx;
        width: 60rpx;
        height: 60rpx;
        display: flex;
        background-color: #c7dbf7;
        border-radius: 50%;
        justify-content: center;
        align-items: center;

        .icon-shichang {
          font-size: 38rpx;
          color: #7daef3;
        }
      }

      .text {
        margin-left: 15rpx;
        font-weight: bold;
      }
    }

    .right {
      margin-right: 70rpx;
      font-size: 25rpx;
      color: grey;
    }
  }
</style>

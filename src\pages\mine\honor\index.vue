<!--
 * @Description: 我的荣誉页面
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-12-16 14:31:25
 * @LastEditTime: 2024-12-27 17:01:54
-->

<template>
  <view class="min-h-screen bg-gray-50">
    <navigationBar path="/pages/mine/index" />

    <!-- 证书标题 -->
    <view class="px-4 pt-3">
      <view class="text-base font-medium text-blue-500">我的证书</view>
    </view>

    <!-- 证书列表 -->
    <view v-show="certificateList.length" class="p-4">
      <view class="grid-cols-2 gap-4">
        <view
          @click="viewDetail(item)"
          v-for="(item, index) in certificateList"
          :key="index"
          class="bg-white rounded-lg overflow-hidden shadow-md p-3"
        >
          <!-- 证书图片容器 -->
          <view class="relative w-full aspect-[4/3] bg-blue-50">
            <image :src="item.templateImg" class="w-full h-full object-contain" mode="aspectFit" />
          </view>
          <!-- 证书信息 -->
          <view class="mt-2">
            <view class="flex items-center justify-between">
              <view class="flex flex-col">
                <text class="text-base text-gray-900">{{ item.templateName }}</text>
                <text class="text-sm text-gray-400">{{ item.issueDate }}</text>
              </view>
              <text
                class="text-sm text-blue-500 cursor-pointer min-w-7"
                @click.stop="handleDetail(item)"
              >
                详情
              </text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <xw-empty :isShow="!certificateList.length" text="暂无证书" textColor="#777777"></xw-empty>
  </view>
</template>

<script>
  import { certificateList, templateDetail, getCertPicture } from "@/api/devops/honor"
  import xwEmpty from "@/components/xw-empty/xw-empty"

  export default {
    components: {
      xwEmpty
    },

    data() {
      return {
        certificateList: [],
        pageNum: 1,
        pageSize: 10,
        total: 0
      }
    },

    onLoad() {
      this.getCertificateList()
    },

    onReachBottom() {
      if (this.certificateList.length < this.total) {
        this.pageNum++
        this.getCertificateList()
      }
    },

    methods: {
      // 获取证书列表
      async getCertificateList() {
        try {
          const params = {
            pageNum: this.pageNum,
            pageSize: this.pageSize
          }
          const { rows, total } = await certificateList(params)
          this.total = total
          if (this.pageNum === 1) {
            this.certificateList = rows
          } else {
            this.certificateList = [...this.certificateList, ...rows]
          }
        } catch (error) {
          console.error("获取证书列表失败:", error)
        }
      },

      async handleDetail(item) {
        try {
          const { code, data } = await templateDetail(item)

          if (code !== 200 || !data?.length) return

          const firstItem = data[0]
          const params = {
            courseId: firstItem.courseId,
            examId: firstItem.examId,
            taskId: firstItem.taskId,
            userId: this.$store.state.user.userId,
            showNavBar: true
          }

          // 构建查询字符串
          const queryString = Object.entries(params)
            .filter(([, value]) => value) // 过滤掉空值
            .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
            .join("&")

          uni.navigateTo({
            url: `/pages/white/certDetail?${queryString}`
          })
        } catch (error) {
          console.error("获取证书详情失败:", error)
        }
      },

      // 查看证书图片
      async viewDetail(item) {
        if (!item.templateImg) return

        try {
          // 1. 获取证书详情数据
          const { code, data } = await templateDetail(item)

          if (code !== 200 || !data?.length) return

          // 2. 准备getCertPicture接口所需参数
          const requestData = {
            certDataList: data,
            studentUserId: this.$store.state.user.userId,
            taskId: data[0].taskId
          }

          // 3. 调用getCertPicture获取完整证书图片
          const response = await getCertPicture(item.templateImg, requestData)

          // 4. 检查响应并处理base64图片数据
          if (response.code === 200 && response.data) {
            // 确保base64数据包含正确的前缀
            const base64Image = response.data.startsWith("data:image/")
              ? response.data
              : `data:image/png;base64,${response.data}`

            // 预览证书图片
            uni.previewImage({
              urls: [base64Image],
              current: 0
            })
          } else {
            throw new Error("获取证书图片失败")
          }
        } catch (error) {
          console.error("获取证书图片失败:", error)
          uni.showToast({
            title: "获取证书图片失败",
            icon: "none"
          })
        }
      }
    }
  }
</script>

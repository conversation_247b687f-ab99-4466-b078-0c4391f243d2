<!--
 * @Description: 问答弹窗
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-07-09 16:01:31
 * @LastEditTime: 2024-12-12 13:26:13
-->
<template>
  <uni-popup ref="popup" type="center" :mask-click="false">
    <view class="question-container">
      <view class="question-header">
        <text class="title">课堂问答</text>
        <text v-if="showCountdown" class="countdown">
          剩余时间：{{ Math.floor(countdown / 60) }}:{{
            (countdown % 60).toString().padStart(2, "0")
          }}
        </text>
      </view>

      <view class="question-content">
        <view class="question-type"> 【{{ questionTypeText }}题】 </view>
        <view class="question-name">{{ qsInfo.questionName }}</view>

        <view class="options">
          <!-- 单选题 -->
          <radio-group
            v-if="qsInfo.questionType === 'S' || qsInfo.questionType === 'J'"
            @change="radioChange"
            class="radio-group"
          >
            <label
              class="radio-item"
              v-for="choice in 11"
              :key="choice"
              v-if="qsInfo['item' + choice]"
            >
              <radio
                :value="String.fromCharCode(64 + choice)"
                :disabled="isShowAnalysis"
                :checked="answerValue === String.fromCharCode(64 + choice)"
              />
              <text class="radio-text"
                >{{ String.fromCharCode(64 + choice) }}. {{ qsInfo["item" + choice] }}</text
              >
            </label>
          </radio-group>

          <!-- 多选题 -->
          <checkbox-group
            v-if="qsInfo.questionType === 'M'"
            @change="checkboxChange"
            class="checkbox-group"
          >
            <label
              class="checkbox-item"
              v-for="choice in 11"
              :key="choice"
              v-if="qsInfo['item' + choice]"
            >
              <checkbox
                :value="String.fromCharCode(64 + choice)"
                :disabled="isShowAnalysis"
                :checked="answerValue.includes(String.fromCharCode(64 + choice))"
              />
              <text class="checkbox-text"
                >{{ String.fromCharCode(64 + choice) }}. {{ qsInfo["item" + choice] }}</text
              >
            </label>
          </checkbox-group>
        </view>

        <!-- 答案解析 -->
        <view class="analysis" v-if="isShowAnalysis">
          <view class="answer">
            <text class="label">【答案】：</text>
            <text>{{ qsInfo.answer }}</text>
          </view>
          <view class="explanation" v-if="qsInfo.analysis">
            <text class="label">【分析】：</text>
            <text>{{ qsInfo.analysis }}</text>
          </view>
        </view>
      </view>

      <view class="footer">
        <button v-if="!isShowAnalysis" class="submit-btn" @tap="handleSubmit">提交</button>
        <button v-else class="continue-btn" @tap="handleClose(false)">继续观看</button>
      </view>
    </view>
  </uni-popup>
</template>

<script>
  import { getQuestion } from "@/api/exam/question-answer.js"
  import { addAnswer } from "@/api/course/question-answer.js"

  export default {
    name: "AnswerDialog",
    data() {
      return {
        visible: false,
        qsInfo: {},
        answerValue: "",
        isShowAnalysis: false,
        countdown: 0,
        countdownTimer: null,
        showCountdown: false
      }
    },
    computed: {
      questionTypeText() {
        const typeMap = {
          S: "单选",
          M: "多选",
          J: "判断"
        }
        return typeMap[this.qsInfo.questionType] || ""
      }
    },
    methods: {
      async openDialog(qsItem) {
        try {
          const res = await getQuestion(qsItem.questionId)
          this.qsInfo = res.data
          // 重置答案
          this.answerValue = this.qsInfo.questionType === "M" ? [] : ""
          this.isShowAnalysis = false

          // 设置倒计时
          if (qsItem.countdownDuration) {
            this.countdown = qsItem.countdownDuration
            this.showCountdown = true
            this.startCountdown()
          } else {
            this.showCountdown = false
          }

          this.$refs.popup.open()
          this.visible = true
        } catch (error) {
          console.error("获取题目信息失败:", error)
        }
      },

      startCountdown() {
        clearInterval(this.countdownTimer)
        this.countdownTimer = setInterval(() => {
          this.countdown--
          if (this.countdown <= 0) {
            clearInterval(this.countdownTimer)
            this.handleTimeUp()
          }
        }, 1000)
      },

      async handleTimeUp() {
        // 先关闭答题弹窗
        this.handleClose(false, true)

        // 显示提示框
        uni.showModal({
          title: "提示",
          content: "超时未答题，当前章节学习记录已清空",
          showCancel: false,
          success: async () => {
            // 提交空答案
            const submitData = {
              userId: this.$store.state.user.userId,
              questionId: this.qsInfo.questionId,
              userAnswer: "",
              remark: this.qsInfo.passageId
            }
            try {
              await addAnswer(submitData)
            } catch (error) {
              console.error("提交答案失败:", error)
            }
          }
        })
      },

      radioChange(e) {
        this.answerValue = e.detail.value
      },

      checkboxChange(e) {
        this.answerValue = e.detail.value
      },

      async handleSubmit() {
        if (!this.answerValue || (Array.isArray(this.answerValue) && this.answerValue.length === 0)) {
          uni.showToast({
            title: '请选择你的答案',
            icon: 'none'
          })
          return
        }

        let answerData = Array.isArray(this.answerValue)
          ? this.answerValue.sort().join(',')
          : this.answerValue

        // 提交答案
        const submitData = {
          userId: this.$store.state.user.userId,
          questionId: this.qsInfo.questionId,
          userAnswer: answerData,
          remark: this.qsInfo.passageId
        }

        try {
          await addAnswer(submitData)

          // 判断答题是否正确
          if (answerData === this.qsInfo.answer) {
            // 答对了，显示提示并继续学习
            uni.showModal({
              title: '提示',
              content: '恭喜您，回答正确！',
              showCancel: false,
              success: () => {
                this.handleClose(true)
              }
            })
          } else {
            // 答错了，显示提示、显示解析并清空进度
            uni.showModal({
              title: '提示',
              content: '回答错误，当前章节学习记录已清空',
              showCancel: false,
              success: () => {
                this.isShowAnalysis = true
                this.handleClose(false, true)
              }
            })
          }
        } catch (error) {
          console.error('提交答案失败:', error)
          uni.showToast({
            title: '提交答案失败，请重试',
            icon: 'none'
          })
        }
      },

      handleClose(flag, needReset = false) {
        if (this.countdownTimer) {
          clearInterval(this.countdownTimer)
        }
        this.$emit("done", flag, needReset)
        this.answerValue = this.qsInfo.questionType === "M" ? [] : ""
        this.visible = false
        this.isShowAnalysis = false
        this.showCountdown = false
        this.$refs.popup.close()
      }
    },
    beforeDestroy() {
      if (this.countdownTimer) {
        clearInterval(this.countdownTimer)
      }
    }
  }
</script>

<style lang="scss" scoped>
  .question-container {
    background-color: #fff;
    border-radius: 24rpx;
    width: 90vw;
    max-height: 80vh;
    overflow-y: auto;

    .question-header {
      padding: 30rpx;
      border-bottom: 1rpx solid #eee;
      position: relative;

      .title {
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
      }

      .countdown {
        position: absolute;
        right: 30rpx;
        top: 30rpx;
        color: #f56c6c;
        font-size: 28rpx;
        font-weight: bold;
      }
    }

    .question-content {
      padding: 30rpx;

      .question-type {
        font-size: 30rpx;
        color: #12aef4;
        font-weight: bold;
        margin-bottom: 20rpx;
      }

      .question-name {
        font-size: 30rpx;
        color: #333;
        line-height: 1.6;
        margin-bottom: 30rpx;
      }

      .options {
        .radio-group,
        .checkbox-group {
          display: flex;
          flex-direction: column;
        }

        .radio-item,
        .checkbox-item {
          margin-bottom: 20rpx;
          display: flex;
          align-items: flex-start;
          padding: 20rpx;

          .radio-text,
          .checkbox-text {
            margin-left: 20rpx;
            font-size: 28rpx;
            line-height: 1.5;
          }
        }
      }

      .analysis {
        margin-top: 30rpx;
        border-top: 1rpx solid #eee;
        padding-top: 30rpx;

        .answer,
        .explanation {
          margin-bottom: 20rpx;
          font-size: 28rpx;
          line-height: 1.5;

          .label {
            color: #12aef4;
            font-weight: bold;
          }
        }
      }
    }

    .footer {
      padding: 30rpx;
      border-top: 1rpx solid #eee;
      display: flex;
      justify-content: center;

      button {
        width: 240rpx;
        height: 80rpx;
        line-height: 80rpx;
        border-radius: 40rpx;
        font-size: 28rpx;

        &.submit-btn {
          background-color: #12aef4;
          color: #fff;
        }

        &.continue-btn {
          background-color: #67c23a;
          color: #fff;
        }
      }
    }
  }
</style>

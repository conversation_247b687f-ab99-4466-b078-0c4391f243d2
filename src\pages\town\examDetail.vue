<!--
 * @Description: 北蔡防灾减灾-考试详情
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-12-20 14:33:23
 * @LastEditTime: 2023-12-20 14:37:11
-->
<template>
  <view class="con">
    <navigationBar
      :path="`/pages/town/exam?baseId=${this.baseId}&arrangeId=${this.arrangeId}`"
      :showPersonal="false"
    />
    <view class="exam-item" v-for="(item, index) in questionList" :key="item.questionId">
      <view class="title">第{{ index + 1 }} 题</view>
      <view class="topic">
        <text class="type">
          <text class="type-con">{{ processType(item.questionType) }}</text>
        </text>
        <text class="content">{{ item.questionName }}</text>
        <text class="score">
          <text v-if="item.userQuestionScore" class="getscore"
            >（{{ item.userQuestionScore }}分）</text
          >
          <text v-else class="noscore">（0分）</text>
        </text>
      </view>
      <view class="option">
        <view v-for="mindex in 11" :key="mindex">
          <view v-if="item[`item${mindex}`]">
            <image
              v-if="isCorrect(item, String.fromCharCode(64 + mindex))"
              class="mark"
              src="/static/images/competition/correct.png"
            />
            <image
              v-else-if="isWrong(item, String.fromCharCode(64 + mindex))"
              class="mark"
              src="/static/images/competition/error.png"
            />
            <text v-else class="circle">{{ String.fromCharCode(64 + mindex) }}</text>
            <text>{{ item[`item${mindex}`] }}</text>
          </view>
        </view>
      </view>
      <view class="answer">
        <text
          style="
            display: inline-block;
            width: 3px;
            height: 20px;
            border-radius: 2px;
            transform: translateY(5px);
            margin-right: 8px;
            background: #04c877;
          "
        ></text>
        <text style="width: 200rpx; display: inline-block"> 答案：{{ item.answer }}</text>
        <text
          style="
            display: inline-block;
            width: 3px;
            height: 20px;
            border-radius: 2px;
            transform: translateY(5px);
            margin-right: 8px;
            background: #3ca7fa;
          "
        ></text>
        <text>我的答案：{{ item.userAnswer }}</text>
      </view>
    </view>
  </view>
</template>

<script>
  import { completedPaperDetail } from "@/api/competition/index"

  export default {
    data() {
      return {
        questionList: [],
        baseId: "",
        arrangeId: ""
      }
    },

    onLoad(option) {
      this.baseId = option.baseId
      this.arrangeId = option.arrangeId
      completedPaperDetail({
        baseId: option.baseId,
        paperAnswerId: option.paperAnswerId,
        arrangeId: option.arrangeId
      }).then(res => {
        let topicList = res.data.paperTactics
        let quesList = []
        for (let item of topicList) {
          for (let mitem of item.examQuestions) {
            quesList.push(mitem)
          }
        }
        this.questionList = quesList
      })
    },
    methods: {
      // 正确的选项
      isCorrect(item, choice) {
        if (item.questionType === "S" || item.questionType === "J") {
          return item.answer === choice
        } else if (item.questionType === "M") {
          if (item.answer.includes(",")) {
            return item.answer.split(",").indexOf(choice) > -1
          }
          return item.answer === choice
        }
        return false
      },

      // 错误的选项
      isWrong(item, choice) {
        if (!item.userAnswer) return false
        if (item.questionType === "S" || item.questionType === "J") {
          return item.userAnswer === choice && item.userAnswer !== item.answer
        } else if (item.questionType === "M") {
          return item.userAnswer.includes(choice) && !item.answer.includes(choice)
        }
        return false
      },
      processType(type) {
        if (type === "S") {
          return "单选"
        } else if (type === "M") {
          return "多选"
        } else if (type === "J") {
          return "判断"
        }
      }
    }
  }
</script>

<style lang="scss" scoped>
  .mark {
    width: 22px;
    height: 22px;
    transform: translate(-3px, 6px);
    margin: 0 0px 0 5px;
  }

  .answer {
    padding-left: 60rpx;
    line-height: 30px;
    background: #f4fafe;
    /*   font-weight: bold; */
  }
  .con {
    min-height: 100vh;
    background: #fff;
    border-top: 1px solid #f2f2f2;
  }
  .getscore {
    color: #c01c13;
  }
  .title {
    line-height: 30px;
  }
  .circle {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    display: inline-block;
    line-height: 16px;
    font-size: 10px;
    text-align: center;
    border: 1px solid #7f7f7f;
    transform: translateY(-1px);
    margin: 0 10rpx 0 10rpx;
  }
  .option {
    font-size: 14px;

    line-height: 30px;
    padding-left: 10rpx;
    margin: 10rpx 0;
  }
  .topic {
    line-height: 24px;
    display: flex;
    .type {
      width: 80rpx;
      text-align: center;
      margin-right: 10rpx;
    }
    .type-con {
      border: 1px solid #7f7f7f;
      padding: 4rpx 10rpx;
      font-size: 10px;
      border-radius: 2rpx;
    }
    .content {
      flex: 1;
      flex-wrap: wrap;

      font-size: 14px;
    }
    .score {
      width: 120rpx;
      white-space: nowrap;
    }
  }
  .exam-item {
    margin-bottom: 10rpx;
    background: #fff;
    padding: 20rpx;
  }
</style>

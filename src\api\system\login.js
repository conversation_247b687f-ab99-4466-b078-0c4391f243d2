/*
 * @Description: 用户相关API
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-06-25 17:10:02
 * @LastEditTime: 2025-05-22 17:18:32
 */
import request from "@/utils/request"

// 登录方法
export function loginRequest(username, password, code, uuid) {
  const data = {
    username,
    password,
    code,
    uuid,
    fromType: 1
  }
  return request({
    url: "/auth/login",
    headers: {
      isToken: false
    },
    method: "post",
    data
  })
}

// 获取用户详细信息
export function getInfo() {
  return request({
    url: "/system/user/getInfo",
    method: "get"
  })
}

// 退出方法
export function logout() {
  return request({
    url: "/auth/logout",
    method: "delete"
  })
}

// 获取验证码
export function getCodeImg() {
  return request({
    url: "/code",
    headers: {
      isToken: false
    },
    method: "get",
    timeout: 20000
  })
}

// 获取key
export function getPublicKey() {
  return request({
    url: "/auth/publicKey",
    method: "get"
  })
}

// 微信授权登录
export function authLoginWechat(data) {
  return request({
    url: "/auth/wx/doAuth",
    method: "post",
    data
  })
}

// 获取微信用户手机号码信息
export function getWxPhoneInfo() {
  return request({
    url: "/system/wx/user/getInfo",
    method: "get"
  })
}

// ========================== 查询租户信息 ==========================
export function querySysTenantInfo() {
  return request({
    url: "/system/tenant/tenantInfo",
    method: "get"
  })
}

// fzjz租户调查问卷页面单独登录接口
export function scanLogin(data) {
  return request({
    url: "/auth/scan-login",
    method: "post",
    data
  })
}

// eduxd租户注册接口
export function eduxdRegister(data) {
  return request({
    url: "/auth/smsRegister",
    method: "post",
    data
  })
}

// zhzg租户注册接口
export function zhzgRegister(data) {
  return request({
    url: "/system/user/third-party-register",
    method: "post",
    data
  })
}

// 检查用户是否已注册
export function thirdPartyCheck(mobile) {
  return request({
    url: `/system/user/third-party-check/${mobile}`,
    method: "get"
  })
}

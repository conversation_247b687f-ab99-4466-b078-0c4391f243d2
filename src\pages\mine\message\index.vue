<!--
 * @Description: 消息页面
 * @Author: <PERSON><PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-04-13 13:54:02
 * @LastEditTime: 2023-06-02 17:21:31
-->
<template>
  <view>
    <view v-for="(item, index) in msgList" :key="item.id">
      <view class="msg-list">
        <view class="msg-list-avatar">
          <img :src="item.avatar" alt="" />
        </view>
        <view class="msg-list-info">
          {{ item.msg }}
        </view>
      </view>
      <view
        style="
          border-top: 1px solid #d7d7d7;
          margin-left: 30rpx;
          margin-right: 30rpx;
        "
      ></view>
    </view>
    <o-divider lineColor="#d7d7d7" textColor="#c4c4c6" margin="50rpx 100rpx"
      >没有更多了</o-divider
    >
  </view>
</template>

<script>
export default {
  data() {
    return {
      msgList: [
        {
          id: 1,
          avatar:
            "https://tse4-mm.cn.bing.net/th/id/OIP-C.nSxgkfEFwY02G0FjanNvJgAAAA?w=201&h=201&c=7&r=0&o=5&pid=1.7",
          msg: "您好，根据业务我们更新了视频内容，您可前往短视频查看。"
        }
      ]
    }
  }
}
</script>

<style lang="scss" scoped>
.msg-list {
  // background-color: skyblue;
  margin-bottom: 10rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-left: 30rpx;
  margin-right: 30rpx;
  height: 150rpx;

  .msg-list-avatar {
    display: flex;
  }

  .msg-list-avatar > img {
    margin-right: 50rpx;
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
  }

  .msg-list-info {
    display: flex;
  }
}
</style>

<template>
  <view class="rounded-lg shadow-lg m-2 p-2">
    <uni-grid
      v-if="list.length > 0"
      class="content-bar-action"
      :column="4"
      :showBorder="false"
      @change="changeGrid"
    >
      <uni-grid-item
        v-for="actionItem in list"
        class="content-bar-action-item"
        :index="actionItem.id"
        :key="actionItem.id"
      >
        <view class="grid-item-box">
          <view class="content-bar-action-item__image">
            <image
              v-if="actionItem.img"
              class="content-bar-action-item__image-icon"
              :src="actionItem.img"
            />
          </view>
          <text class="content-bar-action-item__title">
            {{ actionItem.name }}
          </text>
        </view>
      </uni-grid-item>
    </uni-grid>
  </view>
</template>

<script>
  export default {
    props: ["list"],
    methods: {
      changeGrid(e) {
        const url = this.list.filter(item => {
          return item.id === e.detail.index
        })[0].url
        uni.navigateTo({
          url
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
  .content-bar-action {
    padding: 20rpx 0;
    display: flex;
    // 列表项
    .content-bar-action-item {
      flex: 1;
      display: flex;
      height: 125rpx !important;
      // 图标
      .content-bar-action-item__image {
        margin: 0 auto;
        position: relative;
        width: 80rpx;
        height: 80rpx;
        overflow: hidden;
        border-radius: 35%;
        display: flex;
        justify-content: center;
        align-items: center;

        .content-bar-action-item__image-icon {
          width: 100%;
          height: 100%;
        }

        .content-bar-action-item__image-text {
          position: absolute;
          top: 2rpx;
          left: 1rpx;
          width: 125rpx;
          height: 100rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 2;
          font-size: 16rpx;
          font-weight: 600;
          color: #fff;
        }
      }

      // 小标题
      .content-bar-action-item__title {
        color: black;
        font-size: 26rpx;
        display: block;
        text-align: center;
        margin: 10rpx 0 0 0;
      }
    }
  }
</style>

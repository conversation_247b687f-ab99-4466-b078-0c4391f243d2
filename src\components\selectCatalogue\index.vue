<!--
 * @Description: 页面头部分类选择器
 * @Author: <PERSON><PERSON>
 * @LastEditors: <PERSON><PERSON>
 * @Date: 2023-04-12 16:14:21
 * @LastEditTime: 2023-05-09 14:12:52
-->
<template>
  <view class="main">
    <view class="main-header">
      <scroll-view class="catalogue" scroll-x="true">
        <view
          class="catalogue-list"
          v-for="(item, index) in selectList"
          :key="item.catalogueId"
          @click="changeInfo(index, item.catalogueId)"
          :class="{ changeBackGround: currentIndex == index }"
        >
          {{ item.catalogueName }}
        </view>
      </scroll-view>
      <view class="select">
        <view
          class="iconfont icon-xiangxia icon"
          :class="{ rotate: rotated, rotateBack: !rotated }"
          @click="showList"
        ></view>
      </view>
    </view>
    <uni-transition style="z-index: 998" :show="isShowed" :mode-class="['fade', 'slide-top']">
      <view class="channel">
        <view class="channel-header">
          <view>我的频道</view>
          <view>
            <button class="editBtn" @click="changeState">
              {{ isEdited ? "完成编辑" : "进入编辑" }}
            </button>
          </view>
        </view>
        <view class="channel-body">
          <view v-for="(item, index) in selectList" :key="item.catalogueId">
            <view>{{ item.catalogueName }}</view>
            <view
              class="iconfont icon-roundclosefill icon channel-body-img"
              v-show="isEdited"
              @click="deleteTag(index)"
            ></view>
          </view>
        </view>
      </view>
    </uni-transition>
  </view>
</template>

<script>
  import { getCatalogueList } from "@/api/system/catalogue.js"
  import { catalogueType } from "@/utils/constant.js"
  export default {
    data() {
      return {
        selectList: [],
        rotated: false,
        isShowed: false,
        currentIndex: 0,
        isEdited: false,
        pageNum: 1,
        pageSize: 4,
        dataTotal: 0,
        loaded: false
      }
    },

    created() {
      this.fetchCatalogueData()
    },

    methods: {
      showList() {
        this.rotated = !this.rotated
        this.isShowed = !this.isShowed
      },

      changeInfo(e, id) {
        this.currentIndex = e
        this.$store.commit("RESET_PAGE_NUM")
        this.$store.commit("SET_CATALOGUE_ID", id)
        this.$store.dispatch("fetchCourseData", {
          id: id,
          name: this.$store.state.course.courseName
        })
      },

      changeState() {
        this.isEdited = !this.isEdited
      },

      deleteTag(index) {
        // this.selectList.splice(index,1)
      },

      async fetchCatalogueData() {
        let queryData = {
          catalogueType: catalogueType.COURSE_CATALOGUE
        }
        const { rows } = await getCatalogueList(queryData)
        this.selectList = rows
        this.selectList.unshift({
          catalogueId: 0,
          catalogueName: "全部",
          parentId: 0,
          orderNum: 0,
          catalogueType: "C",
          children: []
        })
      }

      // async fetchCourseList(id) {
      //   let queryData = {
      //     pageNum: this.pageNum,
      //     pageSize: this.pageSize,
      //     catalogueId: id,
      //   };
      //   const { rows, total } = await courseList(queryData);
      //   this.dataTotal = total;
      //   if (this.pageNum === 0) {
      //     this.courseList = rows || [];
      //   } else {
      //     this.courseList = [];
      //     this.courseList = this.courseList.concat(rows);
      //   }
      //   this.loaded = true;
      // },
    }
  }
</script>

<style lang="scss" scoped>
  .main {
    display: flex;
    flex-direction: column;
    position: absolute;
    height: 200rpx;
    width: 100%;
    .main-header {
      display: flex;
      z-index: 999;

      .catalogue {
        position: relative;
        white-space: nowrap;
        height: 100rpx;
        background-color: #fff;
        width: 85%;

        .catalogue-list {
          height: 100rpx;
          line-height: 100rpx;
          display: inline-block;
          text-align: center;
          width: 25%;
          color: #7f7f7f;
        }

        .changeBackGround {
          font-weight: bold;
          color: #000;
        }
      }

      .select {
        width: 15%;
        height: 100rpx;
        background-color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    .channel {
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      background-color: #fff;
      padding: 10rpx 40rpx;

      .channel-header {
        width: 100%;
        display: flex;
        justify-content: space-around;
        align-items: center;

        > view {
          margin-right: 250rpx;
          > button {
            margin-right: -200rpx;
            font-size: 23rpx;
            width: 150rpx;
            border-radius: 30px;
            background-color: #f2f2f2;
            color: #ed9d32;
          }
          > button::after {
            border: none;
          }
        }
      }

      .channel-body {
        position: relative;
        width: 100%;
        margin-top: 20rpx;
        // display: flex;
        // flex-wrap: wrap;
        // justify-content: space-evenly;
        display: grid;
        grid-template-columns: 1fr 1fr 1fr 1fr;
        grid-template-rows: repeat(auto-fill, 50rpx);
        grid-gap: 20rpx;
        margin-bottom: 30rpx;
        > view {
          // width: 22%;
          border-radius: 10rpx;
          // margin-bottom: 20rpx;
          text-align: center;
          background-color: #f2f2f2;
          height: 50rpx;
          line-height: 50rpx;
          position: relative;
        }

        .channel-body-img {
          width: 34rpx;
          height: 34rpx;
          position: absolute;
          border-radius: 60%;
          top: -17rpx;
          right: -17rpx;
        }
      }
    }
  }

  .rotate {
    transform: rotateZ(180deg);
    transition: all 0.3s;
    transform-origin: center;
  }

  .rotateBack {
    transform-origin: center;
    transform: rotateZ(0deg);
    transition: all 0.3s;
  }
</style>

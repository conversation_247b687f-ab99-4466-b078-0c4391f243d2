/*
 * @Description:
 * @Author: <PERSON><PERSON>
 * @LastEditors: <PERSON><PERSON>
 * @Date: 2023-05-24 14:10:36
 * @LastEditTime: 2023-05-25 11:16:52
 */
const dict = {
  state: {
    dict: new Array()
  },

  mutations: {
    setDict: (state, obj) => {
      state.dict.push(obj)
    },

    removeDict: (state, i) => {
      state.dict.splice(i, 1)
    },
    cleanDict: state => {
      state.dict = new Array()
    }
  },

  actions: {
    // 获取字典
    getDict({ state, commit }, _key) {
      return new Promise((resolve, reject) => {
        if (_key == null && _key == "") {
          resolve(null)
        }
        if (state.dict.length === 0) resolve(null)
        for (let i = 0; i < state.dict.length; i++) {
          if (state.dict[i].key == _key) {
            resolve(state.dict[i].value)
          }
        }
        resolve(null)
      })
    },
    // 设置字典
    setDict({ state, commit }, { key, value }) {
      if (key !== null && key !== "") {
        commit("setDict", {
          key,
          value
        })
      }
    },
    // 删除字典
    removeDict({ state, commit }, _key) {
      var bln = false
      try {
        for (let i = 0; i < state.dict.length; i++) {
          if (state.dict[i].key == _key) {
            commit("removeDict", i)
            return true
          }
        }
      } catch (e) {
        bln = false
      }
      return bln
    },
    // 清空字典
    cleanDict() {
      commit("cleanDict")
    }
  }
}

export default dict

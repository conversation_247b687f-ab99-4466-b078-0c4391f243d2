/*
 * @Description:课程接口
 * @Author: <PERSON><PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-04-20 09:52:14
 * @LastEditTime: 2024-12-19 09:22:37
 */
import request from "@/utils/request"

// 课程列表
export function listCourse(params) {
  return request({
    url: "/course/base/list",
    method: "get",
    params
  })
}

// 课程换一换
export function courseRandomizer(params) {
  return request({
    url: "/course/base/courseRandomizer",
    method: "get",
    params
  })
}

// 统计点击次数
export function courseEditView(params) {
  return request({
    url: `/course/base/editView/${params.courseId}`,
    method: "get"
  })
}

// 通过id获取课程信息
export function getCourseInfo(courseId) {
  return request({
    url: "/course/base/" + courseId,
    method: "get"
  })
}

// 我的课程列表
export function myCourseList(params) {
  return request({
    url: "/course/progress/list",
    method: "get",
    params
  })
}

// 更新课程的学习进度
export function updateCourseProgress(data) {
  return request({
    url: "/course/progress/saveOrUpdate",
    method: "post",
    data: data
  })
}

// 获取课程学习进度详细信息
export function getCourseStats(courseId, params) {
  return request({
    url: "/course/stats/" + courseId,
    method: "get",
    params
  })
}

// 首页猜你喜欢
export function hotCourseList(params) {
  return request({
    url: "/course/base/like",
    method: "get",
    params
  })
}

// 清空课程章节进度
export function clearCourseProgress(data) {
  return request({
    url: "/course/stats/reset",
    method: "post",
    data
  })
}
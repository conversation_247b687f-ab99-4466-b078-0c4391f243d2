<!--
 * @Description: 培训任务详情页
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-12-17 10:47:24
 * @LastEditTime: 2024-12-17 13:12:53
-->

<template>
  <view class="min-h-screen bg-gray-50">
    <navigationBar path="/pages/mine/task/index" />
    <study-arrangement :study-list="studyList" :current-task-id="taskId" />
  </view>
</template>

<script>
  import { getTaskById } from "@/api/trainingTask"
  import StudyArrangement from "@/components/StudyArrangement/index.vue"

  export default {
    components: {
      StudyArrangement
    },

    data() {
      return {
        taskId: "",
        taskInfo: {},
        studyList: [],
        taskStatusOpts: []
      }
    },

    computed: {
      taskStatusText() {
        const statusItem = this.taskStatusOpts.find(p => p.value === this.taskInfo.taskStatus)
        return statusItem ? statusItem.text : ""
      }
    },

    onLoad(options) {
      this.taskId = options.id
      this.getTaskStatusList()
      this.getTaskDetail()
    },

    methods: {
      getStatusStyle(status) {
        const baseStyle = "px-2 rounded text-sm inline-flex items-center justify-center"
        const styleMap = {
          1: `${baseStyle} bg-gray-100 text-gray-600`,
          2: `${baseStyle} bg-orange-100 text-orange-600`,
          3: `${baseStyle} bg-green-100 text-green-600`,
          4: `${baseStyle} bg-red-100 text-red-600`
        }
        return styleMap[status] || styleMap["1"]
      },

      formatPercent(value) {
        return Math.round(value * 100) + "%"
      },

      async getTaskStatusList() {
        this.taskStatusOpts = await this.$getDict("training_task_status")
      },

      async getTaskDetail() {
        try {
          const { rows } = await getTaskById(this.taskId, {
            pageSize: 999
          })
          // 获取任务看板数据
          this.studyList = this.formatStudyList(rows)
        } catch (error) {
          console.error("获取任务详情失败:", error)
        }
      },

      // 格式化学习列表数据
      formatStudyList(data) {
        if (!data) return []

        return data.map(item => {
          const type = this.getItemType(item)
          return {
            ...item,
            id: item.fieldId,
            type,
            name: item.courseName || item.questionnaireName || item.manageName || item.examName,
            endTime: item.endTime,
            progress: item.rateLearning ? item.rateLearning : 0,
            status: item.rateLearning >= 1 ? "completed" : "ongoing",
            // 保留前置任务信息
            preconditionLinkList: item.preconditionLinkList || []
          }
        })
      },

      // 获取项目类型
      getItemType(item) {
        if (item.fieldType === "0") return "course"
        if (item.fieldType === "1") return "exam"
        if (item.fieldType === "2") return "questionnaire"
        if (item.fieldType === "3") return "resource"
        return "course"
      }
    }
  }
</script>

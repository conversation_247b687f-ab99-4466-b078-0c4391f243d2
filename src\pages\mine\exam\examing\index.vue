<!--
 * @Description: 考试页面
 * @Author: <PERSON><PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-05-26 09:35:15
 * @LastEditTime: 2025-06-19 10:23:27
-->
<template>
  <view class="flex flex-col min-h-screen bg-gray-100">
    <!-- 导航栏 -->
    <navigationBar
      v-if="!specialTenantList.includes(domainName)"
      :path="`/pages/mine/exam/index?showNavBar=${showNavBar}`"
      :showPersonal="false"
    />

    <!-- 题目内容区域 -->
    <view class="flex-1 px-4 mt-4 pb-4" v-if="examQuestionList[examIndex]">
      <view class="bg-white rounded-lg shadow-md p-4">
        <!-- 顶部信息栏 -->
        <view class="flex items-center justify-between mb-4">
          <view class="flex items-center gap-2" @click="showList">
            <view
              class="i-material-symbols:format-list-numbered text-xl text-gray-600 cursor-pointer"
            ></view>
            <view class="flex items-center gap-2">
              <text class="text-base">第</text>
              <text class="text-blue-500 text-base">{{ examIndex + 1 }} </text>
              <text class="text-base"> / {{ examQuestionList.length }} 题</text>
            </view>
          </view>

          <!-- 计时器 -->
          <view class="flex justify-end items-center" v-show="timeLeft && timeLeft >= 0">
            <view class="bg-teal-500 text-white px-4 py-1 rounded">
              用时: {{ countDownTime() }}
            </view>
          </view>
        </view>

        <!-- 题目内容 -->
        <view class="mb-6">
          <!-- 题型标签 -->
          <span
            class="text-sm px-2 py-0.5 rounded w-150px mr-1"
            :class="{
              'bg-green-100 text-green-600': examQuestionList[examIndex].questionType === 'S',
              'bg-orange-100 text-orange-600': examQuestionList[examIndex].questionType === 'M',
              'bg-[#f5e6d3] text-[#7b4d12]': examQuestionList[examIndex].questionType === 'J',
              'bg-purple-100 text-[#8400ff]': examQuestionList[examIndex].questionType === 'K',
              'bg-blue-100 text-blue-600': examQuestionList[examIndex].questionType === 'Q'
            }"
          >
            {{ questionType[examQuestionList[examIndex].questionType] }}
          </span>
          <span
            class="text-base text-gray-700 break-words whitespace-pre-wrap leading-relaxed block"
          >
            {{ examQuestionList[examIndex].questionName }}
          </span>
        </view>

        <!-- 单选/判断题 -->
        <view
          v-if="
            examQuestionList[examIndex].questionType === 'S' ||
            examQuestionList[examIndex].questionType === 'J'
          "
        >
          <u-radio-group
            class="flex flex-col gap-3"
            v-model="examQuestionList[examIndex].userAnswer"
            @change="value => singleChoiceSelected(examQuestionList[examIndex], examIndex, value)"
          >
            <view
              v-for="choice in 11"
              v-if="examQuestionList[examIndex][`item${choice}`]"
              :key="choice"
              class="!flex items-center p-3 rounded-lg hover:bg-gray-50 transition-colors w-full cursor-pointer"
              :class="{
                'bg-blue-50':
                  examQuestionList[examIndex].userAnswer === String.fromCharCode(64 + choice)
              }"
              @click="
                singleChoiceSelected(
                  examQuestionList[examIndex],
                  examIndex,
                  String.fromCharCode(64 + choice)
                )
              "
            >
              <u-radio
                :name="String.fromCharCode(64 + choice)"
                :custom-style="{
                  marginBottom: '0',
                  width: '100%',
                  pointerEvents: 'none'
                }"
              >
                {{ String.fromCharCode(64 + choice) }}.
                {{ examQuestionList[examIndex][`item${choice}`] }}
              </u-radio>
            </view>
          </u-radio-group>
        </view>

        <!-- 多选题 -->
        <view v-else-if="examQuestionList[examIndex].questionType === 'M'">
          <u-checkbox-group
            class="flex flex-col gap-3"
            :value="examQuestionList[examIndex].userAnswer"
            @change="value => handleMultipleChange(value)"
          >
            <view
              v-for="multiChoice in 11"
              v-if="examQuestionList[examIndex][`item${multiChoice}`]"
              :key="multiChoice"
              class="!flex items-start p-3 rounded-lg hover:bg-gray-50 transition-colors w-full cursor-pointer"
              :class="examQuestionList[examIndex].userAnswer[multiChoice - 1] ? 'bg-blue-50' : ''"
              @click.stop="handleMultipleClick(multiChoice - 1)"
            >
              <u-checkbox
                :name="multiChoice - 1"
                :checked="examQuestionList[examIndex].userAnswer[multiChoice - 1]"
                class="flex-1"
                :customStyle="{ marginBottom: '0', pointerEvents: 'none' }"
                :label="`${String.fromCharCode(64 + multiChoice)}. ${
                  examQuestionList[examIndex][`item${multiChoice}`]
                }`"
                @change="value => handleSingleCheckboxChange(multiChoice - 1, value)"
              >
              </u-checkbox>
            </view>
          </u-checkbox-group>
        </view>

        <!-- 填空题 -->
        <view v-else-if="examQuestionList[examIndex].questionType === 'K'" class="space-y-4">
          <view
            v-for="blankChoice in examQuestionList[examIndex].blankCount"
            :key="blankChoice"
            class="flex flex-col gap-2"
          >
            <text class="text-gray-600">填空{{ blankChoice }}:</text>
            <uni-easyinput
              class="w-full"
              @change="
                writeBlank(
                  examQuestionList[examIndex],
                  blankChoice - 1,
                  examQuestionList[examIndex][`blank${blankChoice}`]
                )
              "
              v-model="examQuestionList[examIndex][`blank${blankChoice}`]"
              placeholder="请输入内容"
              :clearable="false"
            ></uni-easyinput>
          </view>
        </view>

        <!-- 简答题 -->
        <view v-else-if="examQuestionList[examIndex].questionType === 'Q'" class="space-y-4">
          <text class="text-gray-600 text-base">请在下方文本框中输入您的答案：</text>
          <uni-easyinput
            class="w-full"
            type="textarea"
            :autoHeight="true"
            :minRows="6"
            @blur="writeEssay(examQuestionList[examIndex], examQuestionList[examIndex].userAnswer)"
            v-model="examQuestionList[examIndex].userAnswer"
            placeholder="请输入您的答案..."
            :clearable="false"
          ></uni-easyinput>
        </view>
      </view>
    </view>

    <!-- 底部操作栏 -->
    <view class="flex justify-between items-center px-4 py-3 bg-white border-t border-gray-200">
      <button
        v-if="examIndex !== 0"
        class="flex-1 mx-2 py-2 rounded-full bg-gray-500 text-white"
        @click="prevQuestion"
      >
        上一题
      </button>
      <button class="flex-1 mx-2 py-2 rounded-full bg-blue-500 text-white" @click="handleSubmit">
        交卷
      </button>
      <button
        v-if="examQuestionList.length !== examIndex + 1"
        class="flex-1 mx-2 py-2 rounded-full bg-orange-500 text-white"
        @click="nextQuestion"
      >
        下一题
      </button>
    </view>

    <!-- 题目列表弹窗 -->
    <uni-popup ref="popup" background-color="#fff" @maskClick="maskClick">
      <view class="bg-white w-full max-h-[70vh] rounded-t-lg p-4">
        <!-- 顶部说明 -->
        <view class="flex justify-between items-center mb-4">
          <view class="flex items-center gap-4">
            <view class="flex items-center gap-2">
              <view class="w-4 h-4 border border-gray-300 rounded"></view>
              <text class="text-sm text-gray-600">未答</text>
            </view>
            <view class="flex items-center gap-2">
              <view class="w-4 h-4 bg-blue-500 rounded"></view>
              <text class="text-sm text-gray-600">已答</text>
            </view>
          </view>
          <text class="text-sm text-gray-600">共{{ examQuestionList.length }}题</text>
        </view>

        <!-- 题目列表 -->
        <scroll-view scroll-y="true" class="max-h-[calc(70vh-4rem)]">
          <view class="grid grid-cols-7 gap-3 px-2">
            <view
              v-for="(item, index) in examQuestionList"
              :key="index"
              class="h-9 w-9 flex items-center justify-center rounded-md cursor-pointer transition-colors text-[15px]"
              :class="[
                item.userAnswer && isMultipleChoiceSelected(item)
                  ? 'bg-blue-500 text-white'
                  : 'bg-blue-100 text-blue-500',
                'hover:bg-blue-400 hover:text-white'
              ]"
              @click="changeIndex(index)"
            >
              {{ index + 1 }}
            </view>
          </view>
        </scroll-view>
      </view>
    </uni-popup>

    <!-- 签名弹窗 -->
    <uni-popup ref="signPopup" background-color="#fff">
      <view class="bg-white w-full p-4">
        <sign ref="signRef" @submitSign="realSubmitOperation" />
      </view>
    </uni-popup>

    <yangr-msg v-if="yangrMsgShow" :title="title" :type="type"></yangr-msg>
  </view>
</template>

<script>
  import { unepTenantList, specialTenantList } from "@/utils/constant"
  import { startTheExam, submitAnswerPaper } from "@/api/exam/paper-answer.js"
  import { completeEachQuestion } from "@/api/exam/question-answer.js"
  import { questionType } from "@/utils/constant.js"
  import { toast, showConfirm } from "@/utils/common"
  import { getExam } from "@/api/exam/base.js"
  import yangrMsg from "@/components/yangr-msg/yangr-msg.vue"
  import sign from "../sign/index.vue"
  import { mapGetters } from "vuex"

  export default {
    components: { yangrMsg, sign },
    async onLoad(option) {
      this.baseId = option.baseId
      this.arrangeId = option.arrangeId
      this.taskId = option.taskId
      this.remark = option.remark
      if (option.showNavBar == "false") {
        this.showNavBar = option.showNavBar
      }

      await this.fetchExamDetailInfo()
      if (this.taskId) {
        if (this.examDetailInfo.examConfig.faceCapture === "1" && !option.verifySuccess) {
          uni.redirectTo({
            url: `/pages/face-verify/index?baseId=${this.baseId}&arrangeId=${
              this.arrangeId
            }&taskId=${this.taskId}&subTaskType=1&subTaskId=${
              this.baseId
            }&subTaskName=${encodeURIComponent(this.examDetailInfo.baseName)}`
          })
          return
        } else {
          this.verifySuccess = option.verifySuccess === "true"
          if (option.taskMonitorId) {
            this.taskMonitorId = option.taskMonitorId
          }
        }
      }

      if (this.unepTenantList.includes(this.domainName) || this.domainName === "eduxd") {
        this.failPushUrl = "/pages/competition/index"
      } else if (this.domainName === "town") {
        this.failPushUrl = "/pages/town/index"
      } else if (this.domainName === "cjaq") {
        if (this.remark) {
          this.failPushUrl = `/pages/cjaq/exam?baseId=${this.baseId}&arrangeId=${this.arrangeId}&remark=${this.remark}`
        } else {
          this.failPushUrl = "/pages/cjaq/index"
        }
      } else {
        this.failPushUrl = `/pages/mine/exam/prepare/index?baseId=${this.baseId}&arrangeId=${this.arrangeId}&showNavBar=${this.showNavBar}`
      }
      await this.fetchExamData()
      this.submintMinimumNumber = option.submintMinimumNumber
    },

    onUnload() {
      clearInterval(this.timer)
    },

    data() {
      return {
        paperQuestionInfo: [],
        paperTacticsList: [],
        examQuestionList: [],
        examDetailInfo: {},
        examIndex: 0,
        questionType,
        arrangeId: "",
        remark: "",
        baseId: "",
        taskId: "",
        submintMinimumNumber: "",
        timeLeft: null,
        timer: null,
        title: "",
        type: "",
        yangrMsgShow: false,
        failPushUrl: "",
        showNavBar: "",
        unepTenantList,
        specialTenantList,
        debounceTimer: null,
        pendingMultipleAnswer: null,
        isProcessingMultiple: false,
        verifySuccess: false,
        taskMonitorId: undefined,
        isSubmitting: false,
        lastClickTime: 0,
        clickThrottle: 300
      }
    },

    computed: {
      ...mapGetters({
        domainName: "domainName"
      }),
      // 倒计时格式化
      countDownTime() {
        return function () {
          if (this.timeLeft <= 0) {
            return "00:00:00"
          }
          const hours = Math.floor(this.timeLeft / 3600)
          const minutes = Math.floor((this.timeLeft % 3600) / 60)
          const seconds = this.timeLeft % 60
          return `${hours.toString().padStart(2, "0")}:
      ${minutes.toString().padStart(2, "0")}:
        ${seconds.toString().padStart(2, "0")}`
        }
      },

      // 判断多选题是否选中
      isMultipleChoiceSelected() {
        return function (item) {
          if (item.questionType === "M") {
            // [false,false,false,false]
            let isSelected = false
            item.userAnswer.forEach(item => {
              if (item) isSelected = true
            })
            return isSelected
          } else if (item.questionType === "Q") {
            // 简答题判断是否有内容
            return item.userAnswer && item.userAnswer.trim() !== ""
          } else {
            // 其他题型（单选、判断、填空）
            return (
              item.userAnswer &&
              (Array.isArray(item.userAnswer)
                ? item.userAnswer.length > 0
                : item.userAnswer.trim() !== "")
            )
          }
        }
      }
    },
    watch: {
      timeLeft: {
        handler(newValue) {
          // 倒计时结束后的处理
          if (this.examDetailInfo.examConfig.examDurationLimit !== 0 && newValue <= 0) {
            clearInterval(this.timer)
            uni.showToast({
              title: "测试时间耗尽，已自动交卷",
              icon: "error",
              success: () => {
                this.realSubmitOperation()
              }
            })
          }
        }
      }
    },
    methods: {
      countDown() {
        if (this.timeLeft > 0) {
          this.timeLeft--
        } else {
          // 倒计时结束后的处理
          clearInterval(this.timer)
        }
      },
      async fetchExamDetailInfo() {
        const { data } = await getExam(this.baseId)
        this.examDetailInfo = data
      },

      async fetchExamData() {
        try {
          const { data } = await startTheExam({
            baseId: this.baseId,
            arrangeId: this.arrangeId,
            taskId: this.taskId ? this.taskId : undefined,
            taskMonitorId: this.taskMonitorId
          })
          this.paperQuestionInfo = data
          this.timeLeft =
            this.examDetailInfo.examConfig.examDurationLimit && Number(data.remainingTime)
          this.paperTacticsList = data.paperTactics
          this.paperTacticsList.forEach(item => {
            item.examQuestions.forEach(question => {
              this.examQuestionList.push(question)
            })
          })
          this.examQuestionList.forEach(item => {
            // 第一次进入考试
            if (!item.userAnswer) {
              if (item.questionType === "S") {
                item.userAnswer = ""
              } else if (item.questionType === "M") {
                item.userAnswer = []
                for (let i = 1; i < 11; i++) {
                  if (item[`item${i}`]) {
                    item.userAnswer[i - 1] = false
                  }
                }
              } else if (item.questionType === "F") {
                item.userAnswer = []
              } else if (item.questionType === "Q") {
                item.userAnswer = ""
              }
            } else {
              if (item.questionType === "M") {
                let answerArr = []
                for (let i = 1; i < 11; i++) {
                  if (item[`item${i}`]) {
                    answerArr[i - 1] = false
                  }
                }
                item.userAnswer = item.userAnswer.split(",")
                item.userAnswer = this.convertToBooleanArray(item.userAnswer, answerArr.length)
              } else if (item.questionType === "F") {
                item.userAnswer = item.userAnswer.split("::")
                for (let i = 0; i < item.userAnswer.length; i++) {
                  item[`blank${i + 1}`] = item.userAnswer[i]
                }
              }
            }
          })

          // 倒计时
          if (this.examDetailInfo.examConfig.examDurationLimit !== 0) {
            this.timer = setInterval(this.countDown, 1000)
          }
        } catch (error) {
          setTimeout(() => {
            uni.navigateTo({
              url: this.failPushUrl
            })
          }, 2000)
        }
      },

      showList() {
        this.$refs.popup.open("top")
      },

      // 添加防抖函数
      debounce(fn, delay = 300) {
        if (this.debounceTimer) clearTimeout(this.debounceTimer)
        this.debounceTimer = setTimeout(() => {
          fn()
          this.debounceTimer = null
        }, delay)
      },

      // 节流函数
      throttle(callback, delay = 300) {
        const now = Date.now()
        if (now - this.lastClickTime >= delay) {
          callback()
          this.lastClickTime = now
        }
      },

      // 上一题
      prevQuestion() {
        if (this.examIndex > 0) {
          this.throttle(() => {
            this.examIndex--
          })
        } else {
          uni.showToast({
            title: "已经是第一题了",
            icon: "none"
          })
        }
      },

      // 下一题
      nextQuestion() {
        if (this.examIndex < this.examQuestionList.length - 1) {
          this.throttle(() => {
            this.examIndex++
          })
        } else {
          uni.showToast({
            title: "已经是最后一题了",
            icon: "none"
          })
        }
      },

      // 单选题选择处理
      singleChoiceSelected(item, index, value) {
        if (this.isSubmitting) return

        this.throttle(async () => {
          try {
            this.isSubmitting = true
            // 先更新本地状态
            this.$set(item, "userAnswer", value)

            // 调用接口提交答案
            await this.questionAnswerOperation(item, index)

            // 自动切换到下一题（如果不是最后一题）
            // if (index < this.examQuestionList.length - 1) {
            //   setTimeout(() => {
            //     if (this.examIndex < this.examQuestionList.length - 1) {
            //       this.examIndex++
            //     }
            //   }, 500)
            // }
          } catch (error) {
            console.error("提交答案失败:", error)
            // 恢复原来的答案
            this.$set(item, "userAnswer", "")
            uni.showToast({
              title: "提交答案失败，请重试",
              icon: "none"
            })
          } finally {
            this.isSubmitting = false
          }
        })
      },

      // 多选题整体变化处理
      async handleMultipleChange(value) {
        if (this.isSubmitting) return

        const currentQuestion = this.examQuestionList[this.examIndex]
        // 更新本地状态
        this.$set(currentQuestion, "userAnswer", value)

        // 提交答案
        await this.submitMultipleAnswer(currentQuestion)
      },

      // 单个复选框变化处理
      async handleSingleCheckboxChange(index, checked) {
        if (this.isSubmitting) return

        const currentQuestion = this.examQuestionList[this.examIndex]
        const newAnswer = [...currentQuestion.userAnswer]
        newAnswer[index] = checked

        // 更新本地状态
        this.$set(currentQuestion, "userAnswer", newAnswer)

        // 提交答案
        await this.submitMultipleAnswer(currentQuestion)
      },

      // 提交多选题答案
      async submitMultipleAnswer(question) {
        try {
          this.isSubmitting = true

          // 将布尔数组转换为字母答案
          const answerArray = question.userAnswer
            .map((checked, idx) => (checked ? String.fromCharCode(65 + idx) : null))
            .filter(item => item !== null)

          // 构建请求数据
          const requestData = {
            questionId: question.questionId,
            arrangeId: this.arrangeId,
            remark: this.remark,
            userAnswer: answerArray.sort().join(","),
            paperAnswerId: this.paperQuestionInfo?.paperAnswerId
          }

          await completeEachQuestion(requestData)
        } catch (error) {
          console.error("多选提交失败:", error)
          uni.showToast({
            title: "提交答案失败，请重试",
            icon: "none"
          })
        } finally {
          this.isSubmitting = false
        }
      },

      // 填空题处理
      async writeBlank(item, index, value) {
        if (this.isSubmitting) return

        this.throttle(async () => {
          try {
            this.isSubmitting = true
            const oldAnswer = [...item.userAnswer]
            item.userAnswer[index] = value

            // 构建请求数据
            const requestData = {
              questionId: item.questionId,
              arrangeId: this.arrangeId,
              remark: this.remark,
              userAnswer: item.userAnswer.join("::"),
              paperAnswerId: this.paperQuestionInfo?.paperAnswerId
            }

            await completeEachQuestion(requestData)
          } catch (error) {
            console.error("填空题提交失败:", error)
            // 恢复原来的答案
            item.userAnswer = oldAnswer
            uni.showToast({
              title: "提交答案失败，请重试",
              icon: "none"
            })
          } finally {
            this.isSubmitting = false
          }
        }, 1000) // 填空题的节流时间可以稍长一些
      },

      // 简答题处理
      async writeEssay(item, value) {
        if (this.isSubmitting) return

        this.throttle(async () => {
          try {
            this.isSubmitting = true
            const oldAnswer = item.userAnswer
            item.userAnswer = value

            // 构建请求数据
            const requestData = {
              questionId: item.questionId,
              arrangeId: this.arrangeId,
              remark: this.remark,
              userAnswer: item.userAnswer,
              paperAnswerId: this.paperQuestionInfo?.paperAnswerId
            }

            await completeEachQuestion(requestData)
          } catch (error) {
            console.error("简答题提交失败:", error)
            // 恢复原来的答案
            item.userAnswer = oldAnswer
            uni.showToast({
              title: "提交答案失败，请重试",
              icon: "none"
            })
          } finally {
            this.isSubmitting = false
          }
        }, 1000) // 简答题的节流时间可以稍长一些
      },

      // 切换题目时的处理
      async changeIndex(index) {
        if (this.isSubmitting) return

        this.throttle(() => {
          this.$refs.popup.close()
          this.examIndex = index

          // 初始化答案状态
          const currentQuestion = this.examQuestionList[index]
          if (!currentQuestion.userAnswer) {
            if (currentQuestion.questionType === "S" || currentQuestion.questionType === "J") {
              this.$set(currentQuestion, "userAnswer", "")
            } else if (currentQuestion.questionType === "M") {
              const answerArray = []
              for (let i = 1; i < 11; i++) {
                if (currentQuestion[`item${i}`]) {
                  answerArray[i - 1] = false
                }
              }
              this.$set(currentQuestion, "userAnswer", answerArray)
            } else if (currentQuestion.questionType === "F") {
              this.$set(
                currentQuestion,
                "userAnswer",
                new Array(currentQuestion.blankCount).fill("")
              )
            } else if (currentQuestion.questionType === "Q") {
              this.$set(currentQuestion, "userAnswer", "")
            }
          }
        })
      },

      // 提交试卷前的验证
      async handleSubmit() {
        if (this.isSubmitting) {
          uni.showToast({
            title: "正在处理中，请稍候",
            icon: "none"
          })
          return
        }

        // 检查是否有未完成的题目
        const unfinishedQuestions = this.examQuestionList.filter(item => {
          if (item.questionType === "M") {
            return !item.userAnswer.some(answer => answer)
          } else if (item.questionType === "Q") {
            return !item.userAnswer || item.userAnswer.trim() === ""
          } else if (item.questionType === "F") {
            return (
              !item.userAnswer ||
              (Array.isArray(item.userAnswer) && item.userAnswer.some(answer => !answer))
            )
          } else {
            return !item.userAnswer || item.userAnswer.trim() === ""
          }
        })

        if (unfinishedQuestions.length > 0) {
          const res = await showConfirm(
            `还有 ${unfinishedQuestions.length} 道题目未完成，是否确定提交？`
          )
          if (!res.confirm) return
        } else {
          const res = await showConfirm("是否确定提交试卷?")
          if (!res.confirm) return
        }

        // 判断是否需要考试签名
        if (this.paperQuestionInfo.signFlag === "1") {
          this.$refs.signPopup.open("bottom")
        } else {
          this.realSubmitOperation()
        }
      },

      // 实际调用接口提交试卷
      async realSubmitOperation(signUrl) {
        try {
          if (signUrl) {
            this.$refs.signPopup.close()
          }
          const requestData = {
            paperAnswerId: this.paperQuestionInfo?.paperAnswerId,
            signature: signUrl
          }
          const res = await submitAnswerPaper(requestData)
          if (["town", "cjaq", "zhzg", "yuyuan", "jxyj"].includes(this.domainName)) {
            uni.redirectTo({
              url: `/pages/${this.domainName}/${this.domainName}Success?baseId=${this.baseId}&arrangeId=${this.arrangeId}&paperAnswerId=${this.paperQuestionInfo?.paperAnswerId}&score=${res.data.userPaperScore}&duration=${res.data.costTime}`
            })
          } else {
            uni.redirectTo({
              url: `/pages/mine/exam/result/index?baseId=${this.baseId}&arrangeId=${this.arrangeId}&paperAnswerId=${this.paperQuestionInfo?.paperAnswerId}&userPaperScore=${res.data.userPaperScore}&score=${res.data.userPaperScore}&duration=${res.data.costTime}`
            })
          }
        } catch (err) {
          this.title = "测试提交失败，请稍后再试"
          this.type = "error"
          this.yangrMsgShow = true
          setTimeout(() => {
            uni.navigateTo({
              url: this.failPushUrl
            })
          }, 2000)
        }
      },

      // 将字母数组转化为boolean数组
      convertToBooleanArray(letters, totalNum) {
        const result = new Array(totalNum).fill(false)
        for (const letter of letters) {
          const index = letter.charCodeAt(0) - 65
          result[index] = true
        }
        return result
      },

      // 优化问题答案操作
      async questionAnswerOperation(item, index) {
        try {
          // 如果正在请求中，则不重复请求
          if (item.isRequesting) return

          // 标记正在请求
          this.$set(item, "isRequesting", true)

          // 原有的请求逻辑
          const requestData = {
            questionId: item.questionId,
            arrangeId: this.arrangeId,
            remark: this.remark,
            userAnswer: this.examQuestionList[index].userAnswer,
            paperAnswerId: this.paperQuestionInfo?.paperAnswerId
          }
          await completeEachQuestion(requestData)

          // 请求完成后清除标记
          this.$set(item, "isRequesting", false)
        } catch (error) {
          console.error("答题出错:", error)
          // 请求失败时也要清除标记
          this.$set(item, "isRequesting", false)
          // 可以在这里添加错误提示
          uni.showToast({
            title: "答题失败，请重试",
            icon: "none"
          })
        }
      },

      // 处理点击选项区域的事件
      async handleMultipleClick(index) {
        if (this.isSubmitting) return

        const currentQuestion = this.examQuestionList[this.examIndex]
        const newAnswer = [...currentQuestion.userAnswer]
        newAnswer[index] = !newAnswer[index]

        // 更新本地状态
        this.$set(currentQuestion, "userAnswer", newAnswer)

        // 提交答案
        await this.submitMultipleAnswer(currentQuestion)
      }
    }
  }
</script>

<style lang="scss" scoped>
  ::v-deep .u-radio__text {
    font-size: 18px !important;
    line-height: 24px !important;
    white-space: normal !important; /* Allow text to wrap */
    word-break: break-all !important; /* Break words to prevent overflow */
  }

  ::v-deep .u-radio-group,
  .u-checkbox-group {
    display: flex;
    flex-direction: column;
  }

  ::v-deep .u-radio {
    display: flex !important;
    align-items: center !important;
    width: 100% !important;
    pointer-events: none !important;
  }

  ::v-deep .u-radio__icon-wrap {
    flex-shrink: 0 !important;
    margin: 0 !important;
    display: flex !important;
    align-items: center !important;
    margin-right: 12px !important;
  }

  ::v-deep .u-radio__inner {
    width: 20px !important;
    height: 20px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
  }

  ::v-deep .u-radio__label {
    flex: 1 !important;
    padding: 0 12px !important;
    margin-left: 8px !important;
  }

  ::v-deep .text-base {
    > span {
      vertical-align: text-top;
      > br {
        display: none;
      }
    }
  }

  ::v-deep .u-checkbox {
    width: 100% !important;
    > uni-text {
      > span {
        width: 100% !important;
        white-space: normal !important; /* Allow text to wrap */
        word-break: break-all !important; /* Break words to prevent overflow */
        display: inline-block; /* Help span respect width and wrap */
        vertical-align: middle; /* Align text vertically with checkbox */
        > br {
          display: none;
        }
      }
    }
  }

  page {
    background-color: #f3f4f6;
  }

  /* 移除按钮内文字的左边距 */
  button {
    padding: 0;
  }
  button > text {
    margin: 0;
  }

  /* 题目列表样式优化 */
  .grid-cols-7 {
    grid-template-columns: repeat(7, minmax(0, 1fr));
    justify-items: center;
  }

  /* 确保弹窗内容居中 */
  ::v-deep .uni-popup .uni-popup__wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* 修复滚动区域样式 */
  ::v-deep .uni-popup .uni-scroll-view {
    width: 100%;
  }

  /* 添加选项的激活状态样式 */
  .option-active {
    background-color: rgba(74, 144, 226, 0.1);
    border-color: #4a90e2;
  }

  /* 禁用状态样式 */
  .option-disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
</style>

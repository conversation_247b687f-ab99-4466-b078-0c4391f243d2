<template>
  <view class="detail" :style="`background-image: url('${assetsPrefixUrl}fzjz/fzjz-bg.jpg');`">
    <view class="iconfont icon-fanhui icon" @click="back"></view>
    <view class="detail-header">{{ detailItem.manageName }}</view>
    <view class="detail-body">
      <view class="left">
        <img
          v-if="currentIndex !== 0"
          src="@/static/images/fzjz/arrow-left.png"
          alt=""
          @click="goLastPage"
        />
      </view>
      <swiper :current="currentIndex" style="height: 600px" @change="changeItem" :duration="0">
        <swiper-item v-for="item in detailList" :key="item.manageId">
          <view>
            <img
              v-if="fileFormat === 'image'"
              :src="item.manageAddress"
              alt=""
              style="height: 600px"
            />
            <video
              v-else-if="fileFormat === 'video'"
              :src="item.manageAddress"
              controls
              :enable-progress-gesture="false"
              style="height: 600px"
            ></video>
          </view>
        </swiper-item>
      </swiper>
      <view class="right">
        <img
          v-if="currentIndex !== detailList.length - 1"
          src="@/static/images/fzjz/arrow-right.png"
          alt=""
          @click="goNextPage"
        />
      </view>
    </view>
  </view>
</template>

<script>
  import { listManage } from "@/api/course/manage"
  import { validateFileFormat } from "@/utils/common"
  import { assetsPrefixUrl } from "@/utils/constant.js"

  export default {
    data() {
      return {
        flag: null,
        detailList: [],
        detailItem: [],
        currentIndex: null,
        catalogueId: undefined,
        assetsPrefixUrl
      }
    },
    async created() {},
    computed: {
      fileFormat() {
        return this.validateFileFormat(this.detailItem.manageAddress)
      }
    },
    async onLoad(option) {
      this.flag = option.flag
      this.detailItem = JSON.parse(decodeURIComponent(option.detailInfo))
      this.catalogueId = option.catalogueId
        ? Number(option.catalogueId)
        : this.flag === "knowledge"
        ? 2176
        : 2169
      await this.getDetailInfo()
      this.currentIndex = this.detailList.findIndex(item => {
        return item.manageId == this.detailItem.manageId
      })
    },
    methods: {
      validateFileFormat,
      async getDetailInfo() {
        const { rows } = await listManage({
          pageSize: 999,
          pageNum: 1,
          sortField: "manage_id",
          sortOrder: "asc",
          catalogueId: this.catalogueId
        })
        this.detailList = rows
      },

      goLastPage() {
        if (this.currentIndex === 0) {
          this.$modal.showToast("已经是第一页了")
        } else {
          this.detailItem = this.detailList[this.currentIndex - 1]
          this.currentIndex--
        }
      },
      goNextPage() {
        if (this.currentIndex === this.detailList.length - 1) {
          this.$modal.showToast("已经是最后一页了")
        } else {
          this.detailItem = this.detailList[this.currentIndex + 1]
          this.currentIndex++
        }
      },
      changeItem(e) {
        if (this.currentIndex < Number(e.detail.current)) {
          this.detailItem = this.detailList[this.currentIndex + 1]
        } else if (this.currentIndex > Number(e.detail.current)) {
          this.detailItem = this.detailList[this.currentIndex - 1]
        }
        this.currentIndex = e.detail.current
      },

      back() {
        uni.navigateBack({
          delta: 1
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
  .detail {
    position: relative;
    background-repeat: no-repeat;
    background-size: 100% auto;
    background-position: top;
    height: 100vh;
    padding-top: 46%;
    .icon-fanhui {
      position: absolute;
      left: 25rpx;
      top: 260rpx;
      font-weight: bold;
      font-size: 40rpx;
      color: #4a4c47;
    }
    .detail-header {
      text-align: center;
      font-size: 22px;
      font-weight: bold;
    }
    .detail-body {
      margin-top: 50px;
      position: relative;
      text-align: center;
      padding-bottom: 30px;
      background-color: #f0f1f5;

      .left {
        width: 30px;
        z-index: 9999;
        position: absolute;
        top: 285px;
        left: 0px;
        > img {
          height: 20px;
        }
      }
      .mid {
        width: 80%;
      }
      .right {
        width: 30px;
        z-index: 9999;
        position: absolute;
        top: 285px;
        right: 0px;
        > img {
          height: 20px;
        }
      }
    }
  }
</style>

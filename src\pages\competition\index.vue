<!--
 * @Description: 
 * @Author: <PERSON><PERSON><PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-09-19 17:32:35
 * @LastEditTime: 2025-06-09 16:11:20
-->
<template>
  <view
    class="con"
    :class="{ 'eduxd-bg': domainName === 'eduxd' }"
    :style="
      domainName === 'eduxd'
        ? `background-image: url('${assetsPrefixUrl}eduxd-bg.jpg') !important;`
        : ''
    "
  >
    <image
      v-if="domainName !== 'eduxd'"
      :src="topImageSrc"
      mode="widthFix"
      style="width: 100%; margin-top: 0px"
    ></image>
    <view class="title" v-else>中国西电集团、中国西电<br />重大事故隐患判定标准学习竞赛</view>
    <view class="rule">
      <view class="rule-title">活动介绍</view>
      <view class="rule-content">
        {{
          domainName === "unep"
            ? "2023年10月13日是第34个国际减灾日，主题是“共同打造有韧性的未来”，为增强市民灾害风险防范意识和素养，提高学习积极性，上海市在2023年国际减灾日前特别推出了“防灾减灾知识学习竞赛”活动。"
            : domainName === "yingji"
            ? "今年 5 月 11 日至 17 日是我国第 16 个防灾减灾宣传周，主题是“人人讲安全、个个会应急---着力提升基层防灾避险能力”。为深入贯彻落实习近平总书记关于应急管理、防灾减灾救灾工作重要论述和党的二十大精神，进一步提升各级领导干部统筹发展和安全能力，增强全民灾害风险防范意识和能力，提高防灾减灾救灾处置保障能力和水平，应急管理部推出“防灾减灾知识学习竞赛”活动。"
            : domainName === "eduxd"
            ? "为深入贯彻落实习近平总书记关于安全生产系列重要指示精神，	严格落实国务院安委办《关于学好用好重大事故隐患判定标准的通知》要求，切实提高广大员工学习应用重大事故隐患判定标准的主动性、自觉性，健全企业重大事故隐患排查治理长效机制，坚决防范遏制各类事故，公司安委办组织开展重大事故隐患判定标准学习竞赛。"
            : "今年 5 月 11 日至 17 日是我国第 16 个防灾减灾宣传周，主题是“人人讲安全、个个会应急---着力提升基层防灾避险能力”。为深入贯彻落实习近平总书记关于应急管理、防灾减灾救灾工作重要论述和党的二十大精神，进一步提升各级领导干部统筹发展和安全能力，增强全民灾害风险防范意识和能力，提高防灾减灾救灾处置保障能力和水平，应急管理部推出“防灾减灾知识学习竞赛”活动。"
        }}
      </view>
    </view>
    <view class="rule">
      <view class="rule-title">活动规则</view>
      <view class="rule-content">
        {{
          domainName === "eduxd"
            ? `活动由视频学习和知识测试两部分组成，综合总分100分。视频学习：每学习观看完1个视频，可积1分，最多可积25分，未完成一部视频学习扣1分;知识测试:每日完成10道测试题目，最终按照每日平均分×0.75。`
            : "活动由视频学习和知识测试两部分组成，综合总分100分。视频学习：每学习观看完1个视频，可积3分，最多可积45分；知识测试：随机抽取17道题目，总分55分，限时30分钟，活动期间可随时参与测试，测试机会为3次，以最优成绩作为最终记录，测试分数相同时，测试用时较少者优先。"
        }}
        <view v-if="domainName === 'eduxd'">最终成绩=视频学习得分+知识测试得分</view>
      </view>
    </view>
    <view class="rule">
      <view class="rule-title">活动时间</view>
      <view class="rule-content" style="text-indent: 0">
        <view class="rule-time"
          >{{ dayjs(startTime).format("YYYY年MM月DD日 HH:mm") }} -
          {{ dayjs(endTime).format("YYYY年MM月DD日 HH:mm") }}</view
        >
      </view>
    </view>

    <view class="buttonList">
      <button class="btn-learn" @click="handleLearn">开始学习</button>
      <button class="btn-exam" @click="handleExam">开始测试</button>
    </view>
  </view>
</template>

<script>
  import { mapGetters } from "vuex"
  import dayjs from "dayjs"
  import { assetsPrefixUrl } from "@/utils/constant.js"

  export default {
    data() {
      return {
        assetsPrefixUrl,
        showMore: false
      }
    },
    computed: {
      ...mapGetters({
        domainName: "domainName",
        startTime: "startTime",
        endTime: "endTime"
      }),
      topImageSrc() {
        return this.domainName === "unep"
          ? "/static/images/competition/home.png"
          : this.domainName === "yingji"
          ? "/static/images/competition/yingji_home.png"
          : "/static/images/competition/yingji_home.png"
      }
    },
    created() {
      if (new Date() > new Date("2023.10.11 12:00") && this.domainName === "unep") {
        this.$tab.reLaunch("/pages/competition/complete")
      }
    },
    methods: {
      dayjs,
      handleExam() {
        uni.navigateTo({ url: "/pages/competition/exam" })
      },
      handleLearn() {
        uni.navigateTo({ url: "/pages/competition/startLearn" })
      },
      handleMore() {
        this.showMore = !this.showMore
      }
    }
  }
</script>

<style lang="scss" scoped>
  .rule {
    width: 100%;
    /*  margin: 12rpx auto; */
    line-height: 46rpx;
    font-size: 16px;
    position: relative;
  }
  .rule-time {
    text-align: center;
  }
  .rule-title {
    text-align: center;
    font-size: 16px;
    line-height: 72rpx;
    padding-bottom: 10px;
    color: #fff;
    transform: translateY(10px);
    z-index: 1;
    position: relative;
    border-radius: 10px;
    font-weight: bold;
    background: linear-gradient(to right, #09a39b, #0c6fb3);
  }
  .rule-content {
    background: #fff;
    border-radius: 10px;
    z-index: 2;
    text-indent: 60rpx;
    position: relative;
    padding: 16px 16px;
    box-shadow: 0 0 10rpx rgba(0, 0, 0, 0.1);

    text-align: justify;
  }
  .sub-text {
    font-size: 17px;
  }
  .con {
    min-height: 100vh;
    background: url(/static/images/competition/logbg_3.png) no-repeat;
    background-size: 100% 100%;
    padding: 20rpx;
  }
  .content {
    line-height: 26px;
    font-size: 16px;
    margin-top: 20rpx;
  }
  .title {
    /* line-height: 60rpx;
  font-size: 20px; */
    text-align: center;
    font-weight: bold;
    color: #0f5f84;
    font-size: 46rpx;
    margin-bottom: 20rpx;
  }
  .close-date {
    font-weight: bold;
  }
  .more {
    color: #4098f7;
    text-decoration: underline;
    margin-left: 8rpx;
    font-weight: bold;
  }

  .sub-title {
    color: #d01f25;
    font-size: 18px;
    font-weight: bold;
  }

  .nor-title {
    color: #d01f25;
    font-size: 16px;
    font-weight: bold;
    margin-right: 8rpx;
  }
  .buttonList {
    display: flex;
    justify-content: space-evenly;
    margin-top: 30rpx;
    > button {
      width: 40%;
      color: #fff;
    }
  }
  .completeList {
    text-align: center;
    margin-top: 100rpx;
    font-size: 20px;
    line-height: 40px;
    font-weight: bold;
    color: #f58335;
  }
  .btn-learn {
    background: #75affd;
    box-shadow: 0 0 10rpx rgba(0, 0, 0, 0.1);
  }
  .btn-exam {
    background: #f59a23;
    box-shadow: 0 0 10rpx rgba(0, 0, 0, 0.1);
  }
  .eduxd-bg {
    background-repeat: no-repeat !important;
    background-size: 100% 100% !important;
    background-position: top !important;
  }
</style>

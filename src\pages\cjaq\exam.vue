<!--
 * @Description: 工会-考场页面
 * @Author: <PERSON>
 * @LastEditors: sun<PERSON><PERSON>
 * @Date: 2023-12-20 09:55:26
 * @LastEditTime: 2024-04-01 15:28:36
-->
<template>
  <view class="con">
    <navigationBar v-if="!cjaqExamShow" :path="`/pages/cjaq/index`" :showPersonal="false" />

    <view v-else class="navigationBar">
      <div class="left">
        <view class="iconfont icon-fanhui icon" @click="goback"></view>
        <view class="info">
          <view class="iconfont icon-shichang icon"></view>
        </view>
        <view class="text">安康杯竞赛</view>
      </div>
    </view>
    <view>
      <view class="exam-con" v-if="examDetailInfo.examConfig">
        <view class="title"> {{ examDetailInfo.baseName }} </view>
        <view class="content">
          <text class="sub-title">测试时间：</text>
          <text class="sub-content" v-if="examRecordInfo.startTime">
            {{ examRecordInfo.startTime }}~{{ examRecordInfo.endTime }}
          </text>
        </view>
        <view class="content">
          <text class="sub-title">测试时长：</text>
          <text class="sub-content">
            {{
              !examDetailInfo.examConfig.examDurationLimit ||
              examDetailInfo.examConfig.examDurationLimit === 0
                ? "不限时"
                : `${examDetailInfo.examConfig.examDurationLimit}分钟`
            }}
          </text>
        </view>
        <view class="content">
          <text class="sub-title">测试次数：</text>
          <text class="sub-content" v-if="examRecordInfo.remainExamCount == -1"> 无限次 </text>
          <text class="sub-content" v-else>
            还有
            <text class="highlight">{{ examRecordInfo.remainExamCount }} </text>
            次机会
          </text>
        </view>
        <view class="learn-btn">
          <button
            class="disable"
            v-if="!isInTimeRange(examRecordInfo.startTime, examRecordInfo.endTime)"
          >
            {{ displayContent(examRecordInfo.startTime, examRecordInfo.endTime) }}
          </button>
          <button v-else-if="examRecordInfo.remainExamCount != 0" size="mini" @click="jumpTo">
            开始测试
          </button>
          <button class="disable" v-else>已无测试次数</button>
        </view>
      </view>
      <view class="exam-list">
        <view class="exam-log">测试记录</view>
        <template v-if="completedExamRecordList.length > 0">
          <view class="exam-detail" v-for="(item, index) in completedExamRecordList" :key="index">
            <view class="content">
              <text class="sub-title">测试时间：</text>
              <text class="sub-content">{{ item.startTime }}</text>
            </view>
            <view class="content">
              <text class="sub-title">交卷时间：</text>
              <text class="sub-content">{{ item.submitTime }}</text>
            </view>
            <view class="content"
              ><text class="sub-title">测试成绩：</text
              ><text class="sub-content highlight">
                {{ item.userPaperScore }}
              </text></view
            >
            <view class="view-btn">
              <button size="mini" @click="handelExamDetail(item)" style="font-size: 15px">
                查看详情
              </button>
            </view>
          </view>
        </template>
        <view v-else class="nodata">
          <image src="@/static/images/competition/no-data.png"> </image>
          <text class="nodata-text">暂无测试详情</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
  import { getExam, getArrange } from "@/api/exam/base.js"
  import { isInTimeRange } from "@/utils/common"
  export default {
    onLoad(options) {
      this.baseId = options.baseId
      this.arrangeId = options.arrangeId
      this.remark = options.remark
      this.address = options.address
      if (this.address) {
        uni.setStorageSync("address", this.address)
      }

      // 工会安康杯，通过数值判断
      if (this.baseId == "2082" && this.arrangeId == "2331") {
        this.cjaqExamShow = true
      }
      this.loadData()
      this.loadRecordData()
    },
    data() {
      return {
        examDetailInfo: {},
        examRecordInfo: {},
        cjaqExamShow: false,
        baseId: "",
        remark: "",
        address: "",
        arrangeId: "",
        completedExamRecordList: [] // 已完成考试列表
      }
    },
    methods: {
      handleBack() {
        uni.navigateBack()
      },
      goback() {
        const url = "http://*************:10304/#/subPages/competition/index?t=" + Date.now()

        window.location.href = url
        //uni.navigateBack()
      },

      isInTimeRange,
      handelExamDetail(item) {
        uni.navigateTo({
          url: `/pages/cjaq/examDetail?baseId=${this.baseId}&arrangeId=${this.arrangeId}&remark=${this.remark}&paperAnswerId=${item.paperAnswerId}`
        })
      },
      async loadData() {
        const { data } = await getExam(this.baseId)
        this.examDetailInfo = data
      },
      async loadRecordData() {
        const { data } = await getArrange(this.arrangeId)
        this.examRecordInfo = data
        // 已完成的考试才显示在考试记录中
        if (!data.examPaperAnswers || data.examPaperAnswers.length === 0) return
        this.completedExamRecordList = data.examPaperAnswers
          .map(item => {
            if (item.examStatus === "2") {
              return item
            }
          })
          .filter(ite => typeof ite !== "undefined")
      },

      jumpTo() {
        uni.navigateTo({
          url: `/pages/mine/exam/examing/index?baseId=${this.baseId}&arrangeId=${
            this.arrangeId
          }&remark=${this.remark}&submintMinimumNumber=${
            this.examDetailInfo.examConfig.submintMinimumNumber || 0
          }`
        })
      },

      displayContent(startTime, endTime) {
        var now = new Date() // 获取当前时间
        var start = new Date(startTime) // 将开始时间转换为Date对象
        var end = new Date(endTime) // 将结束时间转换为Date对象

        if (now < start) {
          return "还未开考"
        } else if (now > end) {
          return "考试已结束"
        }
      }
    }
  }
</script>

<style lang="scss" scoped>
  .competition {
    line-height: 80rpx;
    font-size: 16px;
    text-align: center;
    background: #fff;
    font-weight: 700;
  }
  .disable {
    background: #d7d7d7 !important;
  }
  .nodata {
    padding-top: 50rpx;
    line-height: 60px;
    font-size: 14px;
    color: #aaaaaa;
    text-align: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    image {
      width: 300rpx;
      height: 300rpx;
    }
  }
  .highlight {
    color: #d01f25 !important;
    font-weight: bold;
    margin: 0 6rpx;
  }
  .exam-detail {
    background: #fff;
    border: 1px solid #d7d7d7;
    padding: 20rpx;
    margin: 20rpx 0;
    border-radius: 10rpx;
  }
  .exam-log {
    color: #e79930;
    border-bottom: 1px solid #e79930;
    line-height: 60rpx;
    font-weight: bold;
    font-size: 16px;
  }
  .exam-con {
    background-color: #fff;
    padding: 10rpx 30rpx 0rpx 30rpx;
  }
  .exam-list {
    background: #f5f5f9;
    padding: 10rpx 30rpx 30rpx 30rpx;
    min-height: calc(100vh - 200px);
  }
  .con {
    min-height: 100vh;
    border-top: 1px solid #f2f2f2;

    .learn-btn {
      margin-top: 0rpx;
      font-size: 12px;
      text-align: center;
      padding-bottom: 10rpx;
      display: flex;
      justify-content: center;
      align-items: center;

      > button {
        font-size: 32rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        background: #ed9d32;
        color: #fff;
        height: 70rpx;
        margin: 30rpx 0;
        width: 70%;
      }
    }
    .view-btn {
      font-size: 12px;
      text-align: center;
      margin-top: 10rpx;
      > button {
        background: #ed9d32;
        color: #fff;
        width: 100%;
      }
    }
    .title {
      font-size: 20px;
      font-weight: bold;
      line-height: 80rpx;
      text-align: center;
    }
    .sub-title {
      font-size: 16px;
      font-weight: bold;
    }

    .content {
      display: flex;
      align-items: center;
      height: 36px;
      font-size: 16px;
    }
  }
  .navigationBar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 90rpx;
    width: 100%;
    background-color: #fff;

    .left,
    .right {
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .left {
      margin-left: 50rpx;

      .iconfont {
        font-weight: bold;
      }

      .info {
        margin-left: 20rpx;
        width: 60rpx;
        height: 60rpx;
        display: flex;
        background-color: #c7dbf7;
        border-radius: 50%;
        justify-content: center;
        align-items: center;

        .icon-shichang {
          font-size: 38rpx;
          color: #7daef3;
        }
      }

      .text {
        margin-left: 15rpx;
        font-weight: bold;
      }
    }

    .right {
      margin-right: 70rpx;
      font-size: 25rpx;
      color: grey;
    }
  }
</style>

<!--
 * @Description: 北蔡防灾减灾-回顾页面
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-12-20 13:56:50
 * @LastEditTime: 2024-02-04 15:02:32
-->
<template>
  <view class="con">
    <navigationBar :path="`/pages/town/index`" :showPersonal="false" />
    <custom-tabs type="a1" :value="activeTab" @change="tabChange">
      <custom-tab-pane label="做题记录" name="a1_1"> </custom-tab-pane>
      <custom-tab-pane label="考试记录" name="a1_2"> </custom-tab-pane>
    </custom-tabs>

    <view class="exam-list">
      <view class="exam-log">{{ activeTab == 0 ? "做题记录" : "考试记录" }}</view>
      <template
        v-if="
          activeTab == 0
            ? exerciseRecordData && exerciseRecordData.length > 0
            : examRecordData && examRecordData.length > 0
        "
      >
        <view
          class="exam-detail"
          v-for="(item, index) in activeTab == 0 ? exerciseRecordData : examRecordData"
          :key="index"
        >
          <view class="content">
            <text class="sub-title">测试时间：</text>
            <text class="sub-content">{{ item.startTime }}</text>
          </view>
          <view class="content">
            <text class="sub-title">交卷时间：</text>
            <text class="sub-content">{{ item.submitTime }}</text>
          </view>
          <view class="content">
            <text class="sub-title">测试成绩：</text>
            <text class="sub-content highlight">
              {{ item.userPaperScore }}
            </text>
          </view>
          <view class="view-btn">
            <button size="mini" @click="handelExamDetail(item)" style="font-size: 15px">
              查看详情
            </button>
          </view>
        </view>
      </template>
      <view v-else class="nodata">
        <image src="@/static/images/competition/no-data.png"> </image>
        <text class="nodata-text">暂无测试记录</text>
      </view>
    </view>
  </view>
</template>

<script>
  import { getTownHistoryList } from "@/api/exam/base.js"
  export default {
    onLoad(options) {
      this.loadRecordData()
    },
    data() {
      return {
        activeTab: 0,
        exerciseRecordData: [],
        examRecordData: []
      }
    },
    methods: {
      handelExamDetail(item) {
        uni.navigateTo({
          url: `/pages/town/examDetail?baseId=${item.baseId}&arrangeId=${item.arrangeId}&paperAnswerId=${item.paperAnswerId}`
        })
      },
      async loadRecordData() {
        const { data } = await getTownHistoryList(this.arrangeId)
        this.exerciseRecordData = data["2066"]
          ?.map(item => {
            if (item.examStatus === "2") {
              return item
            }
          })
          .filter(ite => typeof ite !== "undefined")
        this.examRecordData = data["2057"]
          ?.map(item => {
            if (item.examStatus === "2") {
              return item
            }
          })
          .filter(ite => typeof ite !== "undefined")
      },
      tabChange(val) {
        this.activeTab = val.value
      }
    }
  }
</script>

<style lang="scss" scoped>
  .nodata {
    padding-top: 50rpx;
    line-height: 60px;
    font-size: 14px;
    color: #aaaaaa;
    text-align: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    image {
      width: 300rpx;
      height: 300rpx;
    }
  }
  .highlight {
    color: #d01f25 !important;
    font-weight: bold;
    margin: 0 6rpx;
  }
  .exam-detail {
    background: #fff;
    border: 1px solid #d7d7d7;
    padding: 20rpx;
    margin: 20rpx 0;
    border-radius: 10rpx;
  }
  .exam-log {
    color: #e79930;
    border-bottom: 1px solid #e79930;
    line-height: 60rpx;
    font-weight: bold;
    font-size: 16px;
  }
  .exam-list {
    background: #f5f5f9;
    padding: 10rpx 30rpx 30rpx 30rpx;
    min-height: calc(100vh - 200px);
  }
  .con {
    min-height: 100vh;
    border-top: 1px solid #f2f2f2;
    .view-btn {
      font-size: 12px;
      text-align: center;
      margin-top: 10rpx;
      > button {
        background: #ed9d32;
        color: #fff;
        width: 100%;
      }
    }
    .sub-title {
      font-size: 16px;
      font-weight: bold;
    }

    .content {
      display: flex;
      align-items: center;
      height: 36px;
      font-size: 16px;
    }
  }
</style>

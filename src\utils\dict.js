/*
 * @Description:
 * @Author: <PERSON>
 * @LastEditors: <PERSON><PERSON>
 * @Date: 2023-05-24 15:02:16
 * @LastEditTime: 2023-05-25 11:16:55
 */
import store from "@/store"
import { getDicts } from "@/api/system/dict/data"

/**
 * 获取字典数据
 */
export async function getDict(arg, labelName = "text", valueName = "value") {
  let opts
  const dicts = await store.dispatch("getDict", arg)
  if (dicts) {
    opts = dicts
  } else {
    const resp = await getDicts(arg)
    opts = resp.data.map(p => ({
      [labelName]: p.dictLabel,
      [valueName]: p.dictValue
    }))
    store.dispatch("setDict", { key: arg, value: opts })
  }

  return opts
}

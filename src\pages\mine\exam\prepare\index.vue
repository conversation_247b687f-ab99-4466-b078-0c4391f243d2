<!--
 * @Description: 考试准备页面
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-06-25 17:09:38
 * @LastEditTime: 2025-06-20 08:55:51
-->
<template>
  <view class="con">
    <navigationBar
      v-if="showNavBar != 'false'"
      :path="`/pages/mine/exam/index?showNavBar=${this.showNavBar}`"
      :showPersonal="false"
    />
    <view>
      <view class="exam-con" v-if="examDetailInfo.examConfig">
        <view class="title"> {{ examDetailInfo.baseName }} </view>
        <view class="content">
          <text class="sub-title">开始时间：</text>
          <text class="sub-content" v-if="examRecordInfo.startTime">
            {{ examRecordInfo.startTime }}
          </text>
        </view>
        <view class="content">
          <text class="sub-title">结束时间：</text>
          <text class="sub-content" v-if="examRecordInfo.endTime">
            {{ examRecordInfo.endTime }}
          </text>
        </view>
        <view class="content">
          <text class="sub-title">考试时长：</text>
          <text class="sub-content">
            {{
              !examDetailInfo.examConfig.examDurationLimit ||
              examDetailInfo.examConfig.examDurationLimit === 0
                ? "不限时"
                : `${examDetailInfo.examConfig.examDurationLimit}分钟`
            }}
          </text>
        </view>
        <view class="content">
          <text class="sub-title">考试次数：</text>
          <text class="sub-content" v-if="examRecordInfo.remainExamCount == -1"> 无限次 </text>
          <text class="sub-content" v-else>
            还有
            <text class="highlight">{{ examRecordInfo.remainExamCount }} </text>
            次机会
          </text>
        </view>
        <view class="learn-btn">
          <button
            class="disable"
            v-if="!isInTimeRange(examRecordInfo.startTime, examRecordInfo.endTime)"
          >
            {{ displayContent(examRecordInfo.startTime, examRecordInfo.endTime) }}
          </button>
          <button v-else-if="examRecordInfo.remainExamCount != 0" size="mini" @click="jumpTo">
            开始考试
          </button>
          <button class="disable" v-else>已无考试次数</button>
        </view>
      </view>
      <view class="exam-list" v-if="!dontShowExamLogList.includes(domainName)">
        <view class="exam-log">考试记录</view>
        <template v-if="completedExamRecordList.length > 0">
          <view class="exam-detail" v-for="(item, index) in completedExamRecordList" :key="index">
            <view class="content">
              <text class="sub-title">考试时间：</text>
              <text class="sub-content">{{ item.startTime }}</text>
            </view>
            <view class="content">
              <text class="sub-title">交卷时间：</text>
              <text class="sub-content">{{ item.submitTime }}</text>
            </view>
            <view class="content" v-if="!noNeedExamScoreDomainList.includes(domainName)">
              <text class="sub-title">考试成绩： </text>
              <text class="sub-content highlight">
                {{ item.userPaperScore }}
              </text>
            </view>
            <view class="view-btn">
              <button size="mini" @click="handelExamDetail(item)" style="font-size: 15px">
                查看详情
              </button>
            </view>
          </view>
        </template>
        <view v-else class="nodata">
          <image src="@/static/images/competition/no-data.png"> </image>
          <text class="nodata-text">暂无考试详情</text>
        </view>
      </view>
    </view>

    <yangr-msg v-if="yangrMsgShow" :title="title" :info="info" :type="type" :btn="btn"></yangr-msg>
  </view>
</template>

<script>
  import { getExam, getArrange, getAnswerRecord } from "@/api/exam/base.js"
  import { isInTimeRange } from "@/utils/common"
  import { mapGetters } from "vuex"
  import { noNeedExamScoreDomainList } from "@/utils/constant"
  import yangrMsg from "@/components/yangr-msg/yangr-msg.vue"

  export default {
    components: {
      yangrMsg
    },
    onLoad(options) {
      if (options.showNavBar == "false") {
        this.showNavBar = options.showNavBar
      }
      this.arrangeId = options.arrangeId
      this.baseId = options.baseId
      this.taskId = options.taskId
      this.loadData()
      this.loadRecordData()
    },
    data() {
      return {
        noNeedExamScoreDomainList,
        examDetailInfo: {},
        examRecordInfo: {},
        baseId: "",
        arrangeId: "",
        taskId: "",
        completedExamRecordList: [], // 已完成考试列表
        showNavBar: "",
        dontShowExamLogList: ["zhzg"],
        title: "",
        info: "",
        type: "",
        yangrMsgShow: false,
        btn: ""
      }
    },

    computed: {
      ...mapGetters({
        domainName: "domainName"
      })
    },
    methods: {
      isInTimeRange,
      handelExamDetail(item) {
        uni.navigateTo({
          url: `/pages/mine/exam/examed/index?baseId=${this.baseId}&arrangeId=${this.arrangeId}&paperAnswerId=${item.paperAnswerId}&showNavBar=${this.showNavBar}`
        })
      },
      async loadData() {
        const { data } = await getExam(this.baseId)
        this.examDetailInfo = data
      },
      async loadRecordData() {
        const { data } = await getArrange(this.arrangeId)
        this.examRecordInfo = data
        // 已完成的考试才显示在考试记录中
        if (!data.examPaperAnswers || data.examPaperAnswers.length === 0) return
        this.completedExamRecordList = data.examPaperAnswers
          .map(item => {
            if (item.examStatus === "2") {
              return item
            }
          })
          .filter(ite => typeof ite !== "undefined")
      },

      async jumpTo() {
        if (["yuyuan", "zhzg"].includes(this.domainName)) {
          // 获取答卷记录状态
          const recordRes = await getAnswerRecord(this.arrangeId)

          if (recordRes.code === 200) {
            const recordStatus = recordRes.data

            // 根据返回状态处理不同逻辑
            if (recordStatus === 1) {
              this.title = "已完成培训流程，成绩合格，不需要重复参加"
              this.type = "error"
              this.yangrMsgShow = true
              setTimeout(() => {
                this.yangrMsgShow = false
              }, 3000)
              return
            } else if (recordStatus === 2) {
              this.title = "暂无考试机会"
              this.type = "error"
              this.yangrMsgShow = true
              setTimeout(() => {
                this.yangrMsgShow = false
              }, 3000)
              return
            }
            // recordStatus === 0 时继续执行原逻辑
          }
        }
        uni.navigateTo({
          url: `/pages/mine/exam/examing/index?baseId=${this.baseId}&arrangeId=${
            this.arrangeId
          }&taskId=${this.taskId}&submitMinimumNumber=${
            this.examDetailInfo.examConfig.submintMinimumNumber || 0
          }&showNavBar=${this.showNavBar}`
        })
      },

      displayContent(startTime, endTime) {
        const now = new Date() // 获取当前时间
        const start = new Date(startTime?.replace(/-/g, "/")) // 将开始时间转换为Date对象
        const end = new Date(endTime?.replace(/-/g, "/")) // 将结束时间转换为Date对象

        if (now < start) {
          return "还未开考"
        } else if (now > end) {
          return "考试已结束"
        }
      }
    }
  }
</script>

<style lang="scss" scoped>
  .disable {
    background: #d7d7d7 !important;
  }
  .nodata {
    padding-top: 50rpx;
    line-height: 60px;
    font-size: 14px;
    color: #aaaaaa;
    text-align: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    image {
      width: 300rpx;
      height: 300rpx;
    }
  }
  .highlight {
    color: #d01f25 !important;
    font-weight: bold;
    margin: 0 6rpx;
  }
  .exam-detail {
    background: #fff;
    border: 1px solid #d7d7d7;
    padding: 20rpx;
    margin: 20rpx 0;
    border-radius: 10rpx;
  }
  .exam-log {
    color: #e79930;
    border-bottom: 1px solid #e79930;
    line-height: 60rpx;
    font-weight: bold;
    font-size: 16px;
  }
  .exam-con {
    background-color: #fff;
    padding: 10rpx 30rpx 0rpx 30rpx;
  }
  .exam-list {
    background: #f5f5f9;
    padding: 10rpx 30rpx 30rpx 30rpx;
    min-height: calc(100vh - 200px);
  }
  .con {
    min-height: 100vh;
    border-top: 1px solid #f2f2f2;

    .learn-btn {
      margin-top: 0rpx;
      font-size: 12px;
      text-align: center;
      padding-bottom: 10rpx;
      display: flex;
      justify-content: center;
      align-items: center;

      > button {
        font-size: 32rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        background: #ed9d32;
        color: #fff;
        height: 70rpx;
        margin: 30rpx 0;
        width: 70%;
      }
    }
    .view-btn {
      font-size: 12px;
      text-align: center;
      margin-top: 10rpx;
      > button {
        background: #ed9d32;
        color: #fff;
        width: 100%;
      }
    }
    .title {
      font-size: 20px;
      font-weight: bold;
      line-height: 80rpx;
      text-align: center;
    }

    .content {
      display: flex;
      align-items: center;
      height: 36px;
      font-size: 16px;
      .sub-title {
        min-width: 150rpx;
        font-weight: bold;
      }
    }
  }
</style>

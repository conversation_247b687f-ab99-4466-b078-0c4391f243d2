<template>
  <view class="login" :style="`background-image: url('${assetsPrefixUrl}fzjz/fzjz-login-bg.jpg');`">
    <view class="login-body">
      <view class="login-form">
        <view class="login-form-one">
          <view class="login-form-one-tel">
            <uni-easyinput
              maxlength="11"
              :inputBorder="false"
              v-model="tel"
              :styles="stylesTel"
              placeholderStyle="font-size:16px"
              placeholder="手机号码"
            ></uni-easyinput>
          </view>
          <!-- <view class="login-form-one-code">
            <uni-easyinput
              :inputBorder="false"
              v-model="code"
              :styles="styles"
              placeholderStyle="font-size:16px"
              placeholder="短信验证码"
              primaryColor="white"
            ></uni-easyinput>
            <view class="code" :class="{ disabled: isDisabled }" @click="getCode">{{
              codeText
            }}</view>
          </view> -->
        </view>
      </view>
      <img src="@/static/images/fzjz/login-button.png" class="login-button" @click="login" alt="" />
      <!-- <view class="login-tip">未注册的手机号将默认开通平台账号</view> -->
    </view>
  </view>
</template>

<script>
  import { sendCode } from "@/api/competition/index"
  import { assetsPrefixUrl } from "@/utils/constant.js"

  export default {
    data() {
      return {
        assetsPrefixUrl,
        tel: "",
        code: "",
        isDisabled: false,
        countDownSecond: 60,
        timer: null,
        codeText: "获取验证码",
        stylesTel: {
          borderColor: "#8c8988"
        },
        styles: {
          borderColor: "transparent"
        }
      }
    },
    created() {},
    computed: {},
    methods: {
      async getCode() {
        if (!this.isDisabled) {
          if (!this.tel) {
            return uni.showToast({
              icon: "none",
              title: "手机号不能为空"
            })
          }
          if (!/^1(3|4|5|7|8|9|6)\d{9}$/i.test(this.tel)) {
            return uni.showToast({
              icon: "none",
              title: "请输入正确的手机号码"
            })
          }
          let queryData = {
            mobile: this.tel,
            scene: 5
          }
          const res = await sendCode(queryData)
          this.$modal.msgSuccess("验证码发送成功！")
          this.startCountDown()
        }
      },

      // 验证码倒计时
      startCountDown() {
        this.isDisabled = true
        this.countDownSecond = 60
        this.codeText = "60s"
        this.timer = setInterval(() => {
          this.countDownSecond--
          if (this.countDownSecond <= 0) {
            clearInterval(this.timer)
            this.codeText = "获取验证码"
            this.isDisabled = false
          } else {
            this.codeText = `${this.countDownSecond}s`
          }
        }, 1000)
      },

      // 登录
      async login() {
        if (!this.tel) {
          return uni.showToast({
            icon: "none",
            title: "手机号不能为空"
          })
        }
        if (!/^1(3|4|5|7|8|9|6)\d{9}$/i.test(this.tel)) {
          return uni.showToast({
            icon: "none",
            title: "请输入正确的手机号码"
          })
        }
        let queryData = {
          mobile: this.tel,
          scene: 5
        }
        this.$store.dispatch("unepLogin", queryData).then(() => {
          uni.switchTab({
            url: "/pages/fzjz/index"
          })
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
  .login {
    display: flex;
    flex-direction: column;
    align-items: center;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-position: top;
    height: 100vh;
    .login-body {
      height: 500px;
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 90%;
      margin-top: 450rpx;
    }
    .login-form {
      width: 80%;
      .login-form-one {
        .login-form-one-tel {
          border: 1px solid #8c8988;
          background-color: white;
          border-radius: 10px;
        }
        .login-form-one-code {
          background-color: white;
          border-radius: 10px;
          display: flex;
          align-items: center;
          margin-top: 30px;
          border: 1px solid #8c8988;
          .code {
            width: 35%;
            font-size: 14px;
            text-align: center;
            border-left: 1px solid #8c8988;
            margin-left: 10px;
            color: #5b5b5b;
          }
          .disabled {
            color: #aaaaaa;
          }
        }
      }
    }
    .login-tip {
      margin-top: 20px;
      color: #6d6767;
      font-size: 14px;
      margin-top: 20px;
      text-align: center;
    }
    .login-button {
      margin-top: 50px;
      height: 40px;
      line-height: 40px;
    }
  }

  ::v-deep .uni-easyinput__content-input {
    height: 50px;
    font-size: 18px;
  }
</style>

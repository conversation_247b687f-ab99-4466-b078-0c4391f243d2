<!--
 * @Description: 人脸识别页面
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-12-04 09:43:09
 * @LastEditTime: 2024-12-24 15:54:36
-->

<template>
  <view class="fixed inset-0 w-full h-full bg-black">
    <!-- 视频层 -->
    <video
      ref="video"
      class="absolute inset-0 w-full h-full object-cover"
      :show-center-play-btn="false"
      :show-progress="false"
      :device-position="device"
      flash="off"
      @error="error"
      muted
      preload
    ></video>

    <!-- 遮罩层 -->
    <cover-view class="absolute inset-0 flex flex-col">
      <!-- 背景遮罩 -->
      <cover-image
        class="absolute inset-0 w-full h-full object-cover"
        src="@/static/images/allrlk.png"
      ></cover-image>

      <!-- 顶部栏 -->
      <cover-view class="relative w-full h-[44px] flex items-center bg-black/50">
        <cover-image
          @click="close"
          class="w-[24px] h-[24px] ml-[15px]"
          src="@/static/images/gb.png"
        ></cover-image>
      </cover-view>

      <!-- 中间内容区 -->
      <cover-view class="relative flex-1 flex flex-col items-center justify-center">
        <!-- 人脸识别框 -->
        <cover-view class="w-[280px] h-[280px] relative">
          <cover-view
            class="absolute top-0 left-0 w-[30px] h-[30px] border-t-2 border-l-2 border-white"
          ></cover-view>
          <cover-view
            class="absolute top-0 right-0 w-[30px] h-[30px] border-t-2 border-r-2 border-white"
          ></cover-view>
          <cover-view
            class="absolute bottom-0 left-0 w-[30px] h-[30px] border-b-2 border-l-2 border-white"
          ></cover-view>
          <cover-view
            class="absolute bottom-0 right-0 w-[30px] h-[30px] border-b-2 border-r-2 border-white"
          ></cover-view>
        </cover-view>
      </cover-view>

      <!-- 底部区域 -->
      <cover-view class="relative w-full flex flex-col">
        <!-- 提示文字 -->
        <cover-view class="w-full py-4 flex flex-col items-center justify-center bg-black/50">
          <cover-view class="text-white text-sm">请将面部放入框内</cover-view>
          <cover-view class="text-white text-sm mt-1">注意：光线要充足，人脸正对摄像头</cover-view>
        </cover-view>

        <!-- 操作按钮 -->
        <cover-view class="w-full h-[120px] flex items-center justify-around bg-black/80">
          <cover-image
            @click="deviceQH"
            class="w-[35px] h-[35px]"
            :class="hasSwitch ? '' : 'opacity-0'"
            src="@/static/images/qh.png"
          ></cover-image>
          <cover-image
            @click="photoShoot"
            class="w-[60px] h-[60px]"
            src="@/static/images/pz.png"
          ></cover-image>
        </cover-view>
      </cover-view>
    </cover-view>
  </view>
</template>

<script>
  import { getTaskInfoById } from "@/api/trainingTask"
  import { faceVerify } from "@/api/course/verify"

  const constraints = {
    audio: false,
    video: {
      width: 600,
      height: 400,
      facingMode: "user"
    }
  }

  export default {
    data() {
      return {
        hasSwitch: true,
        hasAlbum: true,
        initScale: 1.277,
        original: true,
        canvasW: 0,
        canvasH: 0,
        device: "front",
        videoShow: false,
        pictureShow: false,
        // 图片地址
        picture: "",
        // 用于视频识别的节点
        canvas: null,
        video: null,
        image: null,
        timeout: 0,
        // 模型识别的条件
        options: "",
        // 提示控制
        noOne: "",
        moreThanOne: "",
        // 不是通过Https访问提示
        httpsAlert: "",
        videoWidth: 600,
        videoHeight: 400,
        useFrontCamera: true,
        taskInfo: null,
        taskId: "",
        subTaskType: "",
        subTaskId: "",
        subTaskName: "",
        arrangeId: ""
      }
    },

    mounted() {
      this.video = this.$refs.video
      uni.getSystemInfo({
        success: res => {
          this.videoWidth = Math.max(res.windowWidth, res.windowHeight) - 120
          constraints.video.width = this.videoWidth
          this.videoHeight = Math.min(res.windowWidth, res.windowHeight)
          constraints.video.height = this.videoHeight
        }
      })
      this.useCamera()
    },

    async onLoad(options) {
      this.taskId = options.taskId
      this.subTaskType = options.subTaskType
      this.subTaskId = options.subTaskId
      this.subTaskName = options.subTaskName
      if (options.arrangeId) {
        this.arrangeId = options.arrangeId
      }

      // 获取任务信息
      if (this.taskId) {
        try {
          const res = await getTaskInfoById(this.taskId)
          if (res.code === 200) {
            this.taskInfo = res.data
          }
        } catch (error) {
          console.error("获取任务信息失败:", error)
        }
      }
    },

    computed: {
      scale: function () {
        if (this.initScale > 1.77) return 1.77
        if (this.initScale < 1.27) return 1.27
        return this.initScale
      }
    },

    methods: {
      /**
       * 使用摄像头
       */
      useCamera() {
        if (window.stream) {
          window.stream.getTracks().forEach(track => {
            track.stop()
          })
        }

        // 如果不是通过loacalhost或者通过https访问会将报错捕获并提示
        try {
          if (navigator.mediaDevices === undefined) {
            navigator.mediaDevices = {}
          }
          if (navigator.mediaDevices.getUserMedia === undefined) {
            navigator.mediaDevices.getUserMedia = function (constraints) {
              const getUserMedia =
                navigator.getUserMedia ||
                navigator.webkitGetUserMedia ||
                navigator.mozGetUserMedia ||
                navigator.msGetUserMedia ||
                navigator.oGetUserMedia
              if (!getUserMedia) {
                return Promise.reject(new Error("getUserMedia is not implemented in this browser"))
              }
              return new Promise(function (resolve, reject) {
                getUserMedia.call(navigator, constraints, resolve, reject)
              })
            }
          }

          let promise = navigator.mediaDevices.getUserMedia(constraints)
          promise
            .then(stream => {
              window.stream = stream
              // 返回参数
              const video = document.querySelector("video")
              if ("srcObject" in video) {
                video.srcObject = stream
              } else {
                // 防止在新的浏览器里使用它，应为它已经不再支持了
                video.src = window.URL.createObjectURL(stream)
              }
              video.onloadedmetadata = function (e) {
                video.play()
              }
            })
            .catch(error => {
              console.log(error)
              this.handleNoCameraCase("摄像头初始化失败，请检查权限设置或刷新重试")
            })
        } catch (err) {
          this.httpsAlert = `您现在在使用非Https访问，
                请先在chrome://flags/#unsafely-treat-insecure-origin-as-secure中修改配置,
                添将当前链接${window.location.href}添加到列表,
                并且将Insecure origins treated as secure修改为enabled,
                修改完成后请重启浏览器后再次访问！`
          this.handleNoCameraCase(this.httpsAlert)
        }
      },

      // 关闭摄像头
      closeCamera() {
        if (!this.$refs["video"].srcObject) return
        let stream = this.$refs["video"].srcObject
        let tracks = stream.getTracks()
        tracks.forEach(track => {
          track.stop()
        })
        this.$refs["video"].srcObject = null
      },

      /**
       * 拍照上传
       */
      photoShoot() {
        // 拿到图片的base64
        this.canvas = document.createElement("canvas")
        let context = this.canvas.getContext("2d")
        const video = document.querySelector("video")
        this.canvas.width = Math.min(video.videoWidth, video.videoHeight)
        this.canvas.height = Math.max(video.videoWidth, video.videoHeight)
        context.drawImage(video, 0, 0, this.canvas.width, this.canvas.height)

        let canvas = this.canvas.toDataURL("image/png")
        // 拍照以后将video隐藏
        // 停止摄像头成像
        video.srcObject.getTracks()[0].stop()
        video.pause()
        if (canvas) {
          // 拍照将base64转为file流文件
          let blob = this.dataURLtoBlob(canvas)
          let file = this.blobToFile(
            blob,
            `${this.$store.state.user.nickName}-${new Date().getTime()}.jpg`
          )
          this.verifyFace(file)
        } else {
          console.log("canvas生成失败")
        }
      },

      async verifyFace(file) {
        try {
          // 将 blob 转换为临时文件路径
          const tempFilePath = await new Promise((resolve, reject) => {
            const reader = new FileReader()
            reader.onload = () => {
              resolve(reader.result)
            }
            reader.onerror = reject
            reader.readAsDataURL(file)
          })

          const params = {
            tempFilePath,
            terminalSource: "1", // 固定为手机端
            userId: this.$store.state.user.userId,
            userName: this.$store.state.user.nickName,
            deptId: this.$store.state.user.deptInfo.deptId,
            deptName: this.$store.state.user.deptInfo.deptName,
            taskId: this.taskId || "",
            subTaskType: this.subTaskType || "",
            subTaskId: this.subTaskId || "",
            subTaskName: this.subTaskName || "",
            arrangeId: this.arrangeId || ""
          }

          const res = await faceVerify(params)

          if (res.code === 200) {
            uni.showToast({
              title: "人脸识别成功",
              icon: "success"
            })
            // 如果是考试场景，需要保存 taskMonitorId
            if (this.subTaskType === "1" && res.data) {
              this.navigateToDetail(true, res.data)
            } else {
              this.navigateToDetail(true)
            }
          } else {
            this.handleVerifyFail(res.msg || "验证失败")
          }
        } catch (error) {
          console.error("人脸识别请求失败:", error)
          this.handleVerifyFail(error.message || "验证失败，请重试")
        }
      },

      /**
       * 将图片转为blob格式
       * dataurl 拿到的base64的数据
       */
      dataURLtoBlob(dataurl) {
        let arr = dataurl.split(","),
          mime = arr[0].match(/:(.*?);/)[1],
          bstr = atob(arr[1]),
          n = bstr.length,
          u8arr = new Uint8Array(n)
        while (n--) {
          u8arr[n] = bstr.charCodeAt(n)
        }
        return new Blob([u8arr], {
          type: mime
        })
      },

      /**
       * 生成文件信息
       * theBlob 文件
       * fileName 文件名字
       */
      blobToFile(theBlob, fileName) {
        theBlob.lastModifiedDate = new Date().toLocaleDateString()
        theBlob.name = fileName
        return theBlob
      },

      deviceQH() {
        this.useFrontCamera = !this.useFrontCamera
        if (window.stream) {
          window.stream.getTracks().forEach(track => {
            track.stop()
          })
        }
        constraints.video.facingMode = this.useFrontCamera ? "user" : { exact: "environment" }
        this.useCamera()
      },

      close() {
        this.closeCamera() // 关闭摄像头
        this.navigateToDetail(false)
      },

      error(e) {
        console.log(e)
        // this.handleNoCameraCase("摄像头发生错误，请检查权限设置或刷新重试")
      },

      handleNoCameraCase(message = "未检测到摄像头设备或未获得摄像头权限") {
        const continueText =
          this.taskInfo?.recordCredit === "1" ? "继续学习将不记录学分" : "继续学习依旧会记录学分"

        uni.showModal({
          title: "提示",
          content: `${message}，${continueText}`,
          confirmText: "继续学习",
          cancelText: "取消",
          success: res => {
            this.navigateToDetail(false)
          }
        })
      },

      navigateToDetail(verifySuccess, taskMonitorId) {
        let url
        switch (this.subTaskType) {
          case "0":
            url = `/pages/course/detail/index?id=${this.subTaskId}&taskId=${this.taskId}&verifySuccess=${verifySuccess}`
            break
          case "1":
            url = `/pages/mine/exam/examing/index?baseId=${this.subTaskId}&arrangeId=${this.arrangeId}&taskId=${this.taskId}&verifySuccess=${verifySuccess}`
            // 如果有 taskMonitorId，添加到 URL 中
            if (taskMonitorId) {
              url += `&taskMonitorId=${taskMonitorId}`
            }
            break
        }
        uni.redirectTo({ url })
      },

      // 添加处理验证失败的方法
      handleVerifyFail(message = "验证失败") {
        uni.showModal({
          title: "提示",
          content: message,
          confirmText: "重试",
          cancelText: "取消",
          success: res => {
            if (res.confirm) {
              this.useCamera() // 重新开启摄像头重试
            } else {
              this.navigateToDetail(false)
            }
          }
        })
      }
    },

    beforeDestroy() {
      this.closeCamera()
    },

    onUnload() {
      this.closeCamera()
    }
  }
</script>

<!--
 * @Description: 
 * @Author: <PERSON><PERSON>
 * @LastEditors: <PERSON><PERSON>
 * @Date: 2023-05-11 10:26:11
 * @LastEditTime: 2023-06-01 16:02:57
-->
<template>
  <view class="tab-pane">
    <template v-if="show">
      <slot></slot>
    </template>
    <template v-else>
      <view style="width: 750rpx; height: 0"></view>
    </template>
  </view>
</template>
<script>
export default {
  props: ["label", "name"],
  data() {
    return {
      timer: 0,
      show: false,
      watchTabKey: `watchTabValue_${this.name.split("_")[0]}`,
      putChangeKey: `putChange_${this.name.split("_")[0]}`
    }
  },
  created() {
    uni.$on(this.putChangeKey, item => {
      if (this.timer) clearTimeout(this.timer)
      this.show = true
      this.timer = setTimeout(() => {
        this.show = this.name == item.name
      }, item.duration)
    })
  },
  watch: {
    label: {
      deep: true,
      handler(newValue, oldValue) {
        uni.$emit(this.watchTabKey, {
          newValue: newValue,
          oldValue: oldValue,
          name: this.name
        })
      },
      immediate: true
    }
  }
}
</script>
<style lang="scss" scoped>
.tab-pane {
  width: 100%;
  // #ifndef APP-PLUS-NVUE
  flex-shrink: 0;
  // #endif
}
</style>

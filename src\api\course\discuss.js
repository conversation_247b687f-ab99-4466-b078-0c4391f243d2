/*
 * @Description:评论接口
 * @Author: <PERSON><PERSON>
 * @LastEditors: <PERSON><PERSON>
 * @Date: 2023-05-11 11:17:43
 * @LastEditTime: 2023-06-01 15:06:30
 */
import request from "@/utils/request"

// 课程列表
export function listDiscuss(params) {
  return request({
    url: "/course/course-discuss/list",
    method: "get",
    params
  })
}

// 发布评论
export function addCourseDiscuss(data) {
  return request({
    url: "/course/course-discuss",
    method: "post",
    data
  })
}

// 删除评论
export function deleteCourseDiscuss(data) {
  return request({
    url: `/course/course-discuss/${data.id}`,
    method: "delete"
  })
}

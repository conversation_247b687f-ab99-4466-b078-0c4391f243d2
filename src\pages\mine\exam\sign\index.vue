<template>
  <view class="signature">
    <view class="signature-button-wrap">
      <view class="signature-button">
        <button type="warn" @click="clear">重写</button>
        <button type="primary" @click="finish">保存</button>
      </view>
    </view>
    <canvas
      style="z-index: 999"
      ref="canvasRef"
      class="signature-canvas"
      canvas-id="mycanvas"
      @touchstart="touchstart"
      @touchmove="touchmove"
      @touchend="touchend"
    ></canvas>

    <!--用于旋转图片的canvas容器-->
    <canvas style="position: absolute" canvas-id="handWriting2"></canvas>
  </view>
</template>

<script>
  import { fileUpload } from "@/api/system/user"
  export default {
    emits: ["submitSign"],
    data() {
      return {
        ctx: "", // 绘图图像
        points: [], // 路径点集合
        canvasw: 0, // 画布宽度
        canvash: 0 // 画布高度
      }
    },
    mounted() {
      this.createCanvas()
    },
    methods: {
      //创建并显示画布
      createCanvas: function () {
        let that = this
        uni.getSystemInfo({
          success: function (res) {
            that.canvasw = res.windowWidth
            that.canvash = res.windowHeight
            that.ctx = uni.createCanvasContext("mycanvas", that) //创建绘图对象
            that.ctx.setFillStyle("#ffffff") // 设置画笔填充样式为白色
            that.ctx.fillRect(0, 0, that.canvasw, that.canvash) // 填充整个画布为白色背景
            that.ctx.setStrokeStyle("#1f1f1f")
            //设置画笔样式
            that.ctx.lineWidth = 4
            that.ctx.lineCap = "round"
            that.ctx.lineJoin = "round"
          }
        })
      },
      //触摸开始，获取到起点
      touchstart: function (e) {
        let startX = e.changedTouches[0].x
        let startY = e.changedTouches[0].y
        let startPoint = {
          X: startX,
          Y: startY
        }
        this.points.push(startPoint)
        //每次触摸开始，开启新的路径
        this.ctx.beginPath()
      },
      //触摸移动，获取到路径点
      touchmove: function (e) {
        let moveX = e.changedTouches[0].x
        let moveY = e.changedTouches[0].y
        let movePoint = {
          X: moveX,
          Y: moveY
        }
        this.points.push(movePoint) //存点
        let len = this.points.length
        if (len >= 2) {
          this.draw() //绘制路径
        }
      },
      // 触摸结束，将未绘制的点清空防止对后续路径产生干扰
      touchend: function () {
        this.points = []
      },
      /* ***********************************************
        #   绘制笔迹
        #	1.为保证笔迹实时显示，必须在移动的同时绘制笔迹
        #	2.为保证笔迹连续，每次从路径集合中区两个点作为起点（moveTo）和终点(lineTo)
        #	3.将上一次的终点作为下一次绘制的起点（即清除第一个点）
        ************************************************ */
      draw: function () {
        let point1 = this.points[0]
        let point2 = this.points[1]
        this.points.shift()
        this.ctx.moveTo(point1.X, point1.Y)
        this.ctx.lineTo(point2.X, point2.Y)
        this.ctx.stroke()
        this.ctx.draw(true)
      },

      //清空画布
      clear: function () {
        this.ctx.clearRect(0, 0, this.canvasw, this.canvash)
        this.ctx.draw(true)
      },
      //完成绘画并保存到本地
      finish() {
        let that = this
        uni.canvasToTempFilePath({
          canvasId: "mycanvas",
          success: res => {
            that.rotate(res.tempFilePath)
          }
        })
      },
      // 旋转图片，生成新canvas实例
      rotate(tempFilePaths) {
        const _this = this
        uni.getImageInfo({
          // 获取图片的信息
          src: tempFilePaths,
          success: res1 => {
            console.log("res1", res1)
            // 将canvas1的内容复制到canvas2中
            let canvasContext = uni.createCanvasContext("handWriting2", this)
            canvasContext.setFillStyle("#ffffff") // 设置画笔填充样式为白色
            canvasContext.fillRect(0, 0, _this.canvasw, _this.canvash) // 填充整个画布为白色背景
            let rate = res1.height / res1.width
            let width = 300 / rate
            let height = 300
            _this.cavWidth = 300 / rate
            _this.cavWidth1 = 300
            canvasContext.translate(height / 2, width / 2)
            canvasContext.rotate((270 * Math.PI) / 180)
            canvasContext.drawImage(tempFilePaths, -width / 2, -height / 2, width, height)
            canvasContext.draw(false, data => {
              // 将之前在绘图上下文中的描述（路径、变形、样式）画到 canvas 中
              uni.canvasToTempFilePath(
                {
                  // 把当前画布指定区域的内容导出生成指定大小的图片。在 draw() 回调里调用该方法才能保证图片导出成功。
                  canvasId: "handWriting2",
                  fileType: "png",
                  quality: 1, //图片质量
                  success(res2) {
                    // 上传到服务器
                    fileUpload(res2.tempFilePath).then(finalRes => {
                      if (finalRes.code === 200) {
                        _this.$emit("submitSign", finalRes.data.url)
                        _this.clear()
                      } else {
                        return uni.showToast({
                          title: "保存失败，请稍后再试",
                          icon: "error"
                        })
                      }
                    })
                  }
                },
                _this
              )
            })
          }
        })
      }
    }
  }
</script>

<style lang="scss">
  .signature {
    padding: 0 10px;
    display: flex;

    &-canvas {
      height: 100vh;
      width: 90%;
      box-sizing: border-box;
      border: 1px solid lightgrey;
    }

    &-button-wrap {
      width: 50px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    &-button {
      display: flex;
      margin-right: 10px;
      white-space: nowrap;
      transform: rotate(90deg);

      button {
        margin-right: 20px;
      }
    }
  }
</style>

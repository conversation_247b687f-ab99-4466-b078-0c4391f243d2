<!--
 * @Description: 512长宁租户首页
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-06-05 11:23:01
 * @LastEditTime: 2024-08-19 10:51:54
-->

<template>
  <view class="home" :style="`background-image: url('${assetsPrefixUrl}fzjz/fzjz-bg.jpg');`">
    <view class="home-body">
      <!-- 自然灾害 -->
      <view class="home-body-menu">
        <img class="home-body-menu-img" :src="`${assetsPrefixUrl}fzjz/zrzh-title.png`" alt="" />
        <view class="home-body-knowledge-list">
          <view
            class="manage-list-content"
            v-for="item in naturalDisasterList"
            :key="item.manageId"
          >
            <image class="img-con" :src="item.src" @click="handleJumpTo(item)" mode="widthFix" />
          </view>
        </view>
      </view>
      <image
        style="margin: 20px 0 20px"
        :src="`${assetsPrefixUrl}divider.png`"
        mode="widthFix"
      ></image>
      <!-- 安全防范 -->
      <view class="home-body-menu">
        <img class="home-body-menu-img" :src="`${assetsPrefixUrl}fzjz/aqff.png`" alt="" />
        <view class="home-body-animation-list">
          <img
            v-for="item in securityPrecautionsList"
            :src="item.src"
            @click="handleJumpTo(item)"
            alt=""
          />
        </view>
      </view>
      <image
        style="margin: 35px 0 20px"
        :src="`${assetsPrefixUrl}divider.png`"
        mode="widthFix"
      ></image>
      <!-- 历年回顾 -->
      <view class="home-body-menu">
        <img class="home-body-menu-img" :src="`${assetsPrefixUrl}fzjz/lnhg.png`" alt="" />
        <view class="home-body-review-list">
          <view class="manage-list-content" v-for="item in reviewList" :key="item.manageId">
            <image class="img-con" :src="item.src" @click="handleJumpTo(item)" mode="widthFix" />
          </view>
        </view>
        <!-- <view class="home-body-menu-more">
          <img
            @click="handleJumpTo('/pages/fzjz/managePop', 'history')"
            src="@/static/images/fzjz/more.png"
            alt=""
          />
        </view> -->
      </view>
    </view>
  </view>
</template>

<script>
  import { assetsPrefixUrl } from "@/utils/constant.js"

  export default {
    data() {
      return {
        assetsPrefixUrl,
        // 自然灾害
        naturalDisasterList: [
          {
            src: `${assetsPrefixUrl}fzjz/zrzh-knowledge.png`,
            catalogueId: 2230,
            type: "knowledge",
            path: "/pages/fzjz/managePop"
          },
          {
            src: `${assetsPrefixUrl}fzjz/zrzh-xjdh.png`,
            catalogueId: 2054,
            type: "course",
            path: "/pages/fzjz/courseList"
          }
        ],
        // 安全防范
        securityPrecautionsList: [
          {
            src: `${assetsPrefixUrl}fzjz/aqff-knowledge.png`,
            catalogueId: 2231,
            type: "knowledge",
            path: "/pages/fzjz/managePop"
          },
          {
            src: `${assetsPrefixUrl}fzjz/aqff-xjdh.png`,
            catalogueId: 2055,
            type: "course",
            path: "/pages/fzjz/courseList"
          }
        ],
        // 历年回顾
        reviewList: [
          {
            src: "/static/images/fzjz/2024-new.png",
            catalogueId: 2240,
            type: "histroy",
            path: "/pages/fzjz/managePop"
          },
          {
            src: "/static/images/fzjz/2023.png",
            catalogueId: 2238,
            type: "histroy",
            path: "/pages/fzjz/managePop"
          },
          {
            src: "/static/images/fzjz/2022.png",
            catalogueId: 2237,
            type: "histroy",
            path: "/pages/fzjz/managePop"
          },
          {
            src: "/static/images/fzjz/2021.png",
            catalogueId: 2236,
            type: "histroy",
            path: "/pages/fzjz/managePop"
          }
        ]
      }
    },
    methods: {
      // 跳转方法
      handleJumpTo(item) {
        const { path, type, catalogueId } = item
        uni.navigateTo({
          url: catalogueId
            ? `${path}?flag=${type}&catalogueId=${catalogueId}`
            : `${path}?flag=${type}`
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
  .home {
    display: flex;
    flex-direction: column;
    align-items: center;
    background-repeat: no-repeat;
    background-size: 100% auto;
    background-position: top;
    height: 100vh;

    .home-body {
      background-color: #f0f1f5;
      margin-top: 350rpx;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      .home-body-menu {
        display: flex;
        flex-direction: column;
        width: 100%;
        align-items: center;
        .home-body-menu-img {
          height: 35px;
        }
        .home-body-knowledge-list {
          width: 100%;
          padding: 28rpx 100rpx;
          margin-bottom: 15rpx;
          display: flex;
          flex-wrap: wrap;
          justify-content: space-between;
          .manage-list-content {
            padding: 0 8rpx;
            display: flex;
            justify-content: center;
            align-items: center;
            width: 46%;
            overflow: hidden;
            .img-con {
              position: relative;
              max-height: 280rpx;
              width: 100%;
            }
          }
        }
        .home-body-animation-list {
          padding: 28rpx 50rpx;
          width: 100%;
          display: flex;
          justify-content: space-evenly;
          > img {
            width: 35%;
          }
        }

        .home-body-review-list {
          padding: 28rpx 50rpx;
          display: flex;
          flex-wrap: wrap;
          justify-content: space-between;
          width: 100%;
          .manage-list-content {
            margin-bottom: 15rpx;
            display: flex;
            justify-content: center;
            align-items: center;
            width: 48%;
            overflow: hidden;
            .img-con {
              position: relative;
              width: 100%;
              max-height: 200rpx;
            }
          }
        }

        .home-body-menu-more {
          height: 15px;
          width: 75%;
          text-align: right;
          margin-bottom: 30px;

          > img {
            height: 26rpx;
          }
        }
      }
    }
  }
</style>

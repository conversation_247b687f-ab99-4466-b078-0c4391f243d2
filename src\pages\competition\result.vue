<!--
 * @Description: 
 * @Author: sunyunwu
 * @LastEditors: sunyunwu
 * @Date: 2023-09-19 17:32:35
 * @LastEditTime: 2023-10-13 16:21:48
-->
<template>
  <view class="con">
    <view class="title"
      ><!-- 防灾减灾知识达人评比活动 -->
      <image
        style="width: 340px; height: 40px"
        src="/static/images/competition/jianzai.png"
      ></image>
    </view>
    <view class="bg-title">
      <view class="tip-info">
        恭喜您！防灾减灾知识达人评比活动第2名，获得线上防灾减灾知识达人的称号。
      </view>
      <view class="tip-date">
        请于2023年10月13日14:00前，在古北黄金城道活动现场的综合服务区领取奖品。
      </view>
    </view>
    <view class="info">
      <view class="rank">2023年防灾减灾知识达人排名</view>
      <view class="btn">
        <button class="rank-num" size="default">
          您的成绩：第
          <text style="margin: 0 10rpx">{{ rank }}</text
          >名
        </button>
      </view>
      <view class="rank-table">
        <uni-table emptyText="暂无更多数据">
          <!-- 表头行 -->
          <uni-tr class="t-head">
            <uni-th width="50" align="center">排名</uni-th>
            <uni-th width="60" align="center">用户名</uni-th>
            <uni-th width="50" align="center">分数</uni-th>
            <uni-th align="center">获得时间</uni-th>
          </uni-tr>
          <!-- 表格数据行 -->
          <uni-tr
            :class="['t-body', item.ranking === rank ? 'check-item' : '']"
            v-for="item in dataList"
            :key="item.ranking"
          >
            <uni-td align="center" style="padding: 0">
              <image
                v-if="item.ranking === 1"
                class="top"
                src="/static/images/competition/one.png"
              ></image>
              <image
                class="top"
                v-else-if="item.ranking === 2"
                src="/static/images/competition/two.png"
              ></image>
              <image
                class="top"
                v-else-if="item.ranking === 3"
                src="/static/images/competition/three.png"
              ></image>
              <text v-else>{{ item.ranking }}</text>
              <view v-if="item.ranking === rank" class="isuser">我</view>
            </uni-td>
            <uni-td align="center">{{ item.mobile }}</uni-td>
            <uni-td align="center">{{ item.score }}</uni-td>
            <uni-td align="center">{{ item.time }}</uni-td>
          </uni-tr>
        </uni-table>
        <view class="more"
          ><text v-if="dataList.length !== allList.length" @click="handleMore"
            >查看更多</text
          >
          <text v-else @click="handleLess">收起</text>
        </view>
        <view class="tip">
          ※
          请凭借注册信息凭证，于2023年10月13日14:00前，在古北黄金城道活动现场的综合服务区确认相关信息。
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getUnepStudyRank } from "@/api/competition/index";
export default {
  data() {
    return {
      showMore: false,
      allList: [],
      dataList: [],
      rank: null,
    };
  },
  methods: {
    handleExam() {},
    handleLearn() {},
    handleMore() {
      this.dataList = this.allList;
    },
    handleLess() {
      this.dataList = this.allList.slice(0, 10);
    },
  },
  created() {
    // 获取名次
    getUnepStudyRank({ userId: uni.getStorageSync("userId") }).then((res) => {
      this.rank = res.rows[0]["ranking"];
    });
    // 获取列表
    getUnepStudyRank({ pageSize: 100 }).then((res) => {
      this.allList = res.rows;
      this.dataList = res.rows.slice(0, 10);
    });
  },
};
</script>

<style lang="scss" scoped>
.top {
  width: 50rpx;
  height: 50rpx;
  margin-top: 12rpx;
}
.check-item {
  > td {
    color: #d31f25 !important;
    font-weight: bold;
    position: relative;
    .isuser {
      position: absolute;
      width: 40rpx;
      height: 40rpx;
      border-radius: 50%;
      top: 18rpx;
      right: -16rpx;
      background: #ec8920;
      color: #fff;
      font-size: 10px;
    }
  }
}
.tip {
  color: #d01f25;
  line-height: 20px;
  font-size: 12px;
  font-weight: bold;
}
.rank-table {
  margin-top: 20rpx;
}

.t-head {
  background: #ec8920;
  > th {
    color: #fff;
  }
}
.t-body {
  background: #fbecd3;
  td {
    border-bottom: none;
  }
}
.rank-num {
  background: linear-gradient(to bottom, #03a7f0, #bf80ff);
  color: #fff;

  width: 400rpx;
  display: inline-block;
  font-weight: bold;
  font-size: 16px;
}

.btn {
  text-align: center;
}
.rank {
  line-height: 40px;
  font-size: 16px;
  color: #d01f52;
  font-weight: bold;
  text-align: center;
}
.bg-title {
  text-align: center;
  padding: 20rpx;
}
.tip-info {
  color: #fcff2d;
  font-size: 16px;
  line-height: 24px;
}
.tip-date {
  color: #fff;
  font-size: 14px;
  line-height: 20px;
  margin-top: 20rpx;
}
.info {
  padding: 0 30rpx 30rpx 30rpx;
}
.bg-title {
  background: #d01f25;
}
.con {
  min-height: 100vh;
  background: url(/static/images/competition/bg.png);
  background-size: cover;
}
.content {
  line-height: 26px;
  font-size: 15px;
  margin-top: 10rpx;
}
.title {
  line-height: 90rpx;
  /* font-size: 20px; */
  text-align: center;
  font-weight: bold;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100rpx;
}
.close-date {
  font-weight: bold;
}
.more {
  text-align: center;
  margin: 20rpx 0 30rpx 0;
}

.sub-title {
  color: #d01f25;
  font-size: 18px;
  font-weight: bold;
}

.nor-title {
  color: #d01f25;
  font-size: 16px;
  font-weight: bold;
  margin-right: 8rpx;
}
.buttonList {
  display: flex;
  justify-content: space-evenly;
  margin-top: 100rpx;
  > button {
    width: 40%;
    color: #fff;
  }
}
.btn-learn {
  background: #e7c283;
}
.btn-exam {
  background: #8dd2f7;
}
</style>

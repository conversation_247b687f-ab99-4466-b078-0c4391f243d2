<!--
 * @Description: 宣教动画列表页面
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-04-15 10:20:33
 * @LastEditTime: 2024-12-27 17:05:33
-->

<template>
  <view
    class="course-container"
    :style="`background-image: url('${assetsPrefixUrl}fzjz/fzjz-bg.jpg');`"
  >
    <view class="iconfont icon-fanhui icon" @click="back"></view>
    <view class="page-title">【宣教动画】</view>
    <view class="course-search">
      <uni-search-bar
        v-model="searchValue"
        class="searchBar"
        placeholder="请输入名称"
        radius="8"
        clearButton="none"
        cancelButton="none"
        @confirm="getCourseData(false)"
      />
      <view class="searchButton iconfont icon-Search icon" @click="getCourseData(false)"></view>
    </view>

    <!-- <view class="course-dropdown">
      <zb-dropdown-menu ref="dropDownMenuRef" style="width: 100%">
        <zb-dropdown-item
          ref="dropDownItemRef"
          name="one"
          :options="catalogueTableData"
          v-model="catalogueId"
          @change="getCourseData"
        ></zb-dropdown-item>
      </zb-dropdown-menu>
    </view> -->

    <view v-show="courseList.length" class="course-list">
      <view
        class="course-list-content"
        v-for="item in courseList"
        :key="item.courseId"
        @click="jumpTo(item)"
      >
        <view class="img-con">
          <image class="course-list-content-img" :src="item.courseImage"></image>
        </view>
        <view class="course-list-content-name">{{ item.courseName }}</view>
      </view>
    </view>
    <xw-empty :isShow="!courseList.length" text="暂无课程" textColor="#777777"></xw-empty>
  </view>
</template>

<script>
  import { catalogueType } from "@/utils/constant.js"
  import { unepList } from "@/api/competition/index"
  import { getCatalogueList } from "@/api/system/catalogue.js"
  import { mapGetters } from "vuex"
  import { assetsPrefixUrl } from "@/utils/constant.js"

  export default {
    onLoad(option) {
      this.catalogueId = option.catalogueId ? Number(option.catalogueId) : undefined
      this.getCourseData("onLoad")
      this.fetchCatalogueData()
    },
    onPullDownRefresh() {
      this.getCourseData(false)
      uni.stopPullDownRefresh()
    },
    onReachBottom() {
      if (this.courseList.length !== this.total) {
        this.pageNum += 1
        this.getCourseData("onReachBottom")
      }
    },
    onPageScroll(e) {
      if (this.$refs.dropDownItemRef.showWrap) {
        this.$refs.dropDownMenuRef.close()
      }
    },

    data() {
      return {
        assetsPrefixUrl,
        searchValue: "",
        pageNum: 1,
        pageSize: 12,
        total: 0,
        courseList: [],
        catalogueId: undefined,
        catalogueTableData: []
      }
    },
    computed: {
      ...mapGetters({
        domainName: "domainName"
      })
    },

    methods: {
      async getCourseData(flag) {
        if (flag !== "onReachBottom") {
          this.pageNum = 1
        }
        let queryData = {
          pageSize: this.pageSize,
          pageNum: this.pageNum,
          courseName: this.searchValue,
          catalogueId: this.catalogueId
        }
        const res = await unepList(queryData)
        this.total = res.total
        this.courseList = flag === "onReachBottom" ? this.courseList.concat(res.rows) : res.rows
      },

      // 跳转详情·
      jumpTo(item) {
        uni.navigateTo({
          url: "/pages/course/detail/index?id=" + item.courseId
        })
      },
      async fetchCatalogueData() {
        getCatalogueList({ catalogueType: catalogueType.COURSE_CATALOGUE }).then(response => {
          this.catalogueTableData = response.rows.map(({ catalogueId, catalogueName }) => ({
            text: catalogueName,
            value: catalogueId
          }))
          if (this.catalogueTableData[0].text === "全部目录") return
          this.catalogueTableData.unshift({
            text: "全部目录",
            value: undefined
          })
        })
      },
      back() {
        uni.navigateTo({
          url: "/pages/fzjz/index"
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
  ::v-deep .uni-searchbar__box-icon-search {
    display: none;
  }
  ::v-deep .uni-searchbar__box {
    justify-content: unset;
    padding: 0 20rpx;
    border: 1px solid #ccc;
  }
  ::v-deep .uni-searchbar__text-placeholder {
    font-size: 30rpx;
  }
  ::v-deep .zb-dropdown-menu__bar {
    background-color: #f0f1f5;
    box-shadow: none;
  }

  ::v-deep .zb-cell {
    background-color: #f0f1f5;
  }
  .course-container {
    position: relative;
    background-repeat: no-repeat;
    background-size: 100% auto;
    background-position: top;
    height: 100vh;
    padding-top: 46%;
    background-color: #f0f1f5;
    .icon-fanhui {
      position: absolute;
      left: 25rpx;
      top: 260rpx;
      font-size: 40rpx;
      font-weight: bold;
      color: #4a4c47;
    }
    .page-title {
      color: #2d9188;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 32rpx;
      font-weight: bold;
    }
    .course-search {
      padding: 0 60rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      .searchBar {
        width: 100%;
      }
      .searchButton {
        position: absolute;
        right: 90rpx;
        color: #b3b3b3;
        font-size: 45rpx;
      }
    }
    .course-list {
      background-color: #f0f1f5;
      padding: 28rpx 50rpx;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      .course-list-content {
        width: 48%;
        margin-bottom: 30rpx;
        border-radius: 10rpx;
        overflow: hidden;

        .img-con {
          background: url("/static/images/fzjz/courseBorder.png") no-repeat;
          background-size: 100% 100%;
          background-position: center;
          position: relative;
          width: 100%;
          height: 100px;
          .course-list-content-img {
            position: relative;
            padding: 13rpx 10rpx 5rpx 10rpx;
            width: 100%;
            height: 100%;
            z-index: 1;
          }
        }

        .course-list-content-name {
          text-align: center;
          margin: 0 auto;
          color: #595656;
          margin: 16rpx 0;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          font-size: 32rpx;
          font-weight: bold;
        }
      }
    }
  }
</style>

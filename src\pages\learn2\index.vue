<!--
 * @Description: 
 * @Author: <PERSON><PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-05-23 14:35:11
 * @LastEditTime: 2024-12-27 17:03:15
-->
<template>
  <view>
    <GlobalHeader />
    <view class="course-dropdown">
      <zb-dropdown-menu style="width: 100%">
        <zb-dropdown-item
          name="one"
          :options="learnTypeData"
          v-model="courseType"
          @change="getCourseData"
        ></zb-dropdown-item>
        <zb-dropdown-item
          name="two"
          :options="completeStatusData"
          v-model="learnStatus"
          @change="getCourseData"
        ></zb-dropdown-item>
      </zb-dropdown-menu>
    </view>
    <view v-show="courseList.length" class="course-list">
      <view
        class="course-list-content"
        v-for="item in courseList"
        :key="item.courseLearnedId"
        @click="jumpTo(item)"
      >
        <image class="course-list-content-img" :src="item.courseImage"></image>
        <view class="course-list-content-name">
          <view class="top">
            <view class="type-tag" style="background-color: #f53223" v-if="item.courseType === '0'">
              必修
            </view>
            <view
              class="type-tag"
              style="background-color: #45b757"
              v-else-if="item.courseType === '1'"
            >
              选修
            </view>
            <view class="course-name">{{ item.courseName }}</view>
          </view>
          <view class="middle">
            <template v-if="item.taskId">
              <div>任务名称：{{ item.taskName }}</div>
              <div>开始时间：{{ item.startTime }}</div>
              <div>结束时间：{{ item.endTime }}</div>
            </template>
            <template v-else>
              <div>上架时间：{{ item.startTime }}</div>
              <div>下架时间：{{ item.endTime }}</div>
            </template>
          </view>
          <view class="bottom">状态： {{ item.learnStatus === "2" ? "已完成" : "未完成" }}</view>
        </view>
      </view>
    </view>
    <xw-empty :isShow="!courseList.length" text="暂无课程" textColor="#777777"></xw-empty>
  </view>
</template>

<script>
  import { myCourseList } from "@/api/course"
  import xwEmpty from "@/components/xw-empty/xw-empty"
  import GlobalHeader from "@/components/GlobalHeader/index.vue"

  export default {
    components: {
      xwEmpty,
      GlobalHeader
    },

    onShow() {
      if (this.$route.query.showTabBar === "false") {
        uni.hideTabBar()
      }
      const keyword = this.$route.query.keyword
      if (keyword) {
        this.searchValue = decodeURIComponent(keyword)
      }
      this.getCourseData("onLoad")
    },

    onReachBottom() {
      if (this.courseList.length !== this.dataTotal) {
        this.pageNum += 1
        this.getCourseData("onReachBottom")
      }
    },

    data() {
      return {
        searchValue: "",
        pageNum: 1,
        pageSize: 12,
        dataTotal: 0,
        courseType: undefined,
        learnStatus: undefined,
        courseList: [],
        learnTypeData: [
          {
            text: "全部类型",
            value: undefined
          },
          {
            text: "必修",
            value: "0"
          },
          {
            text: "选修",
            value: "1"
          }
        ],
        completeStatusData: [
          {
            text: "全部状态",
            value: undefined
          },
          {
            text: "已完成",
            value: 2
          },
          {
            text: "未完成",
            value: 3
          }
        ]
      }
    },

    methods: {
      async getCourseData(flag) {
        if (flag !== "onReachBottom") {
          this.pageNum = 1
        }
        let queryData = {
          pageSize: this.pageSize,
          pageNum: this.pageNum,
          courseType: this.courseType,
          learnStatus: this.learnStatus,
          courseName: this.searchValue
        }
        const { rows, total } = await myCourseList(queryData)
        this.dataTotal = total
        this.courseList = flag === "onReachBottom" ? this.courseList.concat(rows) : rows
      },

      jumpTo(item) {
        uni.navigateTo({
          url: "/pages/course/detail/index?item=" + encodeURIComponent(JSON.stringify(item))
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
  .course-list {
    background-color: #fff;
    padding: 10px;
    display: flex;
    flex-direction: column;
    .course-list-content {
      padding: 20rpx;
      display: flex;
      border-bottom: 1px solid #e7e7e7;
      .course-list-content-img {
        height: 90px;
        width: 60%;
        margin-bottom: 10rpx;
      }
      .course-list-content-name {
        margin-left: 10px;
        width: 100%;
        overflow: hidden;
        white-space: nowrap;

        .top {
          font-size: 16px;
          color: #000;
          display: flex;
          margin-bottom: 5px;

          .type-tag {
            height: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 3px;
            font-size: 11px;
            width: 35px;
            color: #fff;
            margin-right: 5px;
          }
          .course-name {
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }

        .middle {
          color: #727070;
        }

        .bottom {
          color: #727070;
        }
      }
    }
  }
</style>

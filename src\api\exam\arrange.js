/*
 * @Description: 考试安排相关API
 * @Author: <PERSON><PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-05-25 14:46:12
 * @LastEditTime: 2024-08-15 09:58:55
 */
import request from "@/utils/request"

// 查询待考试列表
export function getNeedExamList(query) {
  return request({
    url: "/exam/arrange/userNeedExamList",
    method: "get",
    params: query
  })
}

// 限制培训任务类考试是否已完成前置课程
export function checkTaskCompleted(params) {
  return request({
    url: "/exam/arrange/checkCompleted",
    method: "get",
    params
  })
}

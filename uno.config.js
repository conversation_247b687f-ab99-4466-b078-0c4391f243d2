// uno.config.js
import { defineConfig, presetAttributify, presetUno, transformerVariantGroup } from "unocss"
import presetIcons from "@unocss/preset-icons"

export default defineConfig({
  presets: [
    presetAttributify(),
    presetUno(),
    presetIcons({
      scale: 1.2,
      extraProperties: {
        'display': 'inline-block',
        'vertical-align': 'middle',
      },
      collections: {
        'material-symbols': () => import('@iconify-json/material-symbols/icons.json').then(i => i.default)
      }
    })
  ],
  transformers: [transformerVariantGroup()],
  rules: [
    [/^grid-cols-(\d+)$/, ([, d]) => ({
      'display': 'grid',
      'grid-template-columns': `repeat(${d}, minmax(0, 1fr))`
    })],
  ],
  shortcuts: {
    'grid': 'display-grid',
  },
  safelist: [
    'i-material-symbols:menu-book',
    'i-material-symbols:quiz',
    'i-material-symbols-folder',
    'i-material-symbols-assignment',
    'i-material-symbols:school-outline'
  ]
})

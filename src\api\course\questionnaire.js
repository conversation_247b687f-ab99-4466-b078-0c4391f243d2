/*
 * @Description:
 * @Author: <PERSON><PERSON>
 * @LastEditors: <PERSON><PERSON>
 * @Date: 2023-06-05 14:44:35
 * @LastEditTime: 2023-06-05 15:41:11
 */
import request from "@/utils/request"

// 我的问卷列表
export function myList(query) {
  return request({
    url: "/course/questionnaire/myList",
    method: "get",
    params: query
  })
}

//通过ID获取问卷信息
export function getQuestionById(questionnaireId) {
  return request({
    url: "/course/questionnaire/" + questionnaireId,
    method: "get"
  })
}

// 提交试卷
export function submit(data) {
  return request({
    url: "/course/questionnaire/submit",
    method: "post",
    data: data
  })
}

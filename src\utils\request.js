/*
 * @Description: request封装
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-04-10 10:29:54
 * @LastEditTime: 2025-03-06 09:37:10
 */
import store from "@/store"
import config from "@/config"
import { getToken } from "@/utils/auth"
import errorCode from "@/utils/errorCode"
import { toast, showConfirm, tansParams } from "@/utils/common"
import { unepTenantList } from "@/utils/constant"

// 添加标志位，用于控制是否正在显示登录过期弹窗
let isShowingLoginExpired = false

const dontNeedLoadingApi = [
  "/course/progress/saveOrUpdate",
  "/exam/question-answer",
  "/course/answer"
]
let timeout = 10000
const baseUrl = config.baseUrl

const request = config => {
  // 是否需要设置 token
  const isToken = (config.headers || {}).isToken === false
  config.header = config.header || {}
  if (process.env.NODE_ENV === "development") {
    config.header["Tenant"] = store.state.tenant.domainName
  }
  if (getToken() && !isToken) {
    config.header["Authorization"] = "Bearer " + getToken()
  }
  // get请求映射params参数
  if (config.params) {
    let url = config.url + "?" + tansParams(config.params)
    url = url.slice(0, -1)
    config.url = url
  }
  return new Promise((resolve, reject) => {
    uni
      .request({
        method: config.method || "get",
        timeout: config.timeout || timeout,
        url: config.baseUrl || baseUrl + config.url,
        data: config.data,
        header: config.header,
        dataType: "json"
      })
      .then(response => {
        let [error, res] = response
        if (error) {
          toast("后端接口连接异常")
          reject("后端接口连接异常")
          return
        }
        const code = res.data.code || 200
        const msg = errorCode[code] || res.data.msg || errorCode["default"]
        if (code === 401) {
          // 如果已经在显示登录过期弹窗，则直接返回
          if (isShowingLoginExpired) {
            reject("无效的会话，或者会话已过期，请重新登录。")
            return
          }
          
          // 设置标志位为true，表示正在显示弹窗
          isShowingLoginExpired = true
          
          showConfirm("登录状态已过期，您可以继续留在该页面，或者重新登录?").then(res => {
            if (res.confirm) {
              if (unepTenantList.includes(store.state.tenant.domainName)) {
                uni.reLaunch({
                  url: "/pages/login"
                })
              }
              // 北蔡
              else if (store.state.tenant.domainName === "town") {
                const userType = localStorage.getItem("userType")
                const deptId = localStorage.getItem("deptId")
                const deptName = localStorage.getItem("deptName")
                if (userType === "02" || userType === "03") {
                  uni.reLaunch({
                    url: `/pages/town/placeCommitteeLogin?userType=${userType}&deptId=${deptId}&deptName=${deptName}`
                  })
                } else {
                  uni.reLaunch({
                    url: "/pages/login"
                  })
                }
              } else {
                uni.reLaunch({
                  url: "/pages/login"
                })
              }
              store.dispatch("tokenOut")
            }
            // 无论用户点击确定还是取消，都重置标志位
            isShowingLoginExpired = false
          })
          reject("无效的会话，或者会话已过期，请重新登录。")
        } else if (code === 500) {
          toast(msg)
          reject("500")
        } else if (code !== 200) {
          toast(msg)
          reject(code)
        }
        resolve(res.data)
      })
      .catch(error => {
        let { message } = error
        if (message === "Network Error") {
          message = "后端接口连接异常"
        } else if (message.includes("timeout")) {
          message = "系统接口请求超时"
        } else if (message.includes("Request failed with status code")) {
          message = "系统接口" + message.substr(message.length - 3) + "异常"
        }
        toast(message)
        reject(error)
      })
  })
}

uni.addInterceptor("request", {
  invoke(args) {
    // 如果接口不需要loading，则直接return
    for (let i = 0; i < dontNeedLoadingApi.length; i++) {
      if (args.url.indexOf(dontNeedLoadingApi[i]) !== -1) {
        return
      }
    }
    uni.showLoading({
      title: "加载中...",
      mask: true
    })
  },
  success(args) {
    uni.hideLoading()
  },
  fail(err) {
    uni.hideLoading()
  },
  complete(res) {
    uni.hideLoading()
  }
})

export default request

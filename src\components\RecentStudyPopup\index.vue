<!--
 * @Description: 最近学习浮窗
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-12-20 15:18:13
 * @LastEditTime: 2025-01-07 15:03:02
-->
<template>
  <transition name="slide-fade">
    <view
      v-if="shouldShow && recentCourse"
      class="fixed left-3 right-3 bottom-[calc(var(--window-bottom)+10px)] z-50 flex items-center justify-between bg-white px-4 py-3 shadow-lg rounded-lg"
    >
      <!-- 左侧课程信息 -->
      <view class="flex items-center flex-1 min-w-0">
        <view class="i-material-symbols:school-outline text-blue-500 text-2xl flex-shrink-0"></view>
        <view class="ml-3 text-gray-700 truncate flex-1">{{ recentCourse.fieldName }}</view>
      </view>

      <!-- 右侧按钮区域 -->
      <view class="flex items-center ml-2">
        <view
          class="mr-4 px-4 py-1.5 bg-blue-500 text-white rounded-lg text-sm cursor-pointer hover:bg-blue-600 transition-colors"
          @click="continueLearning"
        >
          继续学习
        </view>
        <view
          class="i-material-symbols:close text-gray-400 text-xl cursor-pointer hover:text-gray-600 transition-colors"
          @click="handleClose"
        ></view>
      </view>
    </view>
  </transition>
</template>

<script>
  import { getRecentStudy } from "@/api/trainingTask"
  import { mapState, mapMutations } from "vuex"

  // #ifdef H5
  const initH5Scroll = handler => window.addEventListener("scroll", handler)
  const removeH5Scroll = handler => window.removeEventListener("scroll", handler)
  // #endif

  // #ifndef H5
  const initMiniScroll = handler => uni.onPageScroll(handler)
  const removeMiniScroll = handler => uni.offPageScroll(handler)
  // #endif

  export default {
    name: "RecentStudyPopup",
    data() {
      return {
        visible: true,
        recentCourse: null
      }
    },
    computed: {
      ...mapState({
        globalShowPopup: state => state.user.showRecentStudyPopup,
        recentStudyList: state => state.user.recentStudyList
      }),
      shouldShow() {
        return this.visible && this.globalShowPopup
      }
    },
    created() {
      // 只有当 Vuex 中没有数据时才调用接口
      if (!this.recentStudyList.length) {
        this.$store.dispatch('getRecentStudyData').then(() => {
          this.updateRecentCourse()
        })
      } else {
        this.updateRecentCourse()
      }
    },
    mounted() {
      // 监听页面滚动
      // #ifdef H5
      initH5Scroll(this.handleScroll)
      // #endif

      // #ifndef H5
      initMiniScroll(this.handleScroll)
      // #endif
    },
    beforeDestroy() {
      // 移除滚动监听
      // #ifdef H5
      removeH5Scroll(this.handleScroll)
      // #endif

      // #ifndef H5
      removeMiniScroll(this.handleScroll)
      // #endif
    },
    methods: {
      ...mapMutations(['SET_SHOW_RECENT_STUDY_POPUP']),
      handleScroll(e) {
        const scrollTop = e?.scrollTop || window?.scrollY || 0
        if (scrollTop > 100) {
          this.visible = false
        } else if (scrollTop <= 100 && this.recentCourse) {
          this.visible = true
        }
      },
      updateRecentCourse() {
        // 从 Vuex 中获取最近学习的课程
        const courseData = this.recentStudyList.find(item => item.learnType === '0')
        if (courseData) {
          this.recentCourse = courseData
          this.visible = true
        }
      },
      handleClose() {
        this.visible = false
        this.SET_SHOW_RECENT_STUDY_POPUP(false)
      },
      continueLearning() {
        if (this.recentCourse) {
          uni.navigateTo({
            url: `/pages/course/detail/index?id=${this.recentCourse.fieldId}`
          })
        }
      }
    }
  }
</script>

<style lang="scss" scoped>
  .slide-fade-enter-active,
  .slide-fade-leave-active {
    transition: all 0.3s ease;
  }

  .slide-fade-enter,
  .slide-fade-leave-to {
    transform: translateY(100%);
    opacity: 0;
  }
</style>

<!--
 * @Description: 课程播放页面
 * @Author: <PERSON><PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-05-09 15:20:37
 * @LastEditTime: 2023-10-09 08:35:25
-->
<template>
  <view class="detail">
    <video
      ref="videoRef"
      id="myVideo"
      class="detail-video"
      :poster="item.image"
      v-if="currentPlayItem"
      :src="currentPlayItem.vodUrl"
      :autoplay="true"
      @play="startPlay"
      @pause="pauseTimer"
      @ended="endVideo"
      @timeupdate="bindtimeupdate"
      :enable-progress-gesture="courseInfo.progressBarDrag === '1'"
    ></video>
    <scroll-view
      style="height: calc(100vh - 250px); padding: 30rpx; background: #fff; position: relative"
    >
      <view class="part" v-for="(item, index) in passageList" :key="item.coursePassageId">
        <view class="part-title">{{ index + 1 }}. {{ item.coursePassageName }} </view>

        <view v-if="item.videoFileList && item.videoFileList.length > 0">
          <view
            class="part-info"
            v-for="(mitem, mindex) in item.videoFileList"
            :key="mitem.passageId"
          >
            <view
              :class="['part-name', currentPlayItem.passageId === mitem.passageId ? 'active' : '']"
              @click="setIndex(mitem, index, mindex)"
            >
              {{ mitem.fileName || getFileName(mitem.fileUrl) }}</view
            >
            <view class="part-per">
              <progress
                class="part-progress"
                :percent="mitem.progress || 0"
                color="#75b519"
              ></progress>
            </view>
            <view class="part-text"> {{ mitem.progress || 0 }}% </view>
          </view>
        </view>
      </view>
      <view class="part-logo" v-if="learnedStatus">
        <image class="part-img" src="/static/images/competition/yiwancheng.png"></image>
      </view>
    </scroll-view>
  </view>
</template>

<script>
  import { listPassage } from "@/api/course/passage.js"
  import { getFileName } from "@/utils/common"
  import {
    courseEditView,
    getCourseInfo,
    updateCourseProgress,
    getCourseStats
  } from "@/api/course"
  import { editFavoriteOrNot } from "@/api/course/user-link.js"
  import { listDiscuss, addCourseDiscuss, deleteCourseDiscuss } from "@/api/course/discuss.js"
  import { editCourseGrade } from "@/api/course/grade.js"

  export default {
    data() {
      return {
        item: {},
        passageList: [],
        discussList: [],
        courseInfo: {},
        valueIndex: 0,
        pageNum: 1,
        pageSize: 10,
        dataTotal: 0,
        status: "more",
        loaded: false,
        currentPlayItem: {},
        passageIndex: 0,
        videoIndex: 0,
        discussContent: "",
        addFlag: false,
        removeFlag: false,
        lastPlayTime: null,
        timer: null,
        lastProgress: 0,
        frequencyTime: 10000,
        videoContext: "",
        rateValue: 0,
        learnedStatus: false,
        statusList: []
      }
    },
    created() {
      // 统计点击次数
      courseEditView({ courseId: this.item.courseId })
      this.fetchDiscussData()
    },
    async onLoad(option) {
      this.item = JSON.parse(decodeURIComponent(option.item))
      await this.fetchCourseData()
      await this.fetchPassageData()
      await this.getCourseProgress()
    },

    onReady: function (res) {
      this.videoContext = uni.createVideoContext("myVideo")
    },

    deactivated() {
      clearInterval(this.timer)
    },

    computed: {
      list() {
        let arr = this.courseInfo.completionConditions?.split(",")
        let newArr = []
        arr?.map(item => {
          if (item === "KC") {
            newArr?.push("课程完成")
          } else if (item === "PJ") {
            newArr?.push("评价")
          } else if (item === "ZY") {
            newArr?.push("作业提交")
          } else {
            newArr?.push("测试通过")
          }
        })
        return newArr
      }
    },

    methods: {
      getFileName,
      bindtimeupdate(e) {
        let currentTime = e.detail.currentTime //当前进度
        // 禁止拖动进度条快进
        if (currentTime - this.lastPlayTime > 2) {
          this.$refs.videoRef.seek(this.lastPlayTime)
        }
        this.lastPlayTime = currentTime
      },
      schedule(id) {
        if (!id) return 0

        const statItem = this.statusList?.find(item => item.passageId === id)

        if (!statItem) return
        return statItem.learnedDuration >= statItem.mediaDuration || statItem.learnedStatus === "2"
          ? 100
          : Math.floor((statItem.learnedDuration / statItem.mediaDuration) * 100)
      },
      // 获取章节
      async fetchPassageData() {
        let queryData = {
          courseId: this.item.courseId,
          pageSize: 999
        }
        const { rows } = await listPassage(queryData)
        this.passageList = rows

        this.currentPlayItem = this.passageList[0].videoFileList[0]
      },
      // 切换章节
      async setIndex(item, index, index2) {
        this.currentPlayItem = item
        this.passageIndex = index
        this.videoIndex = index2
        clearInterval(this.timer)
        await this.getCourseProgress()
      },
      // 获取课程信息
      async fetchCourseData() {
        const { data } = await getCourseInfo(this.item.courseId)
        this.courseInfo = data
        this.rateValue = this.courseInfo.courseGrade
      },
      // 收藏课程
      async collectCourse() {
        if (this.courseInfo.favoriteOrNot == true) {
          let queryData = {
            courseId: this.item.courseId,
            userId: this.$store.state.user.userId,
            favoriteOrNot: false
          }
          await editFavoriteOrNot(queryData)
          uni.showToast({
            title: "取消收藏成功！",
            icon: "success"
          })
        } else {
          let queryData = {
            courseId: this.item.courseId,
            userId: this.$store.state.user.userId,
            favoriteOrNot: true
          }
          await editFavoriteOrNot(queryData)
          uni.showToast({
            title: "收藏成功！",
            icon: "success"
          })
        }
        this.fetchCourseData()
      },

      // 视频播放
      startPlay() {
        this.timer = setInterval(() => {
          this.courseProgressUpdate()
        }, this.frequencyTime)
      },

      // 视频暂停事件
      pauseTimer() {
        clearInterval(this.timer)
      },
      // 更新各个章节进度
      updatePassageProgress() {
        this.passageList[this.passageIndex].videoFileList.forEach(item => {
          const rightItem = this.statusList.find(ite => ite.passageId === item.passageId)
          if (rightItem) {
            let progress =
              rightItem.learnedDuration >= rightItem.mediaDuration ||
              rightItem.learnedStatus === "2"
                ? 100
                : Math.floor((rightItem.learnedDuration / rightItem.mediaDuration) * 100)
            this.$set(item, "progress", progress)
          }
        })
      },
      // 获取视频播放进度
      async getCourseProgress() {
        const res = await getCourseStats(this.item.courseId)
        if (res.code === 200) {
          if (!res.data || res.data.length === 0) return
          this.statusList = res.data
          const statusItem = res.data.find(
            item => item.passageId === this.currentPlayItem?.passageId
          )
          this.currentPlayItem = Object.assign(this.currentPlayItem, statusItem)
          // 更新各个章节进度
          this.updatePassageProgress()
          // 更新视频总状态
          this.learnedStatus = res.data.every(item => {
            return item.learnedStatus === "2"
          })
          res.data.forEach(item => {
            if (item.passageId === this.currentPlayItem?.passageId) {
              this.lastPlayTime = item.mediaProgress
              // 只要原先播放的进度和视频总时长相差小于2秒，则清空原有进度(从头播放)
              if (Math.abs(item.mediaDuration - item.mediaProgress) < 2) {
                return
              }
              // 如果有视频进度，直接跳转到进度位置
              this.videoContext?.seek(this.lastPlayTime)
            }
          })
        }
      },

      // 更新视频播放进度
      async courseProgressUpdate() {
        if (this.$refs.videoRef?.currentTime !== this.lastProgress) {
          this.lastProgress = this.$refs.videoRef?.currentTime
          // 调用后端接口记录进度
          let requestData = {
            courseId: this.item.courseId,
            passageId: this.currentPlayItem?.passageId,
            learnFileType: "0",
            deltaDuration: this.frequencyTime / 1000, // 增量时间，非视频为0，视频就传新增的看视频时长
            mediaProgress: this.$refs.videoRef?.currentTime, // 视频播放节点，非视频为0，视频就传已看到的视频节点
            mediaDuration: this.$refs.videoRef?.durationTime // 视频总时长，非视频为0
          }
          await updateCourseProgress(requestData)
        }
      },

      // 视频结束播放
      async endVideo() {
        // 1.更新视频播放进度
        await this.courseProgressUpdate()

        // 2.如果这个章节下还有下一个视频，则播放下一个视频
        if (this.videoIndex < this.passageList[this.passageIndex].videoFileList.length - 1) {
          this.videoIndex++
          this.currentPlayItem = this.passageList[this.passageIndex].videoFileList[this.videoIndex]
          await this.getCourseProgress()
        }
        // 3.如果该章节下没有下一个视频
        else {
          // 3.1 如果这个章节下还有下一个章节，则继续播放下一个章节下的第一个视频
          if (this.passageIndex < this.passageList.length - 1) {
            this.passageIndex++
            this.videoIndex = 0
            this.currentPlayItem =
              this.passageList[this.passageIndex].videoFileList[this.videoIndex]
            await this.getCourseProgress()
          }
          // 3.没有下个章节，则只更新各个章节的学习状态
          else {
            await this.getCourseProgress()
          }
        }
      },

      // =========================== 评论相关 =================================
      // 获取评论
      async fetchDiscussData() {
        let queryData = {
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          courseId: this.item.courseId
        }
        const discussListData = await listDiscuss(queryData)
        this.discussList = this.discussList.concat(discussListData.rows)
        this.dataTotal = discussListData.total
        if (this.pageNum * this.pageSize < this.dataTotal) {
          this.status = "more"
        } else {
          this.status = "noMore"
        }
      },
      // 发布评论
      async releaseContent() {
        this.addFlag = true
        let addCourseDiscussData = await addCourseDiscuss({
          courseId: this.item.courseId,
          discussContent: this.discussContent,
          discussById: this.$store.state.user.userId,
          discussPortrait: this.$store.state.user.avatarUrl,
          discussBy: this.$store.state.user.nickName
        })
        if (addCourseDiscussData.code === 200) {
          this.pageNum = 1
          const discussListData = await listDiscuss({
            pageNum: this.pageNum,
            pageSize: this.pageSize,
            courseId: this.item.courseId
          })
          this.discussList = discussListData.rows
          this.dataTotal = discussListData.total
          this.discussContent = ""
        }
        this.addFlag = false
      },
      // 删除评论(评论只能一条条删除)
      async removeDiscuss(id) {
        if (this.removeFlag || !id) return
        this.removeFlag = true
        await deleteCourseDiscuss({ id })
        const discussListData = await listDiscuss({
          courseId: this.item.courseId
        })
        this.discussList = discussListData.rows
        this.dataTotal = discussListData.total
        this.removeFlag = false
      },
      // 下拉加载更多评论
      async loadmore() {
        this.status = "loading"
        if (this.dataTotal < this.pageNum * this.pageSize) {
          this.status = "noMore"
        } else {
          this.pageNum += 1
          await this.fetchDiscussData()
        }
      },
      // 评分
      async changeRate(e) {
        if (this.learnedStatus && !this.courseInfo.selfCourseGrade) {
          let queryData = {
            courseId: this.item.courseId,
            grade: this.rateValue
          }
          await editCourseGrade(queryData)
          uni.showToast({
            title: "感谢您的评分！",
            icon: "success"
          })
        } else {
          uni.showToast({
            title: "请在课程学习完成后评价",
            icon: "error"
          })
        }
        this.fetchCourseData()
      }
    }
  }
</script>

<style scoped lang="scss">
  ::v-deep .uni-progress-inner-bar {
    border-radius: 20rpx;
  }
  .part-logo {
    position: absolute;
    top: 0;
    right: 0;
    .part-img {
      width: 120rpx;
      height: 120rpx;
    }
  }
  .part {
    border-bottom: 1px solid #d7d7d7;
    padding: 20rpx 0;
    position: relative;
    min-height: 120rpx;

    .part-title {
      font-weight: bold;
      line-height: 26px;
      font-size: 16px;
    }
    .part-info {
      padding: 0 30rpx;
      line-height: 24px;
      display: flex;
      font-size: 15px;
      .part-name {
        flex-basis: 300rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .part-text {
        flex-basis: 60rpx;
        text-align: center;
        color: #4392f5;
        font-size: 14px;
      }
      .part-per {
        flex-grow: 1;
        padding: 0 20rpx;
        margin-top: 20rpx;
      }
    }
  }
  ::v-deep .uni-list-chat__content-note {
    color: #4c4f50;
    font-size: 24rpx;
  }
  ::v-deep .uni-list-chat__content-title {
    font-size: 30rpx;
  }
  .chat-custom-text {
    font-size: 26rpx;
    color: #808085;
  }
  .detail-video {
    width: 100%;
    height: 250px;
  }

  .detail-tabs-list-catalogue {
    background-color: white;
    padding: 20rpx;

    .passage-title {
      display: flex;
      justify-content: space-between;
      font-size: 38rpx;
      font-weight: bold;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      width: 100%;
    }

    .passage-file-list {
      border-bottom: 1px dashed #dedede;
      padding: 20rpx;
      .passage-file-item {
        display: flex;
        font-size: 30rpx;
        padding: 10rpx 0;
        margin-left: 20rpx;
      }
    }
  }

  .detail-tabs-list-info {
    background-color: #fff;
    margin-bottom: 5px;
    .info {
      padding: 20px 20px 0 20px;
      display: flex;
      justify-content: space-between;
    }

    .rate {
      padding: 0 20px;
      display: flex;
      align-items: center;

      .rate-value {
        margin-left: 10px;
      }
    }

    .condition {
      padding: 0 20px;
      justify-content: space-between;
      display: flex;
      > view {
        display: flex;
        .main-item-content {
          margin-left: 10px;
          margin-bottom: 15px;
        }
      }

      .collect {
        display: flex;
        > view {
          margin-left: 5px;
        }
      }
    }
  }

  .detail-tabs-list-details {
    background-color: white;
    .introduction {
      padding: 20px 20px 0 20px;
      .content {
        margin-top: 15px;
        line-height: 20px;
      }
    }

    .curriculum {
      padding: 0 20px;
      .content {
        margin-top: 15px;
        line-height: 20px;
      }
    }

    .objectives {
      padding: 0 20px 20px 20px;
      .content {
        margin-top: 15px;
        line-height: 20px;
      }
    }
  }
  .uni-input-wrapper {
    border-top: 1px solid rgba(0, 0, 0, 0.1);
  }
  .detail-tabs-list-discuss {
    background-color: white;
    display: flex;
    border-bottom: 1px solid #dedede;
    .detail-tabs-list-discuss-img {
      margin-top: 20px;
      margin-left: 20px;

      > img {
        width: 50px;
        height: 50px;
        border-radius: 50%;
      }
    }

    .detail-tabs-list-discuss-info {
      margin: 20px 20px 0px 20px;
      > view {
        margin-bottom: 30rpx;
        overflow: hidden;
        word-wrap: break-word;
        word-break: break-all;
        white-space: pre-wrap;
      }
    }
  }

  .chat {
    border-bottom: 1px solid #e7e7e7;
  }
  .active {
    color: #007aff;
  }

  ::v-deep .uni-ellipsis {
    white-space: normal;
    line-height: 20px;
  }

  .iconfont {
    width: 20px;
    height: 20px;
  }
</style>

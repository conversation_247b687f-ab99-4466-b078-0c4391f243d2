{"id": "lingfeng-timepicker", "displayName": "timepicker 时间选择器", "version": "1.0.9", "description": "基于picker-view、uni-popup的时间选择器，支持按年、月、日、时、分、秒、周、季度选择时间或时间范围，支持配置最大、最小选择日期", "keywords": ["timepicker", "时间", "日期", "范围选择", "时间选择器"], "repository": "https://github.com/lingfengjgf/lingfeng-timepicker", "engines": {"HBuilderX": "^3.4.1"}, "dcloudext": {"type": "component-vue", "sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": ""}, "declaration": {"ads": "无", "data": "插件不采集任何数据", "permissions": "无"}, "npmurl": ""}, "uni_modules": {"dependencies": ["uni-scss", "uni-transition", "uni-popup"], "encrypt": [], "platforms": {"cloud": {"tcb": "y", "aliyun": "y"}, "client": {"Vue": {"vue2": "y", "vue3": "n"}, "App": {"app-vue": "y", "app-nvue": "u"}, "H5-mobile": {"Safari": "y", "Android Browser": "y", "微信浏览器(Android)": "y", "QQ浏览器(Android)": "y"}, "H5-pc": {"Chrome": "y", "IE": "u", "Edge": "y", "Firefox": "u", "Safari": "u"}, "小程序": {"微信": "y", "阿里": "u", "百度": "u", "字节跳动": "u", "QQ": "u", "钉钉": "u", "快手": "u", "飞书": "u", "京东": "u"}, "快应用": {"华为": "u", "联盟": "u"}}}}}
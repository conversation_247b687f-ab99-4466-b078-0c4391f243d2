<!--
 * @Description: 
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-12-10 13:36:33
 * @LastEditTime: 2024-12-27 10:39:27
-->
<template>
  <div class="flex items-center justify-between h-16 bg-[#1e91fc] pl-4">
    <div class="flex items-center">
      <image
        class="w-50px"
        :src="
          domainInfo.tenantIcon || 'https://training-voc.obs.cn-north-4.myhuaweicloud.com:443/beckwell.png'
        "
        mode="widthFix"
      ></image>
    </div>

    <div class="flex-1 mx-4">
      <div class="relative flex items-center">
        <div class="absolute left-3 text-white">
          <div class="i-material-symbols-search text-20px"></div>
        </div>
        <input
          type="text"
          v-model="searchKeyword"
          placeholder="搜索课程"
          class="w-full h-10 pl-10 pr-10 rounded-full bg-[#61b2fd] border-0 text-white focus:outline-none search-input"
          confirm-type="search"
          @confirm="handleSearch"
        />
        <div 
          v-if="searchKeyword"
          class="absolute right-3 text-white cursor-pointer"
          @click="clearSearch"
        >
          <div class="i-material-symbols-close text-20px"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex"

export default {
  name: "GlobalHeader",

  props: {
    // 默认搜索关键词
    defaultKeyword: {
      type: String,
      default: ''
    }
  },

  data() {
    return {
      searchKeyword: this.defaultKeyword || ''
    }
  },

  watch: {
    // 监听默认关键词的变化
    defaultKeyword: {
      handler(newVal) {
        if (newVal !== this.searchKeyword) {
          this.searchKeyword = newVal
        }
      },
      immediate: true
    }
  },

  computed: {
    ...mapGetters({
      domainInfo: "domainInfo"
    }),
    // 判断是否在课程页面
    isCoursePage() {
      const pages = getCurrentPages()
      const currentPage = pages[pages.length - 1]
      return currentPage && currentPage.route === 'pages/course/index'
    }
  },

  methods: {
    handleSearch(e) {
      const keyword = this.searchKeyword.trim()
      if (!keyword) return

      if (this.isCoursePage) {
        // 如果在课程页面，直接触发父组件的搜索方法
        this.$emit('on-search', keyword)
      } else {
        // 如果不在课程页面，使用reLaunch跳转，确保每次都会触发onLoad
        uni.reLaunch({
          url: `/pages/course/index?keyword=${encodeURIComponent(keyword)}`
        })
      }
    },

    clearSearch() {
      this.searchKeyword = ''
      if (this.isCoursePage) {
        // 如果在课程页面，触发搜索方法重新获取全部数据
        this.$emit('on-search', '')
      }
    }
  }
}
</script>

<style scoped>
.search-input {
  color: white;
}
.search-input::placeholder {
  color: white !important;
  opacity: 1;
}
/* 兼容 Firefox */
.search-input::-moz-placeholder {
  color: white !important;
  opacity: 1;
}
/* 兼容 IE */
.search-input:-ms-input-placeholder {
  color: white !important;
}
/* 兼容 Chrome/Safari */
.search-input::-webkit-input-placeholder {
  color: white !important;
}
.uni-input-placeholder {
  color: white !important;
}
</style>

<template>
  <view class="min-h-screen w-full bg-[#f5f7fa] relative">
    <!-- 背景图容器 -->
    <view v-if="domainName !== 'eduwujin'" class="absolute inset-0 z-0">
      <image
        src="https://training-voc.obs.cn-north-4.myhuaweicloud.com/common-login-bg.jpg"
        class="w-full h-full object-cover"
        mode="aspectFill"
      />
    </view>

    <!-- 自定义覆盖层 -->
    <view v-else class="absolute z-50 top-[13%] left-0 right-0 flex justify-center text-3xl">
      <view class="px-6 py-1">
        <text class="text-[20px] font-bold text-[#0e5c8f]">五进科普宣传</text>
      </view>
    </view>

    <!-- 登录表单区域 -->
    <view class="relative z-10 w-4/5 mx-auto pt-[25%]">
      <!-- 输入框组 -->
      <view class="space-y-4 pt-35">
        <!-- 用户名输入框 -->
        <view
          class="flex items-center h-[45px] rounded-full bg-white/80 shadow-sm px-4 border border-gray-100"
        >
          <view class="i-material-symbols:person text-[20px] text-gray-400"></view>
          <input
            v-model="loginForm.username"
            type="text"
            placeholder="请输入账号"
            maxlength="30"
            class="flex-1 ml-3 text-[14px] bg-transparent placeholder:text-gray-400"
          />
        </view>

        <!-- 密码输入框 -->
        <view
          class="flex items-center h-[45px] rounded-full bg-white/80 shadow-sm px-4 border border-gray-100"
        >
          <view class="i-material-symbols:lock text-[20px] text-gray-400"></view>
          <input
            v-model="loginForm.password"
            type="password"
            placeholder="请输入密码"
            maxlength="20"
            class="flex-1 ml-3 text-[14px] bg-transparent placeholder:text-gray-400"
            @keyup.enter="handleLogin"
          />
        </view>
      </view>

      <!-- 登录按钮 -->
      <button
        @click="handleLogin"
        class="w-full h-[45px] mt-8 rounded-full bg-blue-500 text-white text-[16px] flex items-center justify-center shadow-lg shadow-blue-500/20 hover:bg-blue-600 transition-colors"
      >
        登录
      </button>

      <!-- 协议区域 -->
      <view class="mt-6 text-center text-[12px]">
        <text class="text-gray-500">登录即代表同意</text>
        <text @click="handleUserAgrement" class="text-blue-500 ml-1">《用户协议》</text>
        <text @click="handlePrivacy" class="text-blue-500">《隐私协议》</text>
      </view>

      <view class="mt-6 text-center text-[12px]">
        客服热线：<text class="text-blue-500" @click="handleCustomerService">{{ tel }}</text>
      </view>
    </view>
  </view>
</template>

<script>
  import { mapGetters } from "vuex"

  export default {
    data() {
      return {
        codeUrl: "",
        captchaEnabled: true,
        globalConfig: getApp().globalData.config,
        loginForm: {
          username: "",
          password: ""
        },
        tel: "13032159810"
      }
    },
    computed: {
      ...mapGetters({
        domainName: "domainName"
      })
    },
    methods: {
      handlePrivacy() {
        let site = this.globalConfig.appInfo.agreements[0]
        this.$tab.navigateTo(`/pages/common/webview/index?title=${site.title}&url=${site.url}`)
      },
      handleUserAgrement() {
        let site = this.globalConfig.appInfo.agreements[1]
        this.$tab.navigateTo(`/pages/common/webview/index?title=${site.title}&url=${site.url}`)
      },
      async handleLogin() {
        if (this.loginForm.username === "") {
          this.$modal.msgError("请输入您的账号")
        } else if (this.loginForm.password === "") {
          this.$modal.msgError("请输入您的密码")
        } else {
          this.$modal.loading("登录中，请耐心等待...")
          this.pwdLogin()
        }
      },
      async pwdLogin() {
        this.$store.dispatch("Login", this.loginForm).then(() => {
          this.$modal.closeLoading()
          this.loginSuccess()
        })
      },
      loginSuccess() {
        this.$store.dispatch("GetInfo").then(res => {
          this.$tab.reLaunch("/pages/index")
        })
      },
      handleCustomerService() {
        uni.makePhoneCall({
          phoneNumber: this.tel
        })
      }
    }
  }
</script>

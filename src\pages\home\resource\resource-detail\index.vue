<!--
 * @Description: 资料详情
 * @Author: <PERSON><PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-06-12 10:17:19
 * @LastEditTime: 2024-12-11 14:25:52
-->
<template>
  <view>
    <navigationBar path="/pages/home/<USER>/index" />
    <filePreview ref="filePreviewRef" class="filePreview" />
  </view>
</template>

<script>
  import { editRelateNumber, getMeans } from "@/api/course/manage.js"
  import filePreview from "@/components/FilePreview/index"
  export default {
    components: { filePreview },
    async onLoad(option) {
      if (option.id) {
        const res = await getMeans(option.id)
        this.resourceDetailInfo = res.data
      }
      this.$nextTick(() => {
        this.$refs.filePreviewRef.fileLoad(this.resourceDetailInfo.manageAddress)
        this.filePreview()
      })
    },

    data() {
      return {
        resourceDetailInfo: {}
      }
    },

    methods: {
      async filePreview() {
        await editRelateNumber({ manageId: this.resourceDetailInfo.manageId, viewNumber: 0 })
      }
    }
  }
</script>

<style lang="scss" scoped></style>

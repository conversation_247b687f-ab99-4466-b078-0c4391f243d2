<!--
 * @Description: 针对九小场所/居委不同登录
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-03-04 11:20:24
 * @LastEditTime: 2024-03-06 10:47:28
-->
<template>
  <view class="normal-login-container">
    <image src="/static/images/competition/logbg_1.png" mode="widthFix" style="width: 100%"></image>
    <view class="logincon">
      <image
        class="tenant-icon"
        :src="
          domainInfo.tenantIcon ||
          'https://training-voc.obs.cn-north-4.myhuaweicloud.com/town_logo.png'
        "
        mode="heightFix"
      ></image>
      <view class="title"> 北蔡镇安全发展和综合减灾知识平台 </view>
      <view class="committee-name">{{ $route.query.deptName }}</view>
      <view class="bgcon">
        <view class="login-form-content">
          <view class="input-item flex align-center">
            <input
              v-model="loginForm.mobile"
              class="input"
              type="text"
              @focus="mobileHolder = ''"
              @blur="mobileHolder = '手机号码'"
              :placeholder="mobileHolder"
              maxlength="11"
              @input="checkCode"
            />
          </view>
          <view class="input-item flex align-center">
            <input
              v-model="loginForm.code"
              class="input"
              @focus="codeHolder = ''"
              @blur="codeHolder = '短信验证码'"
              :placeholder="codeHolder"
              maxlength="20"
            />
            <view @click="getCode" class="get-code-msg" :class="{ active: !canCode }">
              {{ codeMsg }}
            </view>
          </view>
          <template v-if="$route.query.userType === '02'">
            <view class="input-item flex align-center required">
              <input
                v-model="loginForm.userName"
                class="input"
                type="text"
                @focus="placeNameHolder = ''"
                @blur="placeNameHolder = '店铺名称'"
                :placeholder="placeNameHolder"
                maxlength="11"
              />
            </view>
            <view class="input-item flex align-center required">
              <input
                v-model="loginForm.idCard"
                class="input"
                type="text"
                @focus="placeNumberHolder = ''"
                @blur="placeNumberHolder = '门牌号码'"
                :placeholder="placeNumberHolder"
              />
            </view>
            <view class="input-item flex align-center required">
              <uni-data-select
                v-model="loginForm.remark"
                :localdata="remarkOptions"
                @focus="businessTypeHolder = ''"
                @blur="businessTypeHolder = '经营类型'"
                :placeholder="businessTypeHolder"
              ></uni-data-select>
            </view>
          </template>
          <template v-else-if="$route.query.userType === '03'">
            <view class="input-item flex align-center">
              <input
                v-model="loginForm.userName"
                class="input"
                type="text"
                @focus="userNameHolder = ''"
                @blur="userNameHolder = '姓名'"
                :placeholder="userNameHolder"
                maxlength="11"
              />
            </view>
            <view class="input-item flex align-center required">
              <input
                v-model="loginForm.nickName"
                class="input"
                type="text"
                @focus="communityNameHolder = ''"
                @blur="communityNameHolder = '小区名称'"
                :placeholder="communityNameHolder"
              />
            </view>
            <view class="input-item flex align-center">
              <input
                v-model="loginForm.idCard"
                class="input"
                type="text"
                @focus="placeNumberHolder = ''"
                @blur="placeNumberHolder = '门牌号码'"
                :placeholder="placeNumberHolder"
              />
            </view>
          </template>

          <view class="action-btn">
            <button @click="handleLogin" class="login-btn cu-btn block bg-blue lg round">
              立即登录
            </button>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
  import { isContactWay } from "@/utils/validate"
  import { sendCode } from "@/api/competition/index"
  import { getInfoByMobile } from "@/api/beicai/index"
  import { mapGetters } from "vuex"

  export default {
    data() {
      return {
        loginForm: {
          mobile: "",
          code: "",
          userName: "",
          idCard: "",
          remark: "",
          nickName: ""
        },
        canCode: false,
        codeMsg: "获取验证码",
        interval: null,
        mobileHolder: "手机号码",
        codeHolder: "短信验证码",
        placeNameHolder: "店铺名称",
        placeNumberHolder: "门牌号码",
        businessTypeHolder: "经营类型",
        userNameHolder: "姓名",
        communityNameHolder: "小区名称",
        startTime: null,
        endTime: null,
        remarkOptions: [],
        sec: 0
      }
    },
    created() {
      this.getRemarkOptionsList()
      if (this.$route.query.userType) {
        localStorage.setItem("userType", this.$route.query.userType)
      }
      if (this.$route.query.deptId) {
        localStorage.setItem("deptId", this.$route.query.deptId)
      }
      if (this.$route.query.deptName) {
        localStorage.setItem("deptName", this.$route.query.deptName)
      }
    },
    computed: {
      ...mapGetters({
        domainInfo: "domainInfo"
      })
    },
    methods: {
      // 获取经营类型下拉选项
      async getRemarkOptionsList() {
        this.remarkOptions = await this.$getDict("business_type")
      },
      // 判断验证码是否可点击
      checkCode(e) {
        let val = e.detail.value
        if (val && isContactWay(val) && this.sec <= 0) {
          this.canCode = true
        } else {
          this.canCode = false
        }
      },
      // 获取验证码
      async getCode() {
        if (!this.loginForm.mobile) {
          return this.$modal.msgError("请先输入手机号")
        }
        if (!this.canCode) {
          return
        }
        await this.fetchExtraInfo()
        let queryData = {
          mobile: this.loginForm.mobile,
          scene: 1
        }
        await sendCode(queryData)
        this.$modal.msgSuccess("验证码发送成功！")
        clearInterval(this.interval)
        this.canCode = false
        this.sec = 60
        this.codeMsg = this.sec + "s后重试"
        this.interval = setInterval(() => {
          this.sec -= 1
          this.codeMsg = this.sec + "s后重试"
          if (this.sec === 0) {
            this.codeMsg = "重新获取"
            this.canCode = true
            clearInterval(this.interval)
          }
        }, 1000)
      },
      // 非首次登录用户通过手机号获取个人信息并填充
      async fetchExtraInfo() {
        const res = await getInfoByMobile({ mobile: this.loginForm.mobile, tenantCode: "town" })
        if (res.code !== 200 || !res.data) return
        this.loginForm.userName = res.data.sysUser.userName
        this.loginForm.idCard = res.data.sysUser.idCard
        this.loginForm.remark = res.data.sysUser.remark
        this.loginForm.nickName = res.data.sysUser.nickName
      },
      // 登录方法
      async handleLogin() {
        if (this.loginForm.mobile === "") {
          this.$modal.msgError("请输入您的手机号")
          return
        } else if (this.loginForm.code === "") {
          this.$modal.msgError("请输入您的验证码")
          return
        }
        if (this.$route.query.userType === "02") {
          if (this.loginForm.userName === "") {
            this.$modal.msgError("请输入您的店铺名称")
            return
          } else if (this.loginForm.idCard === "") {
            this.$modal.msgError("请输入您的门牌号码")
            return
          } else if (this.loginForm.remark === "") {
            this.$modal.msgError("请输入您的经营类型")
            return
          }
        } else if (this.$route.query.userType === "03") {
          if (this.loginForm.nickName === "") {
            this.$modal.msgError("请输入您的小区名称")
            return
          }
        }

        this.$modal.loading("登录中，请耐心等待...")
        this.personalLogin()
      },
      // 登录成功后，处理函数
      loginSuccess() {
        // 设置用户信息
        this.$store.dispatch("GetInfo").then(res => {
          this.$tab.reLaunch("/pages/town/index")
        })
      },
      // 个人登录
      personalLogin() {
        this.loginForm.password = this.loginForm.code
        this.$store
          .dispatch("unepLogin", {
            ...this.loginForm,
            userType: this.$route.query.userType,
            deptId: this.$route.query.deptId
          })
          .then(() => {
            this.$modal.closeLoading()
            this.loginSuccess()
          })
      }
    }
  }
</script>

<style lang="scss">
  ::v-deep .uni-select {
    border: none;
    padding-right: 30rpx;
    .uni-select__input-placeholder,
    .uni-select__input-text {
      font-size: 32rpx;
      display: flex;
      justify-content: flex-start;
      padding-left: 10rpx;
    }
    .uni-select__selector-item {
      font-size: 32rpx;
    }
  }
  page {
    background-color: #ffffff;
  }
  .bgcon {
    width: 100%;
    height: calc(100vh - 150px);
    background: url("/static/images/competition/logbg_3.png") no-repeat;
    background-size: 100% auto;
    background-position: 0 -60px;
  }
  .get-code-msg {
    width: 260rpx;
    margin-right: 20rpx;
    color: #f59a23;
  }
  .active {
    color: #888 !important;
  }
  .normal-login-container {
    width: 100%;
    height: 100vh;
    overflow: hidden;
    .logo-content {
      image {
        border-radius: 4px;
      }

      .title {
        margin-top: 10px;
        font-size: 18px;
        font-weight: 600;
      }

      .tab {
        width: 100%;
        margin-top: 10px;
      }
    }

    .login-form-content {
      text-align: center;
      margin: 10px auto 10px auto;
      width: 80%;

      .input-item {
        position: relative;
        margin: 20px auto;
        padding-left: 20rpx;
        background-color: #f5f6f7;
        height: 45px;
        border-radius: 20px;
        background: #fff;
        box-shadow: 0 0 10rpx rgba(0, 0, 0, 0.1);

        .input {
          width: 100%;
          font-size: 16px;
          line-height: 20px;
          text-align: left;
          padding-left: 15px;
        }
      }

      .login-btn {
        background-color: #f59a23;
        margin-top: 40px;
        height: 45px;
      }

      .xieyi {
        color: #333;
        font-size: 14px;
      }

      .login-code {
        height: 38px;
        float: right;

        .login-code-img {
          height: 38px;
          position: absolute;
          margin-left: 10px;
          width: 200rpx;
        }
      }
    }

    .required::before {
      content: "*";
      color: red;
      position: absolute;
      left: 25rpx;
      font-size: 40rpx;
      font-weight: bold;
    }
  }

  .xieyi {
    font-size: 12px;
  }

  .logincon {
    margin-top: 20rpx;
    display: flex;
    justify-content: center;
    flex-direction: column;
    width: 100%;
    align-items: center;
    .tenant-icon {
      height: 150rpx;
    }
    .title {
      margin: 20rpx 0;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #ee1d23;
      font-size: 42rpx;
      font-weight: 700;
    }
    .committee-name {
      color: #333333;
      font-size: 34rpx;
      font-weight: bold;
    }
  }
</style>

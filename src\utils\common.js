/*
 * @Description: 通用js方法封装
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-06-26 08:55:28
 * @LastEditTime: 2024-05-28 17:21:43
 */

/**  isRequiredIP 为true时如果当前是 IP 地址，则直接返回IP Address，为false则返回空; 如果是 uzi.bkehs.cn 则返回'uzi' */
export function getSubdomain(isRequiredIP = true) {
  try {
    const env = process.env.VUE_APP_ENV
    let subdomain = ""
    const { domain } = document
    const domainList = domain.split(".")
    const ipAddressReg =
      /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/ // 若为 IP 地址、localhost，则直接返回
    if (ipAddressReg.test(domain) || domain === "localhost") {
      return isRequiredIP ? domain : ""
    }
    subdomain = domainList[0]

    if (env === "production") {
      if (subdomain === "training" || subdomain === "learning") return ""
      subdomain = subdomain + "."
      return subdomain || document.domain
    } else if (env === "hwproduction" || env === "xdproduction") {
      subdomain = subdomain.replace("eduapp", "")
      return subdomain
    }
  } catch (e) {
    return document.domain
  }
}

/**
 * 显示消息提示框
 * @param content 提示的标题
 */
export function toast(content) {
  uni.showToast({
    icon: "none",
    title: content
  })
}

/**
 * 显示模态弹窗
 * @param content 提示的标题
 */
export function showConfirm(content) {
  return new Promise((resolve, reject) => {
    uni.showModal({
      title: "提示",
      content: content,
      cancelText: "取消",
      confirmText: "确定",
      success: function (res) {
        resolve(res)
      }
    })
  })
}

/**
 * 参数处理
 * @param params 参数
 */
export function tansParams(params) {
  let result = ""
  for (const propName of Object.keys(params)) {
    const value = params[propName]
    var part = encodeURIComponent(propName) + "="
    if (value !== null && value !== "" && typeof value !== "undefined") {
      if (typeof value === "object") {
        for (const key of Object.keys(value)) {
          if (value[key] !== null && value[key] !== "" && typeof value[key] !== "undefined") {
            let params = propName + "[" + key + "]"
            var subPart = encodeURIComponent(params) + "="
            result += subPart + encodeURIComponent(value[key]) + "&"
          }
        }
      } else {
        result += part + encodeURIComponent(value) + "&"
      }
    }
  }
  return result
}

// 判断当前时间是否在指定时间区间内
export function isInTimeRange(start, end) {
  const now = new Date() // 获取当前时间
  const startTime = new Date(start?.replace(/-/g, "/")) // 开始时间
  const endTime = new Date(end?.replace(/-/g, "/")) // 结束时间
  return now >= startTime && now <= endTime
}

// 获取文件名称
export function getFileName(name) {
  name = decodeURIComponent(name)
  // 从URL中获取文件名
  const fileName = name.substring(name.lastIndexOf("/") + 1)
  // 移除文件扩展名
  const fileNameWithoutExtension = fileName.split(".").slice(0, -1).join(".")

  return fileNameWithoutExtension
}

/**
 * 判断手机机型
 */
export function isAndroidOrIOS() {
  var u = navigator.userAgent
  var isAndroid = u.indexOf("Android") > -1 || u.indexOf("Adr") > -1 //android终端
  var isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) //ios终端
  if (isAndroid) {
    return "android"
  }
  if (isiOS) {
    return "ios"
  }
  return false
}

/**
 * @description: 判断文件类型
 * @param {*} fileStr
 * @return {*} "image"/"video"/"doc"/"other"
 */
export function validateFileFormat(fileStr) {
  let videoRegex = /(\mp4|\avi|\mkv)$/i
  let imgRegex = /(\jpg|\jpeg|\png|\gif|\webp)$/i
  let docRegex = /(\zip|\doc|\pdf|\ppt|\pptx|\txt|\xls|\xlsx|\xlt|\xltx|\docx|\dwg\vsdx)$/
  if (imgRegex.test(fileStr)) {
    return "image"
  } else if (videoRegex.test(fileStr)) {
    return "video"
  } else if (docRegex.test(fileStr)) {
    return "doc"
  } else {
    return "other"
  }
}

// 获取传入时间到当前时间相差的秒数
export function getTimeDifferenceInSeconds(time) {
  const timestamp = new Date(time).getTime()
  const nowTimestamp = new Date().getTime()
  const differenceInMilliseconds = Math.abs(nowTimestamp - timestamp)
  const differenceInSeconds = Math.floor(differenceInMilliseconds / 1000)
  return differenceInSeconds
}

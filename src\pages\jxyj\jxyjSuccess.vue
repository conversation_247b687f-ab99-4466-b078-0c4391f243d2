<template>
  <view>
    <view class="ray-msg-content">
      <view class="ray-msg-title">提交成功</view>
      <view class="score-info">
        您的分数：<text style="color: #d9001b">{{ score }}</text> 分
      </view>
      <view class="time-info">
        答题时间：<text style="color: #d9001b">{{ answerTime }}</text>
      </view>
      <view class="action-btn" @click="handleViewDetail">
        查看答题情况
      </view>
      
    </view>
  </view>
</template>

<script>
  export default {
    name: "jxyj-success",
    onLoad(options) {
      this.baseId = options.baseId
      this.arrangeId = options.arrangeId
      this.paperAnswerId = options.paperAnswerId
      this.score = Number(options.score)
      this.duration = Number(options.duration) || 80
      this.answerTime = this.formatDuration(this.duration)
    },
    data() {
      return {
        baseId: "",
        arrangeId: "",
        paperAnswerId: "",
        score: "",
        duration: 0,
        answerTime: ""
      }
    },
    methods: {
      formatDuration(seconds) {
        const minutes = Math.floor(seconds / 60)
        const remainingSeconds = seconds % 60
        return `${minutes}分${remainingSeconds}秒`
      },
      handleViewDetail() {
        uni.navigateTo({
          url: `/pages/mine/exam/examed/index?baseId=${this.baseId}&arrangeId=${this.arrangeId}&paperAnswerId=${this.paperAnswerId}&showNavBar=false`
        })
      },
      handleReExam(item) {
        uni.navigateTo({
          url: `/pages/mine/exam/prepare/index?baseId=${this.baseId}&arrangeId=${this.arrangeId}&showNavBar=false`
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
  @mixin fixed($l: 0, $t: 0, $r: auto, $b: auto) {
    position: fixed;
    left: $l;
    top: $t;
    right: $r;
    bottom: $b;
  }
  .ray-msg-content {
    width: 590rpx;
    height: auto;
    margin: auto;
    padding: 60rpx;
    @include fixed(50%, 20%);
    z-index: 10000;
    transform: translate(-50%, -50%);
    border-radius: 28rpx;
    background-color: #f1f1f1;
    box-sizing: border-box;
    text-align: center;
  }
  .ray-msg-icon {
    display: inline-block;
    width: 152rpx;
    height: 152rpx;
    mask: url(https://static.jiaoyubao.cn/images/common/icon-check-block.svg) no-repeat;
    mask-size: 100% 100%;
    background-color: #53b883;
    &.ray-msg-error {
      mask: url(https://static.jiaoyubao.cn/images/common/icon-cross-block.svg) no-repeat;
      background-color: #fe1940;
    }
  }
  .emoji {
    width: 152rpx;
    height: 152rpx;
    font-size: 120rpx;
  }
  .ray-msg-title {
    margin: 20rpx auto 40rpx;
    font-size: 48rpx;
    line-height: 56rpx;
    color: #00c9a7;
    font-weight: bold;
  }
  .score-info {
    color: #333333;
    margin: 30rpx 0;
    font-size: 36rpx;
    line-height: 50rpx;
    font-weight: bold;
  }
  .time-info {
    color: #333333;
    margin: 30rpx 0;
    font-size: 36rpx;
    line-height: 50rpx;
    font-weight: bold;
  }
  .action-btn {
    width: 100%;
    height: 88rpx;
    margin: 40rpx auto 0;
    background: #00c9a7;
    border-radius: 44rpx;
    font-size: 32rpx;
    line-height: 88rpx;
    color: #fff;
    text-align: center;
    cursor: pointer;
    &:active {
      opacity: 0.8;
    }
  }
</style>

/*
 * @Description: 
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-12-04 09:42:36
 * @LastEditTime: 2024-12-24 15:32:14
 */
import request from '@/utils/request'
import config from '@/config'
import store from '@/store'
import { getToken } from '@/utils/auth'

// 人脸识别验证
export function faceVerify(data) {
  return new Promise((resolve, reject) => {
    uni.uploadFile({
      url: config.baseUrl + '/system/user/faceVerify',
      filePath: data.tempFilePath,
      name: 'file',
      header: {
        'Authorization': 'Bearer ' + getToken(),
        'Tenant': store.state.tenant.domainName
      },
      formData: {
        terminalSource: data.terminalSource,
        userId: data.userId,
        userName: data.userName,
        deptId: data.deptId,
        deptName: data.deptName,
        taskId: data.taskId,
        subTaskType: data.subTaskType,
        subTaskId: data.subTaskId,
        subTaskName: data.subTaskName,
        arrangeId: data.arrangeId
      },
      success: (res) => {
        if (res.statusCode !== 200) {
          reject(new Error('上传失败'))
          return
        }
        try {
          const result = JSON.parse(res.data)
          resolve(result)
        } catch (error) {
          reject(new Error('解析响应数据失败'))
        }
      },
      fail: (error) => {
        reject(error)
      }
    })
  })
}
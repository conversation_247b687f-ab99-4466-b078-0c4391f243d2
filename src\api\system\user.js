/*
 * @Description: system相关API
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-06-25 17:10:02
 * @LastEditTime: 2024-12-13 10:46:00
 */
import upload from "@/utils/upload"
import request from "@/utils/request"

// 用户密码重置
export function updateUserPwd(oldPassword, newPassword) {
  const data = {
    oldPassword,
    newPassword
  }
  return request({
    url: "/system/user/profile/updatePwd",
    method: "put",
    params: data
  })
}

// 查询用户个人信息
export function getUserProfile() {
  return request({
    url: "/system/user/profile",
    method: "get"
  })
}

// 修改用户个人信息
export function updateUserProfile(data) {
  return request({
    url: "/system/user/profile",
    method: "put",
    data: data
  })
}

// 用户头像上传
export function uploadAvatar(data) {
  return upload({
    url: "/system/user/profile/avatar",
    name: data.name,
    filePath: data.filePath
  })
}

// 文件/图片上传
export function fileUpload(data) {
  return upload({
    url: "/file/upload",
    filePath: data
  })
}

// 编辑用户头像/昵称
export function editWxUser(data) {
  return request({
    url: "/system/wx/user",
    method: "put",
    data: data
  })
}

// 双控跳转自动登录
export function autoLogin(data) {
  return request({
    url: "/auth/sysLogin",
    method: "post",
    data: { ...data, fromType: "apptraining" }
  })
}

// 查询部门下拉树结构
export function deptTreeSelect(params) {
  return request({
    url: "/system/user/deptTree",
    method: "get",
    params
  })
}

// 获取用户积分、学分、学时
export function getUserExtraInfo() {
  return request({
    url: "/system/user/extraInfo",
    method: "get"
  })
}

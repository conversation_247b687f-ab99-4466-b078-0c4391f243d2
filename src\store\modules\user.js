import config from "@/config"
import storage from "@/utils/storage"
import { loginRequest, logout, getInfo, getPublic<PERSON>ey } from "@/api/system/login"
import { getToken, setToken, removeToken } from "@/utils/auth"
import { encrypt } from "@/utils/jsencrypt"
import { handleUnepLogin } from "@/api/competition"
import { removeDomain } from "@/utils/auth"
import { getRecentStudy } from "@/api/trainingTask"
const baseUrl = config.baseUrl

const user = {
  state: {
    token: getToken(),
    nickName: storage.get("nickName"),
    username: storage.get("username"),
    avatar: storage.get("avatarUrl"),
    userId: storage.get("userId"),
    roles: [],
    logBtn: true,
    showRecentStudyPopup: true,
    deptInfo: {},
    recentStudyList: []
  },

  mutations: {
    SET_TOKEN: (state, token) => {
      state.token = token
    },
    SET_NICK_NAME: (state, nickName) => {
      state.nickName = nickName
      storage.set("nickName", nickName)
    },
    SET_USERNAME: (state, username) => {
      state.username = username
      storage.set("username", username)
    },
    SET_AVATAR: (state, avatar) => {
      state.avatar = avatar
      storage.set("avatarUrl", avatar)
    },
    SET_ID: (state, userId) => {
      state.userId = userId
      storage.set("userId", userId)
    },
    SET_ROLES: (state, roles) => {
      state.roles = roles
    },
    SET_LOGBTN: state => {
      state.logBtn = false
    },
    SET_SHOW_RECENT_STUDY_POPUP: (state, show) => {
      state.showRecentStudyPopup = show
    },
    SET_DEPT_INFO: (state, deptInfo) => {
      state.deptInfo = deptInfo
    },
    SET_RECENT_STUDY_LIST: (state, list) => {
      state.recentStudyList = list
    }
  },

  actions: {
    // 登录
    Login({ commit }, userInfo) {
      return new Promise((resolve, reject) => {
        getPublicKey().then(response => {
          const username = userInfo.username.trim()
          // 调用加密方法(传密码和公钥)
          const password = encrypt(userInfo.password, response.publicKey)
          const code = userInfo.code
          const uuid = userInfo.uuid
          loginRequest(username, password, code, uuid)
            .then(res => {
              commit("SET_TOKEN", res.data.access_token)
              commit("SET_SHOW_RECENT_STUDY_POPUP", true)
              setToken(res.data.access_token)
              resolve(response.publicKey)
            })
            .catch(error => {
              reject(error)
            })
        })
      })
    },
    // 获取用户信息
    GetInfo({ commit, state }) {
      return new Promise((resolve, reject) => {
        getInfo()
          .then(res => {
            commit("SET_ID", res.user.userId)
            commit("SET_DEPT_INFO", res.user.dept)
            const user = res.user
            const avatar = user.avatar || require("@/static/images/avatar.png")
            commit("SET_AVATAR", avatar)
            commit("SET_NICK_NAME", user.nickname)
            commit("SET_USERNAME", user.userName)
            if (res.roles && res.roles.length > 0) {
              commit("SET_ROLES", res.roles)
            } else {
              commit("SET_ROLES", ["ROLE_DEFAULT"])
            }
            resolve(res)
          })
          .catch(error => {
            reject(error)
          })
      })
    },

    // 退出系统
    LogOut({ commit, state }) {
      return new Promise((resolve, reject) => {
        logout(state.token)
          .then(() => {
            // 清除 token
            commit("SET_TOKEN", "")
            commit("SET_ROLES", [])
            commit("SET_NICK_NAME", "")
            commit("SET_USERNAME", "")
            commit("SET_AVATAR", "")
            commit("SET_ID", "")
            // 清除 token
            removeToken()
            // 重置状态
            commit("SET_SHOW_RECENT_STUDY_POPUP", true)
            resolve()
          })
          .catch(error => {
            reject(error)
          })
      })
    },
    // 非主动退出
    tokenOut({ commit, state }) {
      commit("SET_TOKEN", "")
      removeToken()
    },
    // 防灾减灾登录
    unepLogin({ commit }, loginForm) {
      return new Promise((resolve, reject) => {
        const requestData = { ...loginForm, scene: loginForm.scene || 1 }
        handleUnepLogin(requestData)
          .then(res => {
            setToken(res.data.access_token)
            commit("SET_TOKEN", res.data.access_token)
            resolve()
          })
          .catch(err => {
            reject(err)
          })
      })
    },
    // 获取最近学习数据
    async getRecentStudyData({ commit }) {
      try {
        const res = await getRecentStudy()
        commit("SET_RECENT_STUDY_LIST", res.data || [])
        return res.data
      } catch (error) {
        console.error("获取最近学习数据失败:", error)
        return []
      }
    }
  }
}

export default user

/*
 * @Description:
 * @Author: <PERSON><PERSON>
 * @LastEditors: <PERSON><PERSON>
 * @Date: 2023-04-12 08:48:30
 * @LastEditTime: 2023-07-07 17:07:29
 */
import Vue from "vue"
import Vuex from "vuex"
import course from "@/store/modules/course"
import user from "@/store/modules/user"
import dict from "@/store/modules/dict"
import tenant from "@/store/modules/tenant"
import getters from "./getters"

Vue.use(Vuex)

const store = new Vuex.Store({
  modules: {
    course,
    user,
    dict,
    tenant
  },
  getters
})

export default store

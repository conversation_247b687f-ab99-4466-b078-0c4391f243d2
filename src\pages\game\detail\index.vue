<!--
  @Description: 游戏详情页
  @Author: <PERSON>
 * @LastEditors: <PERSON>
  @Date: 2025-01-27
 * @LastEditTime: 2025-06-09 11:05:21
-->
<template>
  <div class="game-detail-page">
    <!-- 正常游戏详情界面 -->
    <div v-if="!isGameFullscreen" class="game-detail-content">
      <!-- 自定义导航栏 -->
      <div class="navbar">
        <div class="navbar-content">
          <div class="navbar-left" @click="goBack">
            <text class="back-icon">←</text>
          </div>
          <text class="navbar-title">{{ (gameInfo && gameInfo.gameName) || "游戏详情" }}</text>
          <div class="navbar-right"></div>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <div class="loading-content">
          <text>加载中...</text>
        </div>
      </div>

      <!-- 游戏不存在或已下架 -->
      <div v-else-if="!gameInfo || !gameInfo.id || gameInfo.status !== 1" class="error-container">
        <div class="error-content">
          <text class="error-text">
            {{ !gameInfo || !gameInfo.id ? "游戏不存在" : "该游戏已下架" }}
          </text>
          <button class="back-btn" @click="goBack">返回游戏中心</button>
        </div>
      </div>

      <!-- 正常游戏内容 -->
      <div v-else class="game-content">
        <!-- 游戏信息卡片 -->
        <div class="game-info-card">
          <div class="game-cover">
            <image :src="gameInfo.gameCover || ''" mode="aspectFill" class="cover-image" />
          </div>
          <div class="game-details">
            <div class="game-name">{{ gameInfo.gameName || "" }}</div>
            <div class="game-meta">
              <text class="game-type">{{ gameInfo.gameType || "休闲游戏" }}</text>
              <text class="game-status" :class="gameInfo.status === 1 ? 'online' : 'offline'">
                {{ gameInfo.status === 1 ? "上架中" : "已下架" }}
              </text>
            </div>
          </div>
        </div>

        <!-- 游戏区域 -->
        <div class="game-frame-container">
          <!-- 游戏加载状态 -->
          <div v-if="gameLoading" class="game-loading">
            <text>游戏加载中...</text>
          </div>

          <!-- 游戏加载错误 -->
          <div v-if="gameError" class="game-error">
            <text>游戏加载失败</text>
            <button class="retry-btn" @click="retryLoadGame">重试</button>
          </div>

          <!-- 游戏启动提示 -->
          <div
            v-if="!gameLoading && !gameError && gameInfo.gameLink && !gameStarted"
            class="game-start-hint"
          >
            <text>点击开始游戏</text>
            <div class="start-buttons">
              <button class="start-btn" @click="startGame">普通模式</button>
              <button class="fullscreen-btn" @click="startFullscreenGame">全屏游戏</button>
            </div>
          </div>

          <!-- 游戏iframe (普通模式) -->
          <web-view
            v-show="
              !gameLoading && !gameError && gameInfo.gameLink && gameStarted && !isGameFullscreen
            "
            :src="gameInfo.gameLink || ''"
            class="game-webview"
            @message="handleMessage"
            @error="handleWebViewError"
          ></web-view>

          <!-- 已开始游戏后的全屏按钮 -->
          <div v-if="gameStarted && !isGameFullscreen" class="fullscreen-toggle">
            <button class="toggle-fullscreen-btn" @click="enterFullscreen">全屏</button>
          </div>
        </div>
      </div>
    </div>

    <!-- 全屏游戏界面 -->
    <div v-if="isGameFullscreen" class="game-fullscreen">
      <!-- 返回按钮 -->
      <div class="back-button" @click="exitFullscreen">
        <div class="back-icon">
          <text class="back-text">←</text>
        </div>
      </div>

      <!-- 全屏游戏iframe (H5环境使用iframe替代web-view) -->
      <!-- #ifdef H5 -->
      <iframe
        v-if="gameInfo.gameLink && !fullscreenLoading"
        :src="gameInfo.gameLink"
        class="game-iframe-fullscreen"
        frameborder="0"
        allowfullscreen
        @load="onGameLoad"
      ></iframe>
      <!-- #endif -->

      <!-- #ifndef H5 -->
      <web-view
        v-if="gameInfo.gameLink && !fullscreenLoading"
        :src="gameInfo.gameLink"
        class="game-webview-fullscreen"
        @message="handleMessage"
        @error="handleWebViewError"
        @load="onGameLoad"
      ></web-view>
      <!-- #endif -->

      <!-- 游戏加载中 -->
      <div v-if="fullscreenLoading" class="game-loading-fullscreen">
        <div class="loading-spinner"></div>
        <text class="loading-text">游戏加载中...</text>
      </div>
    </div>
  </div>
</template>

<script>
  import { getGame } from "@/api/game"

  export default {
    name: "GameDetail",
    data() {
      return {
        gameId: "",
        gameInfo: {},
        loading: true,
        gameLoading: true,
        gameError: false,
        gameStarted: false,

        // 全屏游戏相关
        isGameFullscreen: false,
        fullscreenLoading: false,
        screenOrientation: "portrait" // portrait | landscape
      }
    },
    onLoad(options) {
      this.gameId = options.id
      if (this.gameId) {
        this.loadGameDetail()
      } else {
        this.loading = false
        uni.showToast({
          title: "游戏ID不存在",
          icon: "none"
        })
      }
    },
    onShow() {
      // 设置状态栏颜色
      uni.setNavigationBarColor({
        frontColor: "#000000",
        backgroundColor: "#ffffff"
      })
    },
    onHide() {
      // 页面隐藏时退出全屏
      if (this.isGameFullscreen) {
        this.exitFullscreen()
      }
    },
    onUnload() {
      // 页面卸载时恢复竖屏
      this.setScreenOrientation("portrait")
    },
    methods: {
      async loadGameDetail() {
        this.loading = true
        try {
          const res = await getGame(this.gameId)
          const gameData = res.data

          if (gameData && gameData.id) {
            this.gameInfo = gameData

            if (gameData.gameLink) {
              // 直接设置为不加载状态，因为web-view会自动处理加载
              this.gameLoading = false
              this.gameError = false

              // 延迟一下确保web-view渲染完成
              setTimeout(() => {
                this.gameLoading = false
              }, 500)
            } else {
              this.gameLoading = false
              this.gameError = true
            }
          } else {
            this.gameInfo = {}
            this.gameLoading = false
            this.gameError = true
          }
        } catch (error) {
          console.error("获取游戏详情失败:", error)

          // 如果API调用失败，使用测试数据
          this.gameInfo = {
            id: this.gameId,
            gameName: "农村安全消消乐",
            gameType: "休闲游戏",
            gameCover: "https://via.placeholder.com/200x120/4CAF50/white?text=Game",
            gameLink: "https://www.baidu.com", // 测试用URL
            status: 1
          }
          console.log("使用测试游戏数据:", this.gameInfo)

          this.gameLoading = false
          this.gameError = false

          uni.showToast({
            title: "使用测试数据",
            icon: "none"
          })
        } finally {
          this.loading = false
        }
      },

      // webview加载错误
      handleWebViewError() {
        this.gameLoading = false
        this.gameError = true
        this.fullscreenLoading = false
      },

      // webview消息处理
      handleMessage(event) {
        console.log("webview message:", event.detail.data)
      },

      // 开始游戏 (普通模式)
      startGame() {
        console.log("开始普通模式游戏")
        this.gameStarted = true
      },

      // 开始全屏游戏
      async startFullscreenGame() {
        console.log("开始全屏游戏")
        this.gameStarted = true
        await this.enterFullscreen()
      },

      // 进入全屏模式
      async enterFullscreen() {
        try {
          console.log("准备进入全屏模式, 游戏URL:", this.gameInfo.gameLink)

          if (!this.gameInfo.gameLink) {
            uni.showToast({
              title: "游戏链接无效",
              icon: "none"
            })
            return
          }

          this.fullscreenLoading = true

          // 先隐藏状态栏和导航栏
          this.hideStatusBar()

          // 切换到全屏模式
          this.isGameFullscreen = true

          // 确保DOM更新
          await this.$nextTick()

          // 强制进入全屏显示
          this.requestFullscreen()

          // 延迟处理，确保全屏生效
          setTimeout(async () => {
            // 设置横屏
            await this.setScreenOrientation("landscape")

            uni.showToast({
              title: "已进入全屏模式",
              icon: "success",
              duration: 1500
            })

            // 延迟移除加载状态，等待iframe/web-view加载
            setTimeout(() => {
              if (this.fullscreenLoading) {
                this.fullscreenLoading = false
              }
            }, 2000)
          }, 500)
        } catch (error) {
          console.error("进入全屏失败:", error)
          uni.showToast({
            title: "全屏模式启动失败",
            icon: "none"
          })
          this.exitFullscreen()
        }
      },

      // 退出全屏模式
      async exitFullscreen() {
        console.log("退出全屏游戏")

        try {
          // 退出全屏显示
          this.exitFullscreenDisplay()

          // 重置状态
          this.isGameFullscreen = false
          this.fullscreenLoading = false

          // 恢复竖屏
          await this.setScreenOrientation("portrait")

          // 显示状态栏
          this.showStatusBar()

          uni.showToast({
            title: "已退出全屏",
            icon: "success",
            duration: 1500
          })
        } catch (error) {
          console.error("退出全屏失败:", error)
          // 即使出错也要重置状态
          this.isGameFullscreen = false
          this.fullscreenLoading = false
        }
      },

      // 设置屏幕方向
      setScreenOrientation(orientation) {
        return new Promise((resolve, reject) => {
          // #ifdef APP-PLUS
          // 如果是APP环境，使用原生API
          try {
            plus.screen.lockOrientation(
              orientation === "landscape" ? "landscape-primary" : "portrait-primary"
            )
            this.screenOrientation = orientation
            setTimeout(() => resolve(), 300)
          } catch (error) {
            console.warn("APP环境屏幕方向设置失败:", error)
            resolve()
          }
          // #endif

          // #ifndef APP-PLUS
          // H5环境设置屏幕方向偏好和CSS样式
          console.log("H5环境，设置屏幕方向偏好:", orientation === "landscape" ? "横屏" : "竖屏")
          this.screenOrientation = orientation

          // 尝试使用Screen Orientation API
          if (screen && screen.orientation && screen.orientation.lock) {
            try {
              const orientationType =
                orientation === "landscape" ? "landscape-primary" : "portrait-primary"
              screen.orientation
                .lock(orientationType)
                .then(() => {
                  console.log("屏幕方向锁定成功:", orientationType)
                })
                .catch(error => {
                  console.warn("屏幕方向锁定失败:", error)
                  this.showOrientationHint(orientation)
                })
            } catch (error) {
              console.warn("Screen Orientation API不支持:", error)
              this.showOrientationHint(orientation)
            }
          } else {
            this.showOrientationHint(orientation)
          }

          setTimeout(() => resolve(), 100)
          // #endif
        })
      },

      // 显示屏幕方向提示
      showOrientationHint(orientation) {
        if (orientation === "landscape") {
          uni.showToast({
            title: "请将设备横置以获得最佳体验",
            icon: "none",
            duration: 3000
          })
        }
      },

      // 请求全屏显示
      requestFullscreen() {
        try {
          // #ifndef APP-PLUS
          // H5环境使用全屏API
          const element = document.documentElement
          if (element.requestFullscreen) {
            element.requestFullscreen()
          } else if (element.webkitRequestFullscreen) {
            element.webkitRequestFullscreen()
          } else if (element.mozRequestFullScreen) {
            element.mozRequestFullScreen()
          } else if (element.msRequestFullscreen) {
            element.msRequestFullscreen()
          }

          // 隐藏地址栏等浏览器UI
          if (window.navigator && window.navigator.standalone !== undefined) {
            // iOS Safari
            const viewport = document.querySelector("meta[name=viewport]")
            if (viewport) {
              viewport.content =
                "width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover"
            }
          }
          // #endif

          console.log("已请求全屏显示")
        } catch (error) {
          console.warn("全屏API不支持:", error)
        }
      },

      // 退出全屏显示
      exitFullscreenDisplay() {
        try {
          // #ifndef APP-PLUS
          // H5环境退出全屏
          if (document.exitFullscreen) {
            document.exitFullscreen()
          } else if (document.webkitExitFullscreen) {
            document.webkitExitFullscreen()
          } else if (document.mozCancelFullScreen) {
            document.mozCancelFullScreen()
          } else if (document.msExitFullscreen) {
            document.msExitFullscreen()
          }
          // #endif

          console.log("已退出全屏显示")
        } catch (error) {
          console.warn("退出全屏失败:", error)
        }
      },

      // 隐藏状态栏
      hideStatusBar() {
        // #ifdef APP-PLUS
        try {
          plus.navigator.setStatusBarStyle("UIStatusBarStyleDefault")
          plus.navigator.hideStatusBar()
        } catch (error) {
          console.warn("隐藏状态栏失败:", error)
        }
        // #endif
      },

      // 显示状态栏
      showStatusBar() {
        // #ifdef APP-PLUS
        try {
          plus.navigator.showStatusBar()
        } catch (error) {
          console.warn("显示状态栏失败:", error)
        }
        // #endif
      },

      // 游戏加载完成
      onGameLoad() {
        console.log("全屏游戏加载完成")
        setTimeout(() => {
          this.fullscreenLoading = false
        }, 500)
      },

      // 重试加载游戏
      retryLoadGame() {
        if (!this.gameInfo || !this.gameInfo.gameLink) {
          this.loadGameDetail()
          return
        }

        this.gameError = false
        this.gameLoading = true

        // 强制重新加载webview
        this.$nextTick(() => {
          // 可以通过修改src来重新加载
          const originalSrc = this.gameInfo.gameLink
          this.gameInfo.gameLink = ""
          this.$nextTick(() => {
            this.gameInfo.gameLink = originalSrc
            // 延迟设置加载完成状态
            setTimeout(() => {
              this.gameLoading = false
            }, 1000)
          })
        })
      },

      // 返回上一页
      goBack() {
        // 如果是全屏模式，先退出全屏
        if (this.isGameFullscreen) {
          this.exitFullscreen()
          return
        }

        uni.navigateBack({
          delta: 1
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
  .game-detail-page {
    height: 100vh;
    position: relative;
    overflow: hidden;
  }

  // 正常详情页面样式
  .game-detail-content {
    height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    flex-direction: column;
  }

  .navbar {
    height: 44px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;

    .navbar-content {
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 16px;

      .navbar-left {
        width: 44px;
        height: 44px;
        display: flex;
        align-items: center;
        justify-content: center;

        .back-icon {
          font-size: 20px;
          color: #333;
          font-weight: bold;
        }
      }

      .navbar-title {
        flex: 1;
        text-align: center;
        font-size: 18px;
        font-weight: 600;
        color: #333;
      }

      .navbar-right {
        width: 44px;
      }
    }
  }

  .loading-container,
  .error-container {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
  }

  .loading-content,
  .error-content {
    text-align: center;
    color: rgba(255, 255, 255, 0.8);

    .error-text {
      font-size: 16px;
      margin-bottom: 20px;
    }

    .back-btn {
      padding: 10px 20px;
      background: #409eff;
      color: white;
      border: none;
      border-radius: 6px;
      font-size: 14px;
    }
  }

  .game-content {
    flex: 1;
    padding: 16px;
    display: flex;
    flex-direction: column;
  }

  .game-info-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 16px;
    display: flex;
    gap: 16px;

    .game-cover {
      width: 80px;
      height: 80px;
      border-radius: 8px;
      overflow: hidden;

      .cover-image {
        width: 100%;
        height: 100%;
      }
    }

    .game-details {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;

      .game-name {
        font-size: 18px;
        font-weight: 600;
        color: #333;
        margin-bottom: 8px;
      }

      .game-meta {
        display: flex;
        gap: 12px;

        .game-type {
          font-size: 12px;
          color: #666;
          padding: 2px 8px;
          background: #f0f9ff;
          border-radius: 8px;
        }

        .game-status {
          font-size: 12px;
          padding: 2px 8px;
          border-radius: 8px;

          &.online {
            background: #e7f6f7;
            color: #10b981;
          }

          &.offline {
            background: #fef2f2;
            color: #ef4444;
          }
        }
      }
    }
  }

  .game-frame-container {
  flex: 1;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
  
  // 确保容器内的层级关系正确
  .game-webview {
    z-index: 1;
  }
  
  .fullscreen-toggle {
    z-index: 999 !important;
  }
}

  .game-loading,
  .game-error,
  .game-start-hint {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px;
    color: #666;

    .start-buttons {
      display: flex;
      gap: 12px;
      margin-top: 16px;
    }

    .start-btn,
    .fullscreen-btn,
    .retry-btn {
      padding: 12px 24px;
      border: none;
      border-radius: 6px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;
    }

    .start-btn {
      background: #409eff;
      color: white;
    }

    .fullscreen-btn {
      background: #67c23a;
      color: white;
    }

    .retry-btn {
      background: #f56c6c;
      color: white;
    }
  }

  .game-webview {
    flex: 1;
    width: 100%;
  }

  .fullscreen-toggle {
  position: absolute !important;
  bottom: 20px !important;
  right: 20px !important;
  z-index: 1000 !important;
  
  .toggle-fullscreen-btn {
    padding: 10px 16px !important;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.85), rgba(0, 0, 0, 0.9)) !important;
    color: white !important;
    border: 2px solid rgba(255, 255, 255, 0.4) !important;
    border-radius: 20px !important;
    font-size: 13px !important;
    font-weight: 600 !important;
    box-shadow: 
      0 4px 15px rgba(0, 0, 0, 0.4),
      0 2px 5px rgba(0, 0, 0, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
    backdrop-filter: blur(15px) !important;
    transition: all 0.3s ease !important;
    cursor: pointer !important;
    user-select: none !important;
    
    // 添加图标
    &::before {
      content: "⛶" !important;
      margin-right: 6px !important;
      font-size: 14px !important;
    }
    
    &:hover {
      background: linear-gradient(135deg, rgba(0, 0, 0, 0.9), rgba(0, 0, 0, 0.95)) !important;
      border-color: rgba(255, 255, 255, 0.6) !important;
      transform: translateY(-2px) scale(1.02) !important;
      box-shadow: 
        0 6px 20px rgba(0, 0, 0, 0.5),
        0 4px 10px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
    }
    
    &:active {
      background: linear-gradient(135deg, rgba(0, 0, 0, 0.95), rgba(0, 0, 0, 1)) !important;
      transform: translateY(0) scale(0.98) !important;
      box-shadow: 
        0 2px 8px rgba(0, 0, 0, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
    }
  }
}

  // 全屏游戏样式
  .game-fullscreen {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    background: #000 !important;
    z-index: 99999 !important;
    overflow: hidden !important;

    // 确保全屏显示
    margin: 0 !important;
    padding: 0 !important;
    border: none !important;
    box-sizing: border-box !important;
  }

  .back-button {
    position: fixed !important;
    top: 20px !important;
    left: 20px !important;
    width: 44px !important;
    height: 44px !important;
    background: rgba(0, 0, 0, 0.7) !important;
    border-radius: 22px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    z-index: 100000 !important;
    transition: all 0.2s ease !important;
    border: 2px solid rgba(255, 255, 255, 0.2) !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3) !important;

    &:active {
      background: rgba(0, 0, 0, 0.9) !important;
      transform: scale(0.95) !important;
    }

    .back-icon {
      .back-text {
        color: white !important;
        font-size: 20px !important;
        font-weight: bold !important;
        line-height: 1 !important;
      }
    }
  }

  .game-webview-fullscreen,
  .game-iframe-fullscreen {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    width: 100% !important;
    height: 100% !important;
    border: none !important;
    margin: 0 !important;
    padding: 0 !important;
    background: #000 !important;
    z-index: 1 !important;
  }

  .game-loading-fullscreen {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    z-index: 10001;

    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 3px solid rgba(255, 255, 255, 0.3);
      border-top: 3px solid #fff;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: 16px;
    }

    .loading-text {
      color: white;
      font-size: 16px;
    }
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  // 横屏适配
  @media (orientation: landscape) {
    .game-fullscreen {
      .back-button {
        top: 10px !important;
        left: 10px !important;
        width: 40px !important;
        height: 40px !important;
        border-radius: 20px !important;
      }
    }
  }

  // 强制全屏样式（针对不同浏览器）
  .game-fullscreen {
    // Webkit浏览器全屏
    &:-webkit-full-screen {
      width: 100vw !important;
      height: 100vh !important;
    }

    // Firefox全屏
    &:-moz-full-screen {
      width: 100vw !important;
      height: 100vh !important;
    }

    // 标准全屏
    &:fullscreen {
      width: 100vw !important;
      height: 100vh !important;
    }
  }

  // 移动设备特殊处理
  @media only screen and (max-device-width: 768px) {
    .game-fullscreen {
      position: fixed !important;
      top: 0 !important;
      left: 0 !important;
      width: 100vw !important;
      height: 100vh !important;
      z-index: 999999 !important;

      .game-webview-fullscreen {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        width: 100vw !important;
        height: 100vh !important;
      }
    }
  }

  // iOS设备特殊处理
  @supports (-webkit-appearance: none) {
    .game-fullscreen {
      // iOS Safari全屏优化
      -webkit-overflow-scrolling: touch !important;

      .game-webview-fullscreen {
        // 确保web-view在iOS上正确显示
        -webkit-transform: translate3d(0, 0, 0) !important;
      }
    }
  }

  // 移动端适配
@media (max-width: 375px) {
  .game-info-card {
    padding: 12px;
    gap: 12px;
    
    .game-cover {
      width: 60px;
      height: 60px;
    }
    
    .game-details .game-name {
      font-size: 16px;
    }
  }
  
  .game-start-hint {
    padding: 20px;
    
    .start-buttons {
      flex-direction: column;
      width: 100%;
      
      .start-btn,
      .fullscreen-btn {
        width: 100%;
      }
    }
  }
  
  // 移动设备上的全屏按钮适配
  .fullscreen-toggle {
    bottom: 15px !important;
    right: 15px !important;
    
    .toggle-fullscreen-btn {
      padding: 8px 12px !important;
      font-size: 12px !important;
      border-radius: 16px !important;
      
      &::before {
        font-size: 12px !important;
        margin-right: 4px !important;
      }
    }
  }
}

// 小屏幕设备特殊处理
@media (max-width: 320px) {
  .fullscreen-toggle {
    bottom: 10px !important;
    right: 10px !important;
    
    .toggle-fullscreen-btn {
      padding: 6px 10px !important;
      font-size: 11px !important;
      
      &::before {
        font-size: 11px !important;
        margin-right: 3px !important;
      }
    }
  }
}

// 确保在游戏内容上方显示
.game-frame-container {
  .game-webview {
    position: relative !important;
    z-index: 1 !important;
  }
}
</style>

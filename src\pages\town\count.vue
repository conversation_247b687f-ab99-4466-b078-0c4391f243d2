<template>
  <view class="count">
    <view class="count-header">
      <view class="title">
        <view>数据一览</view>
        <uni-tooltip style="width: 60%" :content="content" placement="bottom">
          <img
            style="margin-left: 10px; margin-top: 5px"
            src="@/static/images/town/question.png"
            alt=""
          />
        </uni-tooltip>
      </view>
      <view class="info">
        <view class="top">
          <view style="border-right: 1px solid #d7d7d7">
            <view>今日学习人数</view>
            <view style="color: #a099fa; font-size: 24px; font-weight: bold">{{
              dashboardList.todayLearners
            }}</view>
          </view>
          <view :style="!userType ? 'border-right: 1px solid #d7d7d7' : ''">
            <view>本月学习人数</view>
            <view style="color: #eb6b41; font-size: 24px; font-weight: bold">{{
              dashboardList.thisMonthLearners
            }}</view>
          </view>
          <view v-if="!userType">
            <view>本月学习最长</view>
            <view style="color: #67d2c7; font-size: 24px; font-weight: bold"
              >{{ Math.floor((dashboardList.monthMaxStudyingTime * 1) / 60) }}分钟</view
            >
          </view>
        </view>
        <view style="margin: 15px 0; border-top: 1px solid #d7d7d7"></view>
        <view class="bottom">
          <view style="border-right: 1px solid #d7d7d7">
            <view>{{
              userType === "03"
                ? "本月学习人数最多"
                : userType === "02"
                ? "本月学习最长"
                : "本季度分数最高"
            }}</view>
            <view style="color: #ed9d32; font-size: 24px; font-weight: bold">{{
              userType === "03"
                ? dashboardList.maxMonthlyLearners || "暂无"
                : userType === "02"
                ? dashboardList.monthMaxStudyingTime || "暂无"
                : quarterDeptName || "暂无"
            }}</view>
          </view>
          <view>
            <view>{{
              userType === "03"
                ? "本月学习视频最多"
                : userType === "02"
                ? "本季度分数最高"
                : "本季度学习最长"
            }}</view>
            <view style="color: #68bff5; font-size: 24px; font-weight: bold">{{
              userType === "03"
                ? dashboardList.maxMonthlyCourseWatched || "暂无"
                : userType === "02"
                ? quarterDeptName || "暂无"
                : dashboardList.quarterMaxStudyingDept || "暂无"
            }}</view>
          </view>
        </view>
      </view>
    </view>
    <view class="count-body">
      <view class="tab">
        <view :class="activeTab === 1 ? 'active' : ''" @click="changeTab(1)">居委学习排名</view>
        <view :class="activeTab === 2 ? 'active' : ''" @click="changeTab(2)">社区总分构成</view>
      </view>
      <view class="content">
        <view v-show="activeTab === 1">
          <view class="date">
            <view @click="showPop" class="show-time">
              <img src="@/static/images/town/date.png" alt="" />
              <view>{{ userType === "03" ? defaultData.month : defaultData.quarter }}</view>
              <img src="@/static/images/town/arrow.png" alt="" />
            </view>
          </view>
          <view class="list">
            <view v-for="(item, index) in rankList" class="item" :key="index">
              <view class="item-index">{{ item.ranking }}</view>
              <view class="item-info">
                <view :class="userType ? 'info-special' : 'info-top'">
                  <view>{{ item.deptName }}</view>
                  <view v-if="userType !== '03'">总分：{{ item.score }}</view>
                  <view v-else>学习人数：{{ item.learners }}</view>
                  <view v-if="userType === '03'">视频学习个数：{{ item.courseCount }}</view>
                </view>
                <view class="info-bottom" v-if="userType !== '03'">
                  <view>视频学分 {{ item.courseIntegral }}</view>
                  <view>每月习题 {{ item.monthlyPaperScore }}</view>
                  <view>每月回顾 {{ item.mistakesReviewScore }}</view>
                  <view>季度考试 {{ item.quarterlyPaperScore }}</view>
                </view>
              </view>
            </view>
          </view>
        </view>
        <view v-show="activeTab === 2">
          <view class="charts-box">
            <view class="title">
              <view>{{
                userType === "03"
                  ? "社区学习情况"
                  : userType === "02"
                  ? "社区下属商铺学习情况"
                  : "各社区总分构成情况"
              }}</view>
              <view @click="showPop" class="show-time">
                <img src="@/static/images/town/date.png" alt="" />
                <view>{{ userType === "03" ? defaultData.month : defaultData.quarter }}</view>
                <img src="@/static/images/town/arrow.png" alt="" />
              </view>
            </view>
            <qiun-data-charts type="column" :opts="opts1" :chartData="chartData1" />
          </view>
          <view class="charts-box" style="margin-top: 80px; margin-bottom: 80px">
            <view class="title">
              <view>{{
                userType === "03"
                  ? "居民学习情况"
                  : userType === "02"
                  ? "居委下属商铺学习情况"
                  : "对应居委总分构成情况"
              }}</view>
              <view class="select">
                <uni-data-select
                  v-if="userType === '03'"
                  v-model="firstValue"
                  :localdata="selectList"
                  :clear="false"
                  @change="fetchCountryResidentData"
                ></uni-data-select>
                <uni-data-select
                  v-else-if="userType === '02'"
                  v-model="firstValue"
                  :localdata="selectList"
                  :clear="false"
                  @change="fetchCountryShopsData"
                ></uni-data-select>
                <uni-data-select
                  v-else
                  v-model="firstValue"
                  :localdata="selectList"
                  :clear="false"
                  @change="fetchCountryData"
                ></uni-data-select>
              </view>
            </view>
            <qiun-data-charts :ontouch="true" type="column" :opts="opts2" :chartData="chartData2" />
          </view>
        </view>
      </view>
    </view>
    <lingfeng-timepicker
      ref="timePop"
      :type="userType === '03' ? 'year-month' : 'quarter'"
      :defaultData="defaultData"
      @change="timeChange"
    ></lingfeng-timepicker>
  </view>
</template>

<script>
  import {
    towncommDist,
    towncountryDist,
    getTownCommDistRank,
    townDashboard,
    residentDashboard,
    commResident,
    countryResident,
    getCountryResidentRank,
    commShops,
    countryShops,
    getCountryShopsRank
  } from "@/api/beicai/index.js"
  export default {
    data() {
      return {
        content:
          "总分（100分）= 学习分（满分80分）+季度考试分（满分20）。学习分可通过视频学习、每月习题、每月回顾获得。季度考试有2次，取最高1次分值计入总分。",
        pageNum: 1,
        quarterDeptName: "",
        userType: localStorage.getItem("userType"),
        pageSize: 10,
        rankList: [],
        dashboardList: [],
        firstValue: null,
        selectList: [],
        total: 0,
        activeTab: 1,
        startTime: `${new Date().getFullYear()}-${
          "123".includes(new Date().getMonth() + 1)
            ? "01"
            : "456".includes(new Date().getMonth() + 1)
            ? "04"
            : "789".includes(new Date().getMonth() + 1)
            ? "07"
            : "10"
        }`,
        endTime: `${new Date().getFullYear()}-${
          "123".includes(new Date().getMonth() + 1)
            ? "03"
            : "456".includes(new Date().getMonth() + 1)
            ? "06"
            : "789".includes(new Date().getMonth() + 1)
            ? "09"
            : "12"
        }`,
        defaultData: {
          quarter: `${new Date().getFullYear()} ${
            "123".includes(new Date().getMonth() + 1)
              ? "一"
              : "456".includes(new Date().getMonth() + 1)
              ? "二"
              : "789".includes(new Date().getMonth() + 1)
              ? "三"
              : "四"
          }季度`,
          month: `${new Date().getFullYear()}-${new Date().getMonth() + 1 > 10 ? "" : 0}${
            new Date().getMonth() + 1
          }`
        },
        chartData1: {},
        chartData2: {},
        opts1: {
          color: ["#5c6fc4", "#f4ca5f"],
          padding: [15, 15, 0, 5],
          enableScroll: false,
          legend: {},
          xAxis: {
            disableGrid: true
          },
          yAxis: {
            showTitle: true,
            data: [
              {
                title: this.userType === "03" ? "人数" : "分数",
                min: 0,
                axisLineColor: "#5c6fc4",
                fontColor: "#5c6fc4",
                titleFontColor: "#5c6fc4"
              },
              {
                title: "时长(分钟)",
                min: 0,
                position: "right",
                axisLineColor: "#f4ca5f",
                fontColor: "#f4ca5f",
                titleFontColor: "#f4ca5f"
              }
            ]
          },
          extra: {
            column: {
              type: "group",
              width: 10,
              activeBgColor: "#000000",
              activeBgOpacity: 0.08,
              seriesGap: 5
            }
          }
        },
        opts2: {
          color: ["#5c6fc4", "#f4ca5f"],
          padding: [15, 15, 0, 5],
          legend: {},
          enableScroll: true,
          background: "white",
          xAxis: {
            disableGrid: true,
            rotateLabel: true,
            rotateAngle: 60,
            fontSize: 11,
            marginTop: 5,
            itemCount: 10,
            scrollShow: true
          },
          yAxis: {
            showTitle: true,
            data: [
              {
                title: this.userType === "03" ? "人数" : "分数",
                min: 0,
                axisLineColor: "#5c6fc4",
                fontColor: "#5c6fc4",
                titleFontColor: "#5c6fc4"
              },
              {
                title: "时长(分钟)",
                min: 0,
                position: "right",
                axisLineColor: "#f4ca5f",
                fontColor: "#f4ca5f",
                titleFontColor: "#f4ca5f"
              }
            ]
          },
          extra: {
            column: {
              type: "group",
              width: 10,
              activeBgColor: "#000000",
              activeBgOpacity: 0.08,
              seriesGap: 5
            }
          }
        }
      }
    },
    async onReady() {
      this.fetchDashboardData()
      this.fetchRankData("first")
    },
    onReachBottom() {
      if (this.pageNum * this.pageSize < this.total && this.activeTab === 1) {
        this.pageNum++
        this.fetchRankData("onReachBottom")
      }
    },
    methods: {
      async changeTab(index) {
        this.activeTab = index
        this.startTime = `${new Date().getFullYear()}-${
          "123".includes(new Date().getMonth() + 1)
            ? "01"
            : "456".includes(new Date().getMonth() + 1)
            ? "04"
            : "789".includes(new Date().getMonth() + 1)
            ? "07"
            : "10"
        }`
        this.endTime = `${new Date().getFullYear()}-${
          "123".includes(new Date().getMonth() + 1)
            ? "03"
            : "456".includes(new Date().getMonth() + 1)
            ? "06"
            : "789".includes(new Date().getMonth() + 1)
            ? "09"
            : "12"
        }`
        if (this.userType === "03") {
          this.defaultData.month = `${new Date().getFullYear()}-${
            new Date().getMonth() + 1 > 10 ? "" : 0
          }${new Date().getMonth() + 1}`
        } else {
          this.defaultData.quarter = `${new Date().getFullYear()} ${
            "123".includes(new Date().getMonth() + 1)
              ? "一"
              : "456".includes(new Date().getMonth() + 1)
              ? "二"
              : "789".includes(new Date().getMonth() + 1)
              ? "三"
              : "四"
          }季度`
        }
        if (this.activeTab === 1) {
          this.fetchRankData()
        }
        if (this.activeTab === 2) {
          if (this.userType === "03") {
            await this.fetchCommResidentData()
            await this.fetchCountryResidentData()
          } else if (this.userType === "02") {
            await this.fetchCommShopsData()
            await this.fetchCountryShopsData()
          } else {
            await this.fetchCommData()
            await this.fetchCountryData()
          }
        }
      },

      showPop() {
        this.$refs.timePop.show()
      },

      async timeChange(val) {
        if (this.userType === "03") {
          this.defaultData.month = val
        } else {
          this.defaultData.quarter = val[0]
        }
        this.startTime = val[1].slice(0, 7)
        this.endTime = val[2].slice(0, 7)
        if (this.activeTab === 1) {
          this.fetchRankData()
        }
        if (this.activeTab === 2) {
          if (this.userType === "03") {
            await this.fetchCommResidentData()
            await this.fetchCountryResidentData()
          } else if (this.userType === "02") {
            await this.fetchCommShopsData()
            await this.fetchCountryShopsData()
          } else {
            await this.fetchCommData()
            await this.fetchCountryData()
          }
        }
      },

      async fetchCommData() {
        const { data } = await towncommDist({
          queryTimeFrom: this.startTime,
          queryTimeTo: this.endTime
        })
        this.firstValue = data[0].dept_id
        this.selectList = data.map(item => {
          return {
            value: item.dept_id,
            text: item.dept_name
          }
        })
        let res = {
          categories: data.map(item => {
            return item.dept_name
          }),
          series: [
            {
              index: 0,
              name: "考试成绩",
              data: data.map(item => {
                return item.avg_paper_score
              })
            },
            {
              index: 1,
              name: "学习时长",
              data: data.map(item => {
                return Math.floor(item.total_study_duration / 60)
              })
            }
          ]
        }
        this.chartData1 = JSON.parse(JSON.stringify(res))
      },

      async fetchCountryData() {
        const { data } = await towncountryDist({
          queryTimeFrom: this.startTime,
          queryTimeTo: this.endTime,
          deptId: this.firstValue
        })
        let res = {
          categories: data.map(item => {
            return item.dept_name
          }),
          series: [
            {
              index: 0,
              name: "考试成绩",
              data: data.map(item => {
                return item.avg_paper_score
              })
            },
            {
              index: 1,
              name: "学习时长",
              data: data.map(item => {
                return Math.floor(item.total_study_duration / 60)
              })
            }
          ]
        }
        this.chartData2 = JSON.parse(JSON.stringify(res))
      },

      //居民社区学习情况
      async fetchCommResidentData() {
        const { data } = await commResident({
          queryTime: this.defaultData.month,
          userType: this.userType
        })
        this.firstValue = data[0].dept_id
        this.selectList = data.map(item => {
          return {
            value: item.dept_id,
            text: item.dept_name
          }
        })
        let res = {
          categories: data.map(item => {
            return item.dept_name
          }),
          series: [
            {
              index: 0,
              name: "学习人数",
              data: data.map(item => {
                return item.learners
              })
            },
            {
              index: 1,
              name: "学习时长",
              data: data.map(item => {
                return Math.floor(item.total_study_duration / 60)
              })
            }
          ]
        }
        this.chartData1 = JSON.parse(JSON.stringify(res))
      },

      //居民学习情况
      async fetchCountryResidentData() {
        const { data } = await countryResident({
          queryTime: this.defaultData.month,
          userType: this.userType,
          deptId: this.firstValue
        })
        let res = {
          categories: data.map(item => {
            return item.dept_name
          }),
          series: [
            {
              index: 0,
              name: "学习人数",
              data: data.map(item => {
                return item.learners
              })
            },
            {
              index: 1,
              name: "学习时长",
              data: data.map(item => {
                return Math.floor(item.total_study_duration / 60)
              })
            }
          ]
        }
        this.chartData2 = JSON.parse(JSON.stringify(res))
      },

      //社区下属商铺学习情况
      async fetchCommShopsData() {
        const { data } = await commShops({
          queryTimeFrom: this.startTime,
          queryTimeTo: this.endTime,
          userType: this.userType
        })
        this.selectList = await this.$getDict("business_type")
        this.firstValue = this.selectList[0].value
        let res = {
          categories: data.map(item => {
            return item.dept_name
          }),
          series: [
            {
              index: 0,
              name: "考试成绩",
              data: data.map(item => {
                return item.avg_paper_score
              })
            },
            {
              index: 1,
              name: "学习时长",
              data: data.map(item => {
                return Math.floor(item.total_study_duration / 60)
              })
            }
          ]
        }
        this.chartData1 = JSON.parse(JSON.stringify(res))
      },

      //居委下属商铺学习情况
      async fetchCountryShopsData() {
        const { data } = await countryShops({
          queryTimeFrom: this.startTime,
          queryTimeTo: this.endTime,
          userType: this.userType,
          businessType: this.firstValue
        })
        let res = {
          categories: data.map(item => {
            return item.user_name
          }),
          series: [
            {
              index: 0,
              name: "考试成绩",
              data: data.map(item => {
                return item.avg_paper_score
              })
            },
            {
              index: 1,
              name: "学习时长",
              data: data.map(item => {
                return Math.floor(item.total_study_duration / 60)
              })
            }
          ]
        }
        this.chartData2 = JSON.parse(JSON.stringify(res))
      },

      async fetchRankData(flag) {
        if (flag !== "onReachBottom") {
          this.pageNum = 1
        }
        if (this.userType === "03") {
          const { rows, total } = await getCountryResidentRank({
            pageNum: this.pageNum,
            pageSize: this.pageSize,
            queryTime: this.defaultData.month,
            userType: "03"
          })
          this.rankList = flag === "onReachBottom" ? this.rankList.concat(rows) : rows
          this.total = total
        } else if (this.userType === "02") {
          const { rows, total } = await getCountryShopsRank({
            pageNum: this.pageNum,
            pageSize: this.pageSize,
            queryTimeFrom: this.startTime,
            queryTimeTo: this.endTime,
            userType: "02"
          })
          this.rankList = flag === "onReachBottom" ? this.rankList.concat(rows) : rows
          if (flag === "first") {
            this.quarterDeptName = this.rankList[0].deptName
          }
          this.total = total
        } else {
          const { rows, total } = await getTownCommDistRank({
            pageNum: this.pageNum,
            pageSize: this.pageSize,
            queryTimeFrom: this.startTime,
            queryTimeTo: this.endTime
          })
          this.rankList = flag === "onReachBottom" ? this.rankList.concat(rows) : rows
          if (flag === "first") {
            this.quarterDeptName = this.rankList[0].deptName
          }
          this.total = total
        }
      },

      async fetchDashboardData() {
        if (this.userType === "03") {
          const { data } = await residentDashboard({ userType: "03" })
          this.dashboardList = data
        } else if (this.userType === "02") {
          const { data } = await residentDashboard({ userType: "02" })
          this.dashboardList = data
        } else {
          const { data } = await townDashboard()
          this.dashboardList = data
        }
      }
    }
  }
</script>

<style lang="scss" scoped>
  .count {
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: #f3f5ff;
    min-height: 100vh;
    .count-header {
      background-color: #fff;
      width: 95%;
      margin-top: 10px;
      padding: 10px;
      .title {
        font-weight: bold;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
      }
      .info {
        .top {
          display: flex;
          align-items: center;
          > view {
            flex: 1;
            text-align: center;
          }
        }
        .bottom {
          display: flex;
          align-items: center;
          > view {
            flex: 1;
            text-align: center;
          }
        }
      }
    }
    .count-body {
      background-color: #fff;
      width: 95%;
      margin-top: 20px;
      .tab {
        display: flex;
        align-items: center;
        color: #909192;
        font-weight: bold;
        font-size: 20px;
        .active {
          background-color: #bfdbfa;
          color: #1e4886;
        }
        > view {
          height: 60px;
          line-height: 60px;
          width: 50%;
          text-align: center;
          background-color: #f2f2f2;
        }
      }
      .content {
        .date {
          margin-top: 20px;
          .show-time {
            width: 65%;
            display: flex;
            align-items: center;
            margin: 0 auto;
            border: 2px solid #e5e7ed;
            padding: 5px 20px;
            font-weight: bold;
            color: #a6a7aa;
            font-size: 18px;
            position: relative;
            box-shadow: 0 0 10px 0 rgba(212, 212, 226, 0.8);
            :first-child {
              margin-right: 10px;
            }
            :last-child {
              position: absolute;
              right: 10px;
            }
          }
        }
        .list {
          margin-top: 10px;
          padding: 0 10px;
          min-height: 500px;
          .item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-bottom: 1px solid #e6e5e5;
            padding: 20px 0;
            .item-index {
              width: 3%;
              text-align: center;
              font-weight: bold;
            }
            .item-info {
              width: 94%;
              .info-top {
                display: flex;
                align-items: center;
                margin-bottom: 20px;
                font-size: 17px;
                :first-child {
                  font-weight: bold;
                }
                :last-child {
                  margin-left: 70px;
                }
              }
              .info-special {
                display: flex;
                align-items: center;
                justify-content: space-between;
                font-size: 17px;
                :first-child {
                  font-weight: bold;
                }
              }
              .info-bottom {
                display: flex;
                align-items: center;
                justify-content: space-between;
                > view {
                  font-size: 14px;
                }
              }
            }
          }
        }
        .charts-box {
          width: 100%;
          height: 300px;
          margin-top: 30px;

          .title {
            display: flex;
            align-items: center;
            justify-content: space-around;
            font-weight: bold;
            margin-bottom: 10px;
            .show-time {
              width: 40%;
              display: flex;
              align-items: center;
              border: 2px solid #e5e7ed;
              padding: 5px 0px;
              font-weight: bold;
              color: #a6a7aa;
              position: relative;
              box-shadow: 0 0 10px 0 rgba(212, 212, 226, 0.8);
              :first-child {
                margin-right: 10px;
                margin-left: 5px;
              }
              :last-child {
                position: absolute;
                right: 5px;
              }
            }
            .select {
              width: 45%;
            }
          }
        }
      }
    }
  }
</style>

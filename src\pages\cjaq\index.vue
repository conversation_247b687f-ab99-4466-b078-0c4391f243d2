<!--
 * @Description: 工会首页
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-01-15 11:02:45
 * @LastEditTime: 2025-06-17 10:40:50
-->
<template>
  <view class="con">
    <h1 class="title"> 工会促进安全生产学习平台 </h1>
    <view class="rule">
      <view class="rule-content">
        安全生产是关系到国家和人民群众生命财产的安全和人民群众的切身利益的大事;安全生产管理最根本的目的是保护人民的生命和健康，安全生产是对企业的最根本要求，安全管理是每个管理人员必须遵守的行为准则，这也是安全生产管理的重要内容。安全工作是一项常抓不懈的主题，是生产的保证，也是员工效益的最大体现。“安全”是我们年年讲、月月讲、时时讲、人人讲、大会讲、小会讲班前班后讲的共同主题，“安全”对于我们每一个人来说都是重要的。
      </view>
    </view>

    <view class="buttonList" style="margin-top: 100rpx">
      <button class="btn" style="background-color: #f59a23" @click="handleLearn">学习</button>
      <!-- <button class="btn" style="background-color: #75affd" @click="handleExam"> 考试 </button> -->
    </view>
  </view>
</template>

<script>
  import { getCjaqExamInfo } from "@/api/exam/base.js"
  export default {
    data() {
      return {
        showMore: false,
        examData: {}
      }
    },
    created() {
      this.fetchData()
    },
    methods: {
      handleExam() {
        if (!this.examData || !this.examData.baseId) {
          return this.$modal.msgError("暂无可用考试")
        }
        uni.navigateTo({
          url: `/pages/cjaq/exam?baseId=${this.examData.baseId}&arrangeId=${this.examData.arrangeId}`
        })
      },
      handleLearn() {
        uni.navigateTo({ url: "/pages/competition/startLearn" })
      },
      handleMore() {
        this.showMore = !this.showMore
      },
      async fetchData() {
        const { data } = await getCjaqExamInfo({
          taskId: this.$route.query.taskId ? this.$route.query.taskId : undefined
        })
        this.examData = data && data[0]
      }
    }
  }
</script>

<style lang="scss" scoped>
  .rule {
    width: 100%;
    /*  margin: 12rpx auto; */
    line-height: 46rpx;
    font-size: 16px;
    position: relative;
  }
  .rule-time {
    text-align: center;
  }
  .rule-title {
    text-align: center;
    font-size: 16px;
    line-height: 72rpx;
    padding-bottom: 10px;
    color: #fff;
    transform: translateY(10px);
    z-index: 1;
    position: relative;
    border-radius: 10px;
    font-weight: bold;
    background: linear-gradient(to right, #09a39b, #0c6fb3);
  }
  .rule-content {
    background: #fff;
    border-radius: 10px;
    z-index: 2;
    text-indent: 60rpx;
    position: relative;
    padding: 16px 16px;
    box-shadow: 0 0 10rpx rgba(0, 0, 0, 0.1);
    font-size: 32rpx;
    line-height: 56rpx;

    text-align: justify;
  }
  .sub-text {
    font-size: 17px;
  }
  .con {
    min-height: 100vh;
    background: url(/static/images/competition/logbg_3.png) no-repeat;
    background-size: 100% 100%;
    padding: 20rpx;
  }
  .content {
    line-height: 26px;
    font-size: 16px;
    margin-top: 20rpx;
  }
  .title {
    margin: 40px 0;
    text-align: center;
    font-weight: bold;
  }
  .close-date {
    font-weight: bold;
  }
  .more {
    color: #4098f7;
    text-decoration: underline;
    margin-left: 8rpx;
    font-weight: bold;
  }

  .sub-title {
    color: #d01f25;
    font-size: 18px;
    font-weight: bold;
  }

  .nor-title {
    color: #d01f25;
    font-size: 16px;
    font-weight: bold;
    margin-right: 8rpx;
  }
  .buttonList {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-evenly;
    .btn {
      width: 45%;
      color: #fff;
      font-size: 38rpx;
      margin-top: 50rpx;
      box-shadow: 0 0 10rpx rgba(0, 0, 0, 0.1);
    }
  }
</style>

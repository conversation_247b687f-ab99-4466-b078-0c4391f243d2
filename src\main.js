/*
 * @Description: main.js
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-06-25 17:06:57
 * @LastEditTime: 2025-06-09 14:43:59
 */
import "uno.css"
import Vue from "vue"
import App from "./App"
import { router, RouterMount } from "./router/index"
import store from "./store" // store
import plugins from "./plugins" // plugins
import "../src/utils/permission" // permission
import { getDict } from "@/utils/dict"
import uView from "uview-ui"
import "@/static/scss/index.scss"
import Videojs from "video.js"
import "video.js/dist/video-js.css"
import "videojs-landscape-fullscreen"

Vue.prototype.$video = Videojs

Vue.use(uView)
Vue.use(router)
Vue.use(plugins)

if (store.state.tenant.domainName === "unep") {
  Vue.mixin({
    onShow() {
      /*  uni.setNavigationBarTitle({
        //uni-app 的修改title接口
        title: "防灾减灾知识竞赛-2222"
      }) */
      //以下为H5平台差异写法
      // #ifdef H5
      document.title = "防灾减灾知识竞赛"
      // #endif
    }
  })
}

// 分页组件
import zbDropDownMenu from "@/components/zb-dropdown-menu/zb-dropdown-menu.vue"
import zbDropDownItem from "@/components/zb-dropdown-item/zb-dropdown-item.vue"
import oDivider from "@/components/o-divider/o-divider.vue"
import customTabs from "@/components/custom-tabs/custom-tabs.vue"
import customTabPane from "@/components/custom-tab-pane/custom-tab-pane.vue"
import navigationBar from "@/components/navigationBar/index"

// 全局组件挂载
Vue.component("zb-dropdown-menu", zbDropDownMenu)
Vue.component("zb-dropdown-item", zbDropDownItem)
Vue.component("o-divider", oDivider)
Vue.component("custom-tabs", customTabs)
Vue.component("custom-tab-pane", customTabPane)
Vue.component("navigationBar", navigationBar)

// 全局方法挂载
Vue.prototype.$getDict = getDict
Vue.config.productionTip = false
Vue.prototype.$store = store

App.mpType = "app"
const app = new Vue({
  ...App
})

//v1.3.5起 H5端 你应该去除原有的app.$mount();使用路由自带的渲染方式
// #ifdef H5
RouterMount(app, router, "#app")
// #endif

// #ifndef H5
app.$mount() //为了兼容小程序及app端必须这样写才有效果
// #endif

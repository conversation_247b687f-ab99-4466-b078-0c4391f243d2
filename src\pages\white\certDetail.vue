<!--
 * @Description: 
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-11-25 15:19:31
 * @LastEditTime: 2024-12-16 15:16:03
-->
<template>
  <view class="min-h-screen bg-white">
    <navigationBar v-if="this.$route.query.showNavBar" path="back" :showPersonal="false" />
    <view class="p-4">
      <!-- 顶部标题 -->
      <view class="text-2xl font-bold mb-6 text-[#2196f3]">我的证书</view>

      <!-- 课程部分 -->
      <view class="mb-6" v-if="courseList.length">
        <view class="flex items-center">
          <view class="bg-[#2196f3] h-12 flex items-center rounded-tl-lg rounded-tr-lg">
            <view class="w-8 h-8 rounded-full bg-white flex items-center justify-center mx-4">
              <text class="text-[#2196f3] font-bold">1</text>
            </view>
            <text class="text-white font-bold mr-8">课程</text>
          </view>
        </view>

        <view class="border-t-2 border-[#2196f3] pt-4">
          <view v-for="(course, index) in courseList" :key="index" class="mb-6">
            <!-- 课程标题 -->
            <view class="text-lg font-bold mb-4 text-[#333]"
              >{{ index + 1 }}. {{ course.courseName }}</view
            >

            <!-- 课程内容表格样式布局 -->
            <view class="flex w-full">
              <view
                v-for="(field, idx) in courseFields"
                :key="idx"
                class="flex-1 min-w-0 text-center"
              >
                <view class="text-base text-gray-600 mb-2">{{ field.label }}</view>
                <view class="text-lg font-bold text-[#333] break-words px-2">
                  {{ formatFieldValue(course[field.key], field.type) }}
                </view>
              </view>
            </view>

            <view class="border-b border-gray-200 mt-4"></view>
          </view>
        </view>
      </view>

      <!-- 考试部分 -->
      <view v-if="examList.length">
        <view class="flex items-center">
          <view class="bg-[#2196f3] h-12 flex items-center rounded-tl-lg rounded-tr-lg">
            <view class="w-8 h-8 rounded-full bg-white flex items-center justify-center mx-4">
              <text class="text-[#2196f3] font-bold">2</text>
            </view>
            <text class="text-white font-bold mr-8">考试</text>
          </view>
        </view>

        <view class="border-t-2 border-[#2196f3] pt-4">
          <view v-for="(exam, index) in examList" :key="index" class="mb-6">
            <!-- 考试标题 -->
            <view class="text-lg font-bold mb-4 text-[#333]"
              >{{ index + 1 }}. {{ exam.examName }}</view
            >

            <!-- 考试内容表格样式布局 -->
            <view class="flex w-full">
              <view
                v-for="(field, idx) in examFields"
                :key="idx"
                class="flex-1 min-w-0 text-center"
              >
                <view class="text-base text-gray-600 mb-2">{{ field.label }}</view>
                <view class="text-lg font-bold text-[#333] break-words px-2">
                  {{ formatFieldValue(exam[field.key], field.type) }}
                </view>
              </view>
            </view>

            <view class="border-b border-gray-200 mt-4"></view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
  import { getTrainingCourseList, getTrainingExamList } from "@/api/trainingTask"

  export default {
    data() {
      return {
        courseList: [],
        examList: [],
        loading: false,
        courseFields: [
          { label: "学习时长", key: "deltaDuration", type: "duration" },
          { label: "学习时间", key: "startTime", type: "date" }
        ],
        examFields: [
          { label: "得分", key: "userPaperScore", type: "number" },
          { label: "合格分", key: "lowestScore", type: "number" },
          { label: "考试时间", key: "submitTime", type: "date" },
          { label: "考试用时", key: "examTime", type: "duration" }
        ]
      }
    },

    created() {
      this.fetchCourseList()
      this.fetchExamList()
    },

    methods: {
      formatFieldValue(value, type) {
        if (!value) return "-"
        switch (type) {
          case "duration":
            return `${value}秒`
          case "date":
            return value
          case "number":
            return value.toString()
          default:
            return value
        }
      },

      async fetchCourseList() {
        try {
          this.loading = true
          const res = await getTrainingCourseList({
            pageNum: 1,
            pageSize: 999,
            courseId: this.$route.query.courseId,
            examId: this.$route.query.examId,
            taskId: this.$route.query.taskId,
            userId: this.$route.query.userId
          })
          if (res.code === 200) {
            this.courseList = res.rows
          }
        } finally {
          this.loading = false
        }
      },

      async fetchExamList() {
        try {
          this.loading = true
          const res = await getTrainingExamList({
            pageNum: 1,
            pageSize: 999,
            courseId: this.$route.query.courseId,
            examId: this.$route.query.examId,
            taskId: this.$route.query.taskId,
            userId: this.$route.query.userId
          })
          if (res.code === 200) {
            this.examList = res.rows
          }
        } finally {
          this.loading = false
        }
      }
    }
  }
</script>

<!--
 * @Description: 学习统计页面
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-12-16 10:15:31
 * @LastEditTime: 2025-01-08 15:13:54
-->

<template>
  <view class="min-h-screen bg-gray-50">
    <navigationBar path="back" />

    <!-- 累计数据 -->
    <view class="bg-white p-4 mb-3">
      <view class="flex items-center mb-3">
        <view class="i-material-symbols-analytics text-blue-500 text-lg"></view>
        <text class="ml-2 text-gray-800 font-medium">累计数据</text>
      </view>
      <view class="flex gap-4">
        <view class="bg-gray-100 rounded-lg p-4 flex-1">
          <view class="text-base font-medium text-gray-700 mb-2">累计学习时长</view>
          <view class="flex items-baseline">
            <text class="text-3xl font-bold text-gray-800 mr-1">{{ totalTime }}</text>
            <text class="text-sm text-gray-500">分钟</text>
          </view>
        </view>
        <view class="bg-gray-100 rounded-lg p-4 flex-1">
          <view class="text-base font-medium text-gray-700 mb-2">累计完成课程</view>
          <view class="flex items-baseline">
            <text class="text-3xl font-bold text-gray-800 mr-1">{{ totalCourse }}</text>
            <text class="text-sm text-gray-500">节</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 学习数据 -->
    <view class="bg-white p-4">
      <view class="flex items-center justify-between mb-4">
        <view class="flex items-center">
          <view class="i-material-symbols-monitoring text-blue-500 text-lg"></view>
          <text class="ml-2 text-gray-800 font-medium">学习数据</text>
        </view>
        <view class="flex bg-gray-100 rounded-full p-1">
          <view
            v-for="(item, index) in timeRanges"
            :key="index"
            class="px-4 py-1 rounded-full text-sm cursor-pointer"
            :class="currentRange === item.value ? 'bg-white text-blue-500 shadow' : 'text-gray-500'"
            @click="changeTimeRange(item.value)"
          >
            {{ item.label }}
          </view>
        </view>
      </view>

      <!-- 当前时段数据 -->
      <view class="grid grid-cols-2 gap-8 mb-2">
        <view>
          <view class="text-base font-medium text-gray-700 mb-2">学习时长</view>
          <view class="flex items-baseline">
            <text class="text-3xl font-bold text-gray-800 mr-1">{{ currentTime }}</text>
            <text class="text-sm text-gray-500">分钟</text>
          </view>
        </view>
        <view>
          <view class="text-base font-medium text-gray-700 mb-2">完成课程</view>
          <view class="flex items-baseline">
            <text class="text-3xl font-bold text-gray-800 mr-1">{{ currentCourse }}</text>
            <text class="text-sm text-gray-500">节</text>
          </view>
        </view>
      </view>

      <!-- 时间范围显示 -->
      <view class="text-sm text-gray-400 mb-6">{{ formatTimeRangeText }}</view>

      <!-- 图表区域 -->
      <qiun-data-charts
        v-if="chartData.series[0].data.length > 0"
        type="column"
        :opts="chartOpts"
        :chartData="chartData"
        class="w-full h-60"
        canvas2d
        background="none"
      />
    </view>
  </view>
</template>

<script>
  import { getStudyTime, getCourseCount, getStudyData, getCourseData } from "@/api/statistics"

  export default {
    data() {
      return {
        timeRanges: [
          { label: "日", value: 0 },
          { label: "周", value: 1 },
          { label: "月", value: 2 }
        ],
        currentRange: 0,
        totalTime: 0,
        totalCourse: 0,
        currentTime: 0,
        currentCourse: 0,
        chartData: {
          categories: [],
          series: [
            {
              name: "学习时长",
              data: []
            }
          ]
        },
        chartOpts: {
          color: ["#7daef3"],
          padding: [15, 15, 0, 5],
          enableScroll: false,
          legend: {
            show: false
          },
          xAxis: {
            disableGrid: true,
            itemCount: 7,
            fontSize: 12,
            color: "#999"
          },
          yAxis: {
            gridType: "dash",
            dashLength: 2,
            data: [
              {
                min: 0,
                fontSize: 12,
                color: "#999"
              }
            ],
            splitNumber: 4
          },
          extra: {
            column: {
              width: 20,
              activeBgColor: "#000000",
              activeBgOpacity: 0.08
            }
          },
          dataLabel: false,
          animation: true
        }
      }
    },

    computed: {
      formatTimeRangeText() {
        const now = new Date()
        const month = now.getMonth() + 1
        const date = now.getDate()

        switch (this.currentRange) {
          case 0:
            return `${month}月${date}日（今日）`
          case 1:
            const weekStart = new Date(now)
            weekStart.setDate(now.getDate() - now.getDay() + 1) // 获取本周一
            const weekEnd = new Date(now)
            weekEnd.setDate(weekStart.getDate() + 6) // 获取本周日
            return `${weekStart.getMonth() + 1}月${weekStart.getDate()}日~${
              weekEnd.getMonth() + 1
            }月${weekEnd.getDate()}日（本周）`
          case 2:
            return `${month}月（本月）`
          default:
            return ""
        }
      }
    },

    async onLoad() {
      await this.initData()
    },

    methods: {
      // 秒转分钟，保留整数
      secondsToMinutes(seconds) {
        if (seconds && seconds < 60) return 1
        return Math.floor(seconds / 60)
      },

      // 格式化图表数据
      formatChartData(data) {
        if (!data) return { categories: [], values: [] }

        // 根据不同的时间维度处理数据
        let sortedEntries = []
        const currentDate = new Date()
        const currentMonth = currentDate.getMonth() + 1
        
        switch (this.currentRange) {
          case 0: // 日维度，格式：12-10
            const currentDay = currentDate.getDate()
            
            sortedEntries = Object.entries(data).sort(([dateA], [dateB]) => {
              const [monthA, dayA] = dateA.split("-").map(Number)
              const [monthB, dayB] = dateB.split("-").map(Number)
              
              // 计算与当前日期的差距，用于判断是否是跨年的日期
              let adjustedMonthA = monthA
              let adjustedMonthB = monthB
              
              // 如果日期小于（当前日期-7天）对应的月份和日期，说明是下一年的日期
              if (monthA < currentMonth || (monthA === currentMonth && dayA < currentDay - 7)) {
                adjustedMonthA += 12
              }
              if (monthB < currentMonth || (monthB === currentMonth && dayB < currentDay - 7)) {
                adjustedMonthB += 12
              }
              
              // 先比较月份，月份相同再比较日期
              return adjustedMonthA === adjustedMonthB ? dayA - dayB : adjustedMonthA - adjustedMonthB
            })
            break
          case 1: // 周维度，格式：202445（年份+周数）
            sortedEntries = Object.entries(data).sort(([a], [b]) => Number(a) - Number(b))
            break
          case 2: // 月维度，格式：YYYY-MM
            const currentYear = currentDate.getFullYear()
            
            sortedEntries = Object.entries(data).sort(([dateA], [dateB]) => {
              // 解析年份和月份
              const [yearA, monthA] = dateA.split("-").map(Number)
              const [yearB, monthB] = dateB.split("-").map(Number)
              
              // 计算与当前日期的差距
              const diffA = (yearA - currentYear) * 12 + monthA - currentMonth
              const diffB = (yearB - currentYear) * 12 + monthB - currentMonth
              
              return diffA - diffB
            })
            break
        }

        return {
          categories: sortedEntries.map(([key]) => this.formatLabel(key)),
          values: sortedEntries.map(([, value]) => Math.max(0, this.secondsToMinutes(value))) // 负值显示为0
        }
      },

      // 格式化显示标签
      formatLabel(key) {
        switch (this.currentRange) {
          case 0: // 日维度
            const [dayMonthPart, dayNum] = key.split("-")
            return `${Number(dayMonthPart)}/${Number(dayNum)}`
          case 1: // 周维度
            return `第${key.slice(-2)}周`
          case 2: // 月维度，格式：YYYY-MM
            const yearMonthParts = key.split("-")
            if (yearMonthParts.length !== 2) return "无效月份"
            const monthNum = parseInt(yearMonthParts[1], 10)
            return isNaN(monthNum) ? "无效月份" : `${monthNum}月`
          default:
            return key
        }
      },

      async initData() {
        try {
          // 获取累计数据
          const [timeRes, courseRes] = await Promise.all([getStudyTime(), getCourseCount()])
          this.totalTime = this.secondsToMinutes(timeRes.data || 0)
          this.totalCourse = courseRes.data || 0

          // 获取当前时段数据
          await this.fetchRangeData()
        } catch (error) {
          console.error("初始化数据失败:", error)
        }
      },

      // 获取指定日期范围的开始和结束日期
      getDateRange() {
        const now = new Date()
        const year = now.getFullYear()
        const month = String(now.getMonth() + 1).padStart(2, "0")
        const day = String(now.getDate()).padStart(2, "0")

        let fromDate, toDate

        switch (this.currentRange) {
          case 0: // 日
            // 获取7天前的日期
            const sevenDaysAgo = new Date(now)
            sevenDaysAgo.setDate(now.getDate() - 6)
            fromDate = `${sevenDaysAgo.getFullYear()}-${String(
              sevenDaysAgo.getMonth() + 1
            ).padStart(2, "0")}-${String(sevenDaysAgo.getDate()).padStart(2, "0")}`
            toDate = `${year}-${month}-${day}`
            break
          case 1: // 周
            // 获取7周前的日期
            const sevenWeeksAgo = new Date(now)
            sevenWeeksAgo.setDate(now.getDate() - 42)
            fromDate = `${sevenWeeksAgo.getFullYear()}-${String(
              sevenWeeksAgo.getMonth() + 1
            ).padStart(2, "0")}-${String(sevenWeeksAgo.getDate()).padStart(2, "0")}`
            toDate = `${year}-${month}-${day}`
            break
          case 2: // 月
            // 获取7个月前的日期
            const sevenMonthsAgo = new Date(now)
            sevenMonthsAgo.setMonth(now.getMonth() - 6)
            fromDate = `${sevenMonthsAgo.getFullYear()}-${String(
              sevenMonthsAgo.getMonth() + 1
            ).padStart(2, "0")}-01`
            toDate = `${year}-${month}-${day}`
            break
        }

        return { fromDate, toDate }
      },

      async fetchRangeData() {
        try {
          const { fromDate, toDate } = this.getDateRange()
          const params = {
            range: this.currentRange,
            queryTimeFrom: fromDate,
            queryTimeTo: toDate
          }

          const [studyRes, courseRes] = await Promise.all([
            getStudyData(params),
            getCourseData(params)
          ])

          const studyData = studyRes.data || {}
          const courseData = courseRes.data || {}

          // 获取最新的时间段的数据
          const latestTimeKey = Object.keys(studyData).sort().pop()
          const latestTime = latestTimeKey ? studyData[latestTimeKey] : 0
          this.currentTime = this.secondsToMinutes(Math.max(0, latestTime)) // 负值显示为0

          // 获取最新的课程完成数
          const latestCourseKey = Object.keys(courseData).sort().pop()
          this.currentCourse = latestCourseKey ? courseData[latestCourseKey] || 0 : 0

          // 更新图表数据
          const { categories, values } = this.formatChartData(studyData)

          // 使用 nextTick 确保数据更新后再渲染图表
          await this.$nextTick()
          this.chartData = {
            categories,
            series: [
              {
                name: "学习时长",
                data: values
              }
            ]
          }
        } catch (error) {
          console.error("获取数据失败:", error)
        }
      },

      changeTimeRange(range) {
        this.currentRange = range
        this.fetchRangeData()
      }
    }
  }
</script>

<!--
 * @Description: 学习轨迹页面
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-12-18 14:37:29
 * @LastEditTime: 2025-01-02 16:33:43
-->

<template>
  <view class="min-h-screen bg-gray-50">
    <navigationBar path="/pages/mine/index" />

    <!-- 空状态 -->
    <xw-empty :isShow="!trackList.length" text="暂无学习轨迹" textColor="#777777"></xw-empty>

    <!-- 轨迹列表 -->
    <view v-show="trackList.length" class="px-4 py-2">
      <view v-for="(item, index) in trackList" :key="item.studyLogId" class="flex mb-6">
        <!-- 左侧时间线 -->
        <view class="relative flex-none w-12 mr-4">
          <!-- 上方连接线 -->
          <view
            v-if="index !== 0"
            class="absolute top-0 left-1/2 h-4 w-0.5 bg-gray-200 -translate-x-1/2"
          ></view>

          <!-- 图标容器 -->
          <view
            :class="[
              'absolute top-4 left-1/2 w-10 h-10 rounded-full -translate-x-1/2 flex items-center justify-center overflow-hidden z-10',
              TASK_ITEM_LEARNED_TYPE[item.learnedType].color
            ]"
          >
            <view
              :class="[TASK_ITEM_LEARNED_TYPE[item.learnedType].iconClass, 'text-xl text-white']"
            ></view>
          </view>

          <!-- 下方连接线 -->
          <view
            v-if="index !== trackList.length - 1"
            class="absolute top-12 left-1/2 h-full w-0.5 bg-gray-200 -translate-x-1/2"
          ></view>
        </view>

        <!-- 右侧内容卡片 -->
        <view class="flex-1 bg-white rounded-lg shadow-sm p-4">
          <!-- 时间 -->
          <view class="text-gray-400 text-sm mb-2">{{ item.createTime }}</view>

          <!-- 内容 -->
          <view class="text-gray-700">
            <text>
              {{ TASK_ITEM_LEARNED_TYPE[item.learnedType].actionText
              }}{{ TASK_ITEM_LEARNED_TYPE[item.learnedType].typeName }}《{{
                item[`${[TASK_ITEM_LEARNED_TYPE[item.learnedType].field]}Name`]
              }}》
              <text v-if="item.learnedType === '1'">，考试分数{{ item.learningProcess }}分 </text>
            </text>
          </view>

          <!-- 学习时长 -->
          <view class="flex items-center mt-2 text-sm text-gray-500">
            <view class="flex items-center">
              <view class="i-material-symbols:schedule mr-1"></view>
              <text>{{ formatDuration(item) }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
  import { studyLogList } from "@/api/trainingTask"
  import xwEmpty from "@/components/xw-empty/xw-empty"
  import { TASK_ITEM_LEARNED_TYPE } from "@/utils/constant"

  export default {
    components: {
      xwEmpty
    },

    data() {
      return {
        TASK_ITEM_LEARNED_TYPE,
        trackList: [],
        pageNum: 1,
        pageSize: 10,
        total: 0
      }
    },

    onLoad() {
      this.getTrackList()
    },

    onReachBottom() {
      if (this.trackList.length < this.total) {
        this.pageNum++
        this.getTrackList()
      }
    },

    methods: {
      // 获取学习轨迹列表
      async getTrackList() {
        try {
          const params = {
            pageNum: this.pageNum,
            pageSize: this.pageSize
          }
          const { rows, total } = await studyLogList(params)
          this.total = total
          if (this.pageNum === 1) {
            this.trackList = rows
          } else {
            this.trackList = [...this.trackList, ...rows]
          }
        } catch (error) {
          console.error("获取学习轨迹列表失败:", error)
        }
      },

      // 格式化持续时间
      formatDuration(item) {
        const seconds = item.lastDeltaDuration
        let result = ""
        if (!seconds) {
          result = "0秒"
        } else {
          const minutes = Math.floor(seconds / 60)
          const remainingSeconds = seconds % 60

          if (minutes > 0) result += `${minutes}分钟`
          if (remainingSeconds > 0) result += `${remainingSeconds}秒`
        }

        // 根据类型添加前缀
        return `${TASK_ITEM_LEARNED_TYPE[item.learnedType].actionText}${result}`
      }
    }
  }
</script>

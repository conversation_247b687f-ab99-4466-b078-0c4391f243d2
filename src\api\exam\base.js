/*
 * @Description:
 * @Author: <PERSON><PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-05-25 15:59:19
 * @LastEditTime: 2025-05-26 09:15:57
 */
import request from "@/utils/request"

// 查询考试详细
export function getExam(examId) {
  return request({
    url: "/exam/base/" + examId,
    method: "get"
  })
}

// 查询考试安排/记录详细
export function getArrange(arrangeId) {
  return request({
    url: "/exam/arrange/" + arrangeId,
    method: "get"
  })
}

// 北蔡防灾减灾首页获取考试baseId与arrangeId
export function getTownExamInfo(params) {
  return request({
    url: "/exam/arrange/examList",
    method: "get",
    params
  })
}

// 北蔡防灾减灾首页获取回顾页内容
export function getTownHistoryList() {
  return request({
    url: "/exam/paper-answer/historyList",
    method: "get"
  })
}

// 工会项目根据taskId获取考试examId与arrangeId
export function getCjaqExamInfo(params) {
  return request({
    url: "/exam/arrange/examByTask",
    method: "get",
    params
  })
}

// 西电项目根据baseId查每日考试的arrangeId
export function getXdExamInfo(params) {
  return request({
    url: "/exam/arrange/dailyExams",
    method: "get",
    params
  })
}

// 获取动态的考试 baseId 和 arrangeId
export function getExamRange(params) {
  return request({
    url: "/exam/base/range",
    method: "get",
    params
  })
}

// 获取答卷记录状态
export function getAnswerRecord(arrangeId) {
  return request({
    url: `/exam/paper-answer/record/${arrangeId}`,
    method: "get"
  })
}

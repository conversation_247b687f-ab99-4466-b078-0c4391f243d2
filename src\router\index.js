/*
 * @Description: router.js
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-06-20 13:39:31
 * @LastEditTime: 2025-06-19 09:55:20
 */

import { RouterMount, createRouter } from "uni-simple-router"
import { autoLogin } from "@/api/system/user"
import { setToken, getToken, removeToken } from "@/utils/auth"
import store from "@/store"
import { getSubdomain } from "@/utils/common"
import { unepTenantList } from "@/utils/constant"
import { scanLogin } from "@/api/system/login"

const whiteListPages = ["/pages/white/certDetail"]
let isAlreadyAutoLogin = false
let isAlreadySetToken = false
const router = createRouter({
  platform: "h5",
  routes: [...ROUTES]
})
// 全局路由前置守卫
router.beforeEach(async (to, from, next) => {
  const subDomain = getSubdomain(false)
  if (whiteListPages.includes(to.path)) {
    next()
  }
  if (subDomain && !store.state.tenant.domainName) {
    await store.dispatch("queryTenantInfo")
  }
  if (to.path === "/pages/mine/exam2/index") {
    next("/pages/mine/exam/index?showNavBar=false")
  }
  // 512长宁防灾减灾租户调查问卷页面单独特殊处理
  if (
    store.state.tenant.domainName === "fzjz" &&
    to.path === "/pages/mine/my-questionnaire/doQuestionnaire/index"
  ) {
    const res = await scanLogin()
    if (res.code === 200) {
      setToken(res.data.access_token)
    }
    next()
  }
  // 处理app跳转
  if (window.location.href.indexOf("appId") >= 0 && !isAlreadyAutoLogin) {
    // 获取当前页面URL
    let queryString = window.location.href.split("?")[1]
    let urlNoParams = window.location.href.split("?")[0]
    // 创建URLSearchParams对象
    let urlParams = new URLSearchParams(queryString)

    // http://localhost:9090/#/pages/course/index?username=黄少云&tenantName=喜达储运&appId=6645cbae-27d5-11ec-805b-525400b8bd2d&domainName=xida
    // 获取参数值
    let username = urlParams.get("username")
    let appId = urlParams.get("appId")
    let tenantName = urlParams.get("tenantName")
    let domainName = urlParams.get("domainName")
    let tenantCode = urlParams.get("tenantCode")
    let ehsOrgId = urlParams.get("ehsOrgId")
    let ehsOrgName = urlParams.get("ehsOrgName")
    let parentEhsOrgId = urlParams.get("parentEhsOrgId")
    let parentEhsOrgName = urlParams.get("parentEhsOrgName")

    let courseId = urlParams.get("courseId")
    // 是否显示退出登录按钮
    store.commit("SET_LOGBTN")
    const res = await autoLogin({
      username,
      tenantName,
      appId,
      domainName,
      tenantCode,
      ehsOrgId,
      ehsOrgName,
      parentEhsOrgId,
      parentEhsOrgName
    })
    if (res.code === 200) {
      isAlreadyAutoLogin = true
      // 截取成功储存token，下面重定向时会在请求时再获取token添加到头部的
      setToken(res.data.access_token)
      if (to.path === "/pages/mine/exam2/index") {
        next("/pages/mine/exam/index?showNavBar=false")
      }
      // else {
      //   // 重定向地址去除url当中一大长串的token
      //   setTimeout(() => {
      //     window.location.href = urlNoParams
      //   }, 1000)
      // }
    }
  }
  // url中携带token进入
  else if (window.location.href.indexOf("token") >= 0 && !isAlreadySetToken) {
    // 如果token存在
    // 如果url中包含token   split？分割成数组，取第二个
    const queryString = window.location.href.split("?")[1]
    const urlParams = new URLSearchParams(queryString)
    // 获取参数值
    const token = urlParams.get("token")
    setToken(token)
    isAlreadySetToken = true
    next()
  }
  if (getToken()) {
    if (to.path === "/pages/login" || to.path === "/pages/town/placeCommitteeLogin") {
      // token存在 并且 domainname是unep 并且是登录页
      if (unepTenantList.includes(store.state.tenant.domainName)) {
        next("/pages/competition/index")
      } else if (
        ["zhzg", "yuyuan"].includes(store.state.tenant.domainName) &&
        window.location.href.indexOf("userType") >= 0
      ) {
        const queryString = window.location.href.split("?")[1]
        const urlParams = new URLSearchParams(queryString)
        const userType = urlParams.get("userType")
        removeToken()
        next({
          path: "/pages/login",
          query: {
            userType
          }
        })
      } else if (store.state.tenant.domainName === "jxyj") {
        const queryString = window.location.href.split("?")[1]
        const urlParams = new URLSearchParams(queryString)
        const baseId = urlParams.get("baseId")
        const arrangeId = urlParams.get("arrangeId")
        removeToken()
        next({
          path: "/pages/login",
          query: {
            baseId,
            arrangeId
          }
        })
      } else {
        next("/pages/index")
      }
    } else {
      if (store.state.user.roles.length === 0) {
        if (!whiteListPages.includes(to.path)) {
          store.dispatch("GetInfo")
        }
        next()
      }
      next()
    }
  }
  next()
})

export { router, RouterMount }

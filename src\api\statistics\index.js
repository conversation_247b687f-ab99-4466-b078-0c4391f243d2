import request from "@/utils/request"
// 累计时长/course/study-log/getStudyTime
// get方式（仅学习页面今日学习时长才需要传taskId和onlyToday=true）
export function getStudyTime(params) {
  return request({
    url: "/course/study-log/getStudyTime",
    method: "get",
    params
  })
}

// 累加完成课程数/course/study-log/getCourseCount
// get方式   无参
export function getCourseCount() {
  return request({
    url: "/course/study-log/getCourseCount",
    method: "get"
  })
}

// 学习时长/course/study-log/getStudyData
// get方式
// 传参：range            0-按日  1-按周  2-按月    number类型
// queryTimeFrom         yyyy-MM格式
// queryTimeTo             yyyy-MM格式
export function getStudyData(params) {
  return request({
    url: "/course/study-log/getStudyData",
    method: "get",
    params
  })
}

// 完成课程数/course/study-log/getCourseData
// get方式
// 传参：range                0-按日  1-按周  2-按月    number类型
// queryTimeFrom         yyyy-MM格式
// queryTimeTo             yyyy-MM格式
export function getCourseData(params) {
  return request({
    url: "/course/study-log/getCourseData",
    method: "get",
    params
  })
}

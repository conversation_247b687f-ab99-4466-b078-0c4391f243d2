<!--
 * @Description: 轮播图组件
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-06-25 17:10:02
 * @LastEditTime: 2024-12-23 14:44:54
-->
<template>
  <swiper
    class="content-bar-swiper"
    :indicator-dots="true"
    :autoplay="true"
    :interval="3000"
    :duration="500"
    :circular="true"
    :indicator-color="'rgba(255, 255, 255, 0.3)'"
    :indicator-active-color="'#ffffff'"
  >
    <swiper-item v-for="bannerItem in list" :key="bannerItem.id" class="swiper-item">
      <image 
        class="swiper-img" 
        :src="bannerItem.img" 
        mode="aspectFill"
        :webp="true"
        :fade-show="false"
        :draggable="false"
        @load="handleImageLoad"
      />
    </swiper-item>
  </swiper>
</template>

<script>
export default {
  props: {
    list: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    handleImageLoad(e) {
      // 图片加载完成后的处理
      console.log('Image loaded:', e)
    }
  }
}
</script>

<style lang="scss" scoped>
.content-bar-swiper {
  width: 100%;
  height: 170px;
  padding: 0 8px;
  box-sizing: border-box;

  .swiper-item {
    width: 100%;
    height: 100%;
    border-radius: 8px;
    overflow: hidden;
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000;
    will-change: transform;
  }

  .swiper-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 8px;
    transform: translateZ(0);
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

:deep(.uni-swiper-dots-horizontal) {
  bottom: 10px;
}

:deep(.uni-swiper-dot) {
  width: 6px;
  height: 6px;
  border-radius: 3px;
  margin: 0 4px;
  transition: all 0.3s;
  
  &.uni-swiper-dot-active {
    width: 12px;
  }
}
</style>

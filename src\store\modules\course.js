/*
 * @Description:
 * @Author: <PERSON><PERSON>
 * @LastEditors: <PERSON><PERSON>
 * @Date: 2023-04-23 15:54:29
 * @LastEditTime: 2023-05-25 11:30:33
 */
import { listCourse } from "@/api/course"

const course = {
  state: {
    catalogueId: 0,
    courseList: [],
    pageSize: 8,
    pageNum: 1,
    dataTotal: 0,
    courseName: ""
  },

  mutations: {
    SET_COURSE_LIST: (state, list) => {
      state.courseList = list
    },

    ADD_PAGE_NUM: state => {
      state.pageNum++
    },

    SET_CATALOGUE_ID: (state, id) => {
      state.catalogueId = id
    },

    RESET_PAGE_NUM: state => {
      state.pageNum = 1
    },

    SET_COURSE_NAME: (state, name) => {
      state.courseName = name
    }
  },

  actions: {
    async fetchCourseData({ state, commit }, data) {
      let queryData = {
        pageNum: state.pageNum,
        pageSize: state.pageSize,
        catalogueId: data.id,
        courseName: data.name
      }
      const { rows, total } = await listCourse(queryData)
      state.dataTotal = total
      commit("SET_COURSE_LIST", rows)
    }
  }
}

export default course

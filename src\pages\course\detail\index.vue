<!--
 * @Description: 课程详情页面
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-06-25 17:09:38
 * @LastEditTime: 2025-03-05 11:11:15
-->
<template>
  <view
    class="detail-container"
    :class="{ 'bg-fzjz': domainName === 'fzjz' }"
    :style="
      domainName === 'fzjz'
        ? `background-image: url('${assetsPrefixUrl}coursePlay.jpg') !important;`
        : ''
    "
    v-if="passageList"
  >
    <view class="top-container" v-if="domainName === 'fzjz'">
      <view class="iconfont icon-fanhui icon" @click="back"></view>
      <view class="course-name">{{ courseInfo.courseName }}</view>
    </view>
    <navigationBar path="back" :showPersonal="false" v-if="showNavBar" />
    <view v-if="courseInfo.courseStatus && courseInfo.courseStatus !== '2'" class="player-tip">
      <view class="player-tip-inner">
        <view class="player-tip-info">该课程已下架，请联系管理员</view>
      </view>
    </view>
    <view v-else-if="currentPlayItem" class="video-display-wrap">
      <!-- 文档预览 -->
      <view v-if="isWordCourse && currentPlayItem.fileUrl" class="document-preview">
        <view class="document-container">
          <web-view v-show="!docError" :src="currentWordUrl"></web-view>
          <!-- 加载状态 -->
          <view v-if="docLoading" class="doc-loading">
            <text class="loading-text">文档加载中...</text>
          </view>
          <!-- 错误状态 -->
          <view v-if="docError" class="doc-error">
            <text class="error-text">文档加载失败，请刷新重试</text>
            <button class="retry-btn" @tap="retryLoadDocument">重试</button>
          </view>
        </view>
      </view>
      <!-- 视频播放器 -->
      <view v-else class="video-js" ref="videoRef" style="width: 100%; height: 100%"></view>
    </view>
    <detail-body
      :courseInfo="courseInfo"
      :passageList="passageList"
      :currentPlayItem="currentPlayItem"
      @playItemChange="handlePlayItemChange"
      @refreshCourseData="fetchCourseData"
    />
    <AnswerDialog ref="answerDialogRef" @done="answerQuestionDone" />
  </view>
</template>

<script>
  import AnswerDialog from "./components/answerDialog.vue"
  import { listPassage } from "@/api/course/passage.js"
  import detailBody from "./components/detail-body"
  import { getToken } from "@/utils/auth"
  import { isAndroidOrIOS } from "@/utils/common"
  import {
    courseEditView,
    getCourseInfo,
    updateCourseProgress,
    getCourseStats,
    clearCourseProgress
  } from "@/api/course"
  import { getTaskInfoById } from "@/api/trainingTask"
  import { mapGetters } from "vuex"
  import { assetsPrefixUrl, COURSE_TYPE } from "@/utils/constant.js"
  import { Base64 } from "js-base64"

  export default {
    components: {
      detailBody,
      AnswerDialog
    },
    provide() {
      return {
        finalLearnedStatus: () => this.finalLearnedStatus
      }
    },
    data() {
      return {
        assetsPrefixUrl,
        passageList: [],
        courseInfo: {},
        currentPlayItem: {},
        passageIndex: 0,
        videoIndex: 0,
        lastPlayTime: null,
        progressUpdateTimer: null,
        lastProgress: 0,
        frequencyTime: 10000,
        finalLearnedStatus: false,
        player: null,
        showNavBar: true,
        taskId: null,
        // 文档预览相关状态
        docLoading: true,
        docError: false,
        startTime: Date.now(),
        updateTimer: null,
        isPaused: false,
        pausedTime: 0,
        verifySuccess: false
      }
    },
    async onLoad(option) {
      const queryCourseId = option.id ? option.id : option.courseId
      if (option.courseId) this.showNavBar = false

      this.taskId = option.taskId
      // 只有存在 taskId 时才进行人脸识别
      if (this.taskId) {
        if (option.verifySuccess === undefined) {
          const res = await getTaskInfoById(this.taskId)
          if (res.code === 200 && res.data.faceCapture === "1") {
            uni.redirectTo({
              url: `/pages/face-verify/index?taskId=${
                this.taskId
              }&subTaskType=0&subTaskId=${queryCourseId}&subTaskName=${decodeURIComponent(
                option.courseName
              )}`
            })
            return
          }
        } else {
          this.verifySuccess = option.verifySuccess === "true"
        }
      }

      if (this.domainName === "fzjz") {
        this.showNavBar = false
      }

      await this.fetchCourseData(queryCourseId)
      await this.fetchPassageData()
      await this.getCourseProgress()
      if (this.isWordCourse) {
        this.docLoading = true
        this.docError = false
        this.startTime = Date.now()
        setTimeout(() => {
          this.docLoading = false
          this.startDocumentTimer()
        }, 1000)
      } else {
        this.initVideoJS()
      }
      courseEditView({ courseId: this.courseInfo.courseId })
    },
    beforeDestroy() {
      // 组件销毁前清理资源
      clearInterval(this.progressUpdateTimer)
      clearInterval(this.updateTimer)
      if (this.player) {
        try {
          this.player.dispose()
        } catch (error) {
          console.error("销毁播放器失败:", error)
        }
        this.player = null
      }
    },

    deactivated() {
      clearInterval(this.progressUpdateTimer)
    },

    computed: {
      ...mapGetters({
        domainName: "domainName"
      }),
      videoSrcUrl() {
        if (!this.currentPlayItem?.vodUrl) return ""
        const token = getToken()
        if (!token) return ""
        // 检查 URL 是否已经包含参数
        const separator = this.currentPlayItem.vodUrl.includes("?") ? "&" : "?"
        return `${this.currentPlayItem.vodUrl}${separator}token=${token}`
      },
      // 判断是否为文档课程
      isWordCourse() {
        return this.courseInfo?.courseType?.includes(COURSE_TYPE.WORD_COURSE)
      },
      // 文档预览URL
      currentWordUrl() {
        if (!this.currentPlayItem?.fileUrl) return ""
        const fileUrl = this.currentPlayItem.fileUrl
        const encodedUrl = encodeURIComponent(Base64.encode(fileUrl))
        return `${process.env.VUE_APP_KK_URL}/onlinePreview?url=${encodedUrl}`
      }
    },

    methods: {
      isAndroidOrIOS,
      getToken,
      // 获取课程信息
      async fetchCourseData(id) {
        const { data } = await getCourseInfo(id || this.courseInfo.courseId)
        this.courseInfo = data
        this.rateValue = this.courseInfo.courseGrade
        this.questionList = this.courseInfo.courseQuestionLinkList
      },
      // 获取章节
      async fetchPassageData() {
        const queryData = {
          courseId: this.courseInfo.courseId,
          pageSize: 999
        }
        const { rows } = await listPassage(queryData)
        this.passageList = rows
        this.currentPlayItem = this.passageList[0].videoFileList[0]
      },
      // 章节切换
      async handlePlayItemChange({ item, index, index2 }) {
        try {
          if (this.currentPlayItem === item) return

          // 清理定时器
          clearInterval(this.progressUpdateTimer)
          clearInterval(this.updateTimer)

          // 更新当前播放项
          this.currentPlayItem = item
          this.passageIndex = index
          this.videoIndex = index2

          // 等待 DOM 更新
          await this.$nextTick()

          // 获取学习进度
          await this.getCourseProgress()

          // 根据课程类型初始化不同的播放器
          if (this.isWordCourse) {
            this.docLoading = true
            this.docError = false
            this.startTime = Date.now()
            setTimeout(() => {
              this.docLoading = false
              this.startDocumentTimer()
            }, 1000)
          } else {
            if (this.player) {
              this.player.src(this.videoSrcUrl)
            }
          }
        } catch (error) {
          console.error("handlePlayItemChange error", error)
        }
      },

      // 视频播放
      startPlay() {
        this.progressUpdateTimer = setInterval(() => {
          this.courseProgressUpdate()
        }, this.frequencyTime)
      },

      // 视频暂停事件
      pauseTimer() {
        clearInterval(this.progressUpdateTimer)
        this.courseProgressUpdate()
      },

      // 获取视频播放进度
      async getCourseProgress() {
        const params = this.taskId ? { taskId: this.taskId } : undefined
        const res = await getCourseStats(this.courseInfo.courseId, params)
        if (res.code === 200) {
          if (!res.data || res.data.length === 0) return
          const statusItem = res.data.find(
            item => item.passageId === this.currentPlayItem?.passageId
          )
          this.currentPlayItem = Object.assign(this.currentPlayItem, statusItem)
          this.finalLearnedStatus = res.data.every(item => {
            return item.learnedStatus === "2"
          })
          res.data.forEach(item => {
            if (item.passageId === this.currentPlayItem?.passageId) {
              this.lastPlayTime = item.mediaProgress
              // 只要原先播放的进度和视频总时长相差小于2秒，则清空原有进度(从头播放)
              if (Math.abs(item.mediaDuration - item.mediaProgress) < 2) {
                this.lastPlayTime = 0
              }
              // 跳转至上次记录的播放位置
              if (this.player && this.lastPlayTime) {
                this.player?.currentTime(this.lastPlayTime)
              }
            }
          })
        }
      },

      // 更新视频播放进度
      async courseProgressUpdate() {
        if (this.player?.currentTime() !== this.lastProgress) {
          this.lastProgress = this.player?.currentTime()
          // 构建请求数据
          const requestData = {
            courseId: this.currentPlayItem.courseId,
            passageId: this.currentPlayItem?.passageId,
            learnFileType: "0",
            deltaDuration: this.frequencyTime / 1000,
            mediaProgress: this.player?.currentTime(),
            mediaDuration: this.player?.duration()
          }
          // 只在有taskId时添加taskId字段
          if (this.taskId) {
            requestData.taskId = this.taskId
          }
          await updateCourseProgress(requestData)
        }
      },

      // 视频结束播放
      async endVideo() {
        // 1.更新视频播放进度
        await this.courseProgressUpdate()

        // 2.如果这个章节下还有下一个视频，则播放下一个视频
        if (this.videoIndex < this.passageList[this.passageIndex].videoFileList.length - 1) {
          this.videoIndex++
          this.currentPlayItem = this.passageList[this.passageIndex].videoFileList[this.videoIndex]
          this.player.src(this.videoSrcUrl)
          await this.getCourseProgress()
        }
        // 3.如果该章节下没有下一个视频
        else {
          // 3.1 如果这个章节下还有下一个章节，则继续播放下一个章节下的第一个视频
          if (this.passageIndex < this.passageList.length - 1) {
            this.passageIndex++
            this.videoIndex = 0
            this.currentPlayItem =
              this.passageList[this.passageIndex].videoFileList[this.videoIndex]
            this.player.src(this.videoSrcUrl)
            await this.getCourseProgress()
          }
          // 3.没有下个章节，则只更新各个章节的学习状态
          else {
            const params = taskId ? { taskId: this.taskId } : undefined
            const res = await getCourseStats(this.courseInfo.courseId, params)
            this.finalLearnedStatus = res.data.every(item => {
              return item.learnedStatus === "2"
            })
          }
        }
      },
      // 是否允许拖动进度条
      registerDrug() {
        if (this.courseInfo.progressBarDrag === "1") {
          //启用拖动
          document.querySelector(".vjs-progress-control").style.pointerEvents = "auto"
        } else {
          //禁用拖动
          document.querySelector(".vjs-progress-control").style.pointerEvents = "none"
        }
      },

      // 初始化视频-videoJS
      async initVideoJS() {
        try {
          // 先确保清理旧的播放器实例
          if (this.player) {
            this.player.dispose()
            this.player = null
          }

          // 清理旧的video元素
          if (this.$refs.videoRef?.$el) {
            this.$refs.videoRef.$el.innerHTML = ""
          }

          // 创建新的video元素
          const videoEl = document.createElement("video")
          videoEl.id = `myVideo${this.currentPlayItem.passageId}`
          videoEl.className = "video-js vjs-big-play-centered"
          videoEl.setAttribute("controls", "")
          videoEl.setAttribute("preload", "auto")
          videoEl.setAttribute("playsinline", false)
          videoEl.setAttribute("webkit-playsinline", true)
          videoEl.setAttribute("x5-video-player-type", "h5") //安卓 声明启用同层H5播放器 可以在video上面加东西

          const source = document.createElement("source")
          source.src = this.videoSrcUrl

          //根据流地址的后缀设置好播放类型

          if (source.src.indexOf(".mp4") !== -1) {
            //mp4类型
            source.type = "video/mp4"
          } else if (source.src.indexOf(".m3u8") !== -1) {
            //hls类型
            source.type = "application/x-mpegURL"
          } else if (source.src.indexOf(".flv") !== -1) {
            //flv类型
            source.type = "video/flv"
          } else {
            //rtmp类型
            source.type = "rtmp/hls"
          }

          // "https://training-voc.bkehs.cn/asset/f710a5aaa77df419430b9cf16f24edb7/play_video/index.m3u8?token=eyJhbGciOiJIUzUxMiJ9.eyJ1c2VyX2lkIjoxLCJmcm9tLXNvdXJjZSI6bnVsbCwidXNlcl9rZXkiOiIxMjJkNDE1Yi1kNzRhLTQxMTAtODRjOC03MDA5NmE4ZDY3NzIiLCJ1c2VybmFtZSI6ImFkbWluIn0.y_yGF2p1mNjsls4-E6t3RV19DE0CjO4-k75vHmlb7q-fpCaVbuhVKMMzJXPMs7M_vXFg6xnvx7Hn7qszkgFSBQ"
          videoEl.appendChild(source)
          this.$refs.videoRef?.$el.appendChild(videoEl)
          let that = this
          this.player = this.$video(
            videoEl,
            {
              poster: this.courseInfo.courseImage,
              // playbackRates: [0.5, 1.0, 1.5, 2.0], //播放速度
              autoDisable: true,
              preload: "none", //auto - 当页面加载后载入整个视频 meta - 当页面加载后只载入元数据 none - 当页面加载后不载入视频
              language: "zh-CN",
              fluid: true, // 自适应宽高
              muted: false, // 是否静音
              aspectRatio: "16:9", // 将播放器置于流畅模式，并在计算播放器的动态大小时使用该值。值应该代表一个比例 - 用冒号分隔的两个数字（例如"16:9"或"4:3"）
              controls: true, // 是否拥有控制条 【默认true】,如果设为false ,那么只能通过api进行控制了。也就是说界面上不会出现任何控制按钮
              autoplay: true, // 如果true,浏览器准备好时开始回放。 autoplay: "muted", // //自动播放属性,muted:静音播放
              loop: false, // 导致视频一结束就重新开始。 视频播放结束后，是否循环播放
              techOrder: ["html5"], //播放顺序
              screenshot: true,
              controlBar: {
                volumePanel: {
                  //声音样式
                  inline: false // 不使用水平方式
                },
                timeDivider: true, // 时间分割线
                durationDisplay: true, // 总时间
                progressControl: true, // 进度条
                remainingTimeDisplay: true, //当前以播放时间
                fullscreenToggle: true, //全屏按钮
                pictureInPictureToggle: false //画中画
              }
            },
            function () {
              // this.on("loadstart", function () {
              //   console.log("开始请求数据")
              // })
              // this.on("progress", function () {
              //   console.log("正在请求数据")
              // })
              // this.on("loadedmetadata", function () {
              //   console.log("获取资源长度完成")
              // })
              // this.on("canplaythrough", function () {
              //   console.log("视频源数据加载完成")
              // })
              // this.on("waiting", function () {
              //   console.log("等待数据")
              // })
              // this.on("error", function () {
              //   console.log("请求数据时遇到错误")
              // })
              // this.on("stalled", function () {
              //   console.log("网速失速")
              // })
              this.on("play", () => that.startPlay())
              this.on("pause", () => that.pauseTimer())
              this.on("ended", () => that.endVideo())
              // this.on("fullscreenchange", e => {
              //   console.log("最大化")
              // })
              this.on("timeupdate", () => that.onTimeUpdate())
            }
          )

          if (this.lastPlayTime) {
            // 如果有视频进度，直接跳转到进度位置
            this.player?.currentTime(this.lastPlayTime)
          }
          // 安卓全屏bug处理
          if (this.isAndroidOrIOS() === "android") {
            this.player.landscapeFullscreen()
          }
          // 判断视频是否可以拖拽
          this.registerDrug()
        } catch (error) {
          console.error("初始化视频播放器失败:", error)
        }
      },
      back() {
        uni.navigateBack({
          delta: 1
        })
      },
      onTimeUpdate(e) {
        // 该视频状态已完成 || 该视频下没有问答题 || 视频答题弹窗已经打开时 不执行下方代码
        if (
          this.currentPlayItem.learnedStatus === "2" ||
          !this.currentPlayItem.courseQuestionLinkList ||
          this.currentPlayItem.courseQuestionLinkList.length === 0 ||
          this.$refs["answerDialogRef"]?.visible
        )
          return
        // 该视频下有问答题时
        const currentTime = this.isWordCourse ? e.target.currentTime : this.player?.currentTime()
        if (!currentTime) return
        for (let i = 0; i < this.currentPlayItem.courseQuestionLinkList.length; i++) {
          const qsItem = this.currentPlayItem.courseQuestionLinkList[i]
          // 如果已经答过该题，跳过
          if (qsItem.isAlreadyAnswer) continue
          if (Math.floor(currentTime) === qsItem.embeddingPoint) {
            this.currentPlayItem.courseQuestionLinkList[i].isAlreadyAnswer = true
            this.openAnswerDialog(qsItem)
            break
          }
        }
      },
      openAnswerDialog(qsItem) {
        if (this.isWordCourse) {
          this.isPaused = true
          this.pausedTime = Date.now()
        } else {
          // 检查视频是否处于全屏状态
          if (this.player.isFullscreen_) {
            this.player.exitFullscreen()
          }
          this.player?.pause()
        }
        this.$refs["answerDialogRef"].openDialog(qsItem)
      },
      answerQuestionDone(flag, needReset = false) {
        if (needReset) {
          // 清空学习进度
          const requestData = {
            taskId: this.taskId,
            courseId: this.courseInfo.courseId,
            passageId: this.currentPlayItem?.passageId
          }
          clearCourseProgress(requestData)

          // 如果是视频课程，将视频进度调整到0
          if (!this.isWordCourse && this.player) {
            this.player.currentTime(0)
          }
        }

        if (this.isWordCourse) {
          // 继续计时，更新开始时间以抵消暂停的时间
          this.isPaused = false
          const pauseDuration = Date.now() - this.pausedTime
          this.startTime += pauseDuration
          this.pausedTime = 0
        } else {
          this.player?.play()
        }
      },
      retryLoadDocument() {
        this.docError = false
        this.docLoading = true
        // 重新加载文档
        this.startDocumentTimer()
      },

      startDocumentTimer() {
        // 清除可能存在的定时器
        clearInterval(this.progressUpdateTimer)
        clearInterval(this.updateTimer)

        // 添加每秒调用 onTimeUpdate 的定时器，用于检查是否需要答题
        this.updateTimer = setInterval(() => {
          if (!this.isPaused) {
            const pauseDuration = this.pausedTime ? Date.now() - this.pausedTime : 0
            const currentTime = Math.floor((Date.now() - this.startTime - pauseDuration) / 1000)
            this.onTimeUpdate({ target: { currentTime } })
          }
        }, 1000)

        // 每10秒更新一次学习进度
        this.progressUpdateTimer = setInterval(async () => {
          if (this.isPaused) return

          // 文档类型的学习进度更新
          const requestData = {
            courseId: this.courseInfo.courseId,
            passageId: this.currentPlayItem.passageId,
            learnFileType: "0",
            deltaDuration: this.frequencyTime / 1000,
            mediaDuration: this.currentPlayItem.passageDuration || 0
          }
          // 只在有taskId时添加taskId字段
          if (this.taskId) {
            requestData.taskId = this.taskId
          }
          try {
            await updateCourseProgress(requestData)
          } catch (error) {
            console.error("更新学习进度失败:", error)
          }
        }, this.frequencyTime)

        // 记录开始时间
        this.startTime = Date.now()
        this.isPaused = false
        this.pausedTime = 0
      }
    },
    watch: {
      "currentPlayItem.fileUrl": {
        handler(newVal) {
          if (this.isWordCourse && newVal) {
            this.docLoading = true
            this.docError = false
            // 重置开始时间
            this.startTime = Date.now()
          }
        },
        immediate: true
      }
    }
  }
</script>

<style scoped lang="scss">
  .bg-fzjz {
    background-repeat: no-repeat;
    background-size: cover;
    background-position: top;
    padding-top: 5%;
    .top-container {
      position: relative;
      padding-bottom: 20rpx;
      .icon-fanhui {
        position: absolute;
        left: 25rpx;
        top: 8rpx;
        font-size: 40rpx;
        color: #fff;
      }
      .course-name {
        margin: 0 auto;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #fff;
        font-size: 38rpx;
        text-align: center;
      }
    }
  }
  .player-tip {
    height: 400rpx;
    background-image: url("/static/images/video/vipPlayerbg.jpg");
    background-repeat: no-repeat;
    background-size: cover;
    position: relative;

    .player-tip-inner {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      position: absolute;
      top: 45%;
      left: 0;
      right: 0;
      width: 100%;
      color: #fff;

      .player-tip-info {
        margin-bottom: 22px;
        font-size: 17px;
        text-align: center;
      }
    }
  }

  .video-display-wrap {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    overflow: hidden;
    background: #000;

    .video-js {
      width: 100%;
      height: auto;
      aspect-ratio: 16/9;
    }
  }
  ::v-deep .vjs-button > .vjs-icon-placeholder:before {
    line-height: 1;
  }

  ::v-deep .video-js .vjs-mute-control {
    width: 3em;
  }

  // 添加以下播放器控制栏样式优化代码
  ::v-deep .video-js {
    .vjs-control-bar {
      background-color: rgba(43, 51, 63, 0.7);
      height: 40px;
      display: flex;
      align-items: center;
      padding: 0 10px;
    }

    // 隐藏不需要的控件
    .vjs-picture-in-picture-control,
    .vjs-audio-button,
    .vjs-chapters-button,
    .vjs-descriptions-button,
    .vjs-subs-caps-button,
    .vjs-audio-button,
    .vjs-playback-rate,
    .vjs-live-control,
    .vjs-seek-to-live-control,
    .vjs-custom-control-spacer {
      display: none !important;
    }

    // 统一所有控制按钮的基础样式
    .vjs-control {
      height: 40px;
      width: 40px;
      margin: 0;
      padding: 0;
      background: none;
      border: none;
      outline: none;
      display: flex;
      align-items: center;
      justify-content: center;

      &:focus {
        outline: none;
        box-shadow: none;
      }
    }

    .vjs-button {
      > .vjs-icon-placeholder:before {
        font-size: 18px;
        line-height: 40px;
        color: rgba(255, 255, 255, 0.9);
      }

      &:hover {
        > .vjs-icon-placeholder:before {
          color: #40acea;
        }
      }
    }

    // 进度条样式
    .vjs-progress-control {
      position: relative;
      height: 40px;
      margin: 0;
      padding: 0;
      flex: auto;
      min-width: 4em;
      display: flex;
      align-items: center;

      .vjs-progress-holder {
        height: 3px;
        margin: 0;
        background-color: rgba(255, 255, 255, 0.3);
        transition: height 0.1s;

        &:hover {
          height: 5px;
        }
      }

      .vjs-play-progress {
        background-color: #40acea;

        &:before {
          display: none;
        }
      }

      .vjs-time-tooltip {
        background-color: rgba(43, 51, 63, 0.9);
        padding: 2px 5px;
        font-size: 12px;
      }
    }

    // 音量控制
    .vjs-volume-panel {
      display: flex;
      align-items: center;

      .vjs-mute-control {
        width: 40px;
        height: 40px;
      }

      .vjs-volume-control {
        width: 0;
        height: 40px;
        margin: 0;
        padding: 0;
        opacity: 0;
        transition: all 0.1s;

        .vjs-volume-bar {
          height: 3px;
          background: rgba(255, 255, 255, 0.3);
          margin: 18.5px 8px;
        }

        .vjs-volume-level {
          background-color: #40acea;
        }
      }

      &:hover {
        .vjs-volume-control {
          width: 60px;
          opacity: 1;
        }
      }
    }

    // 时间显示
    .vjs-time-control {
      display: flex;
      align-items: center;
      padding: 0 4px;
      font-size: 13px;
      font-family: Arial, sans-serif;
      color: rgba(255, 255, 255, 0.9);
      min-width: auto;
      line-height: 40px;
    }

    .vjs-current-time {
      padding-left: 8px;
    }

    .vjs-time-divider {
      padding: 0 3px;
      line-height: 40px;
    }

    .vjs-duration {
      padding-right: 8px;
    }

    // 全屏按钮
    .vjs-fullscreen-control {
      width: 40px;
    }

    // 隐藏大播放按钮
    .vjs-big-play-button {
      display: none;
    }

    // 自定义播放按钮图标大小
    .vjs-play-control,
    .vjs-mute-control,
    .vjs-volume-control,
    .vjs-fullscreen-control {
      .vjs-icon-placeholder:before {
        font-size: 22px;
      }
    }
  }

  .document-preview {
    width: 100%;
    height: 400rpx;
    position: relative;
    background: #fff;
    border-radius: 8rpx;
    box-shadow: 0 2rpx 12rpx 0 rgba(0, 0, 0, 0.1);

    .document-container {
      width: 100%;
      height: 100%;
      position: relative;
      overflow: hidden;

      web-view {
        width: 100%;
        height: 100%;
        border-radius: 8rpx;
      }

      .doc-loading {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 16rpx;
        background: rgba(255, 255, 255, 0.9);
        padding: 40rpx;
        border-radius: 8rpx;

        .loading-text {
          color: #666;
          font-size: 28rpx;
        }
      }

      .doc-error {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 24rpx;
        background: #fff;
        padding: 48rpx;
        border-radius: 8rpx;
        box-shadow: 0 2rpx 12rpx 0 rgba(0, 0, 0, 0.1);

        .error-text {
          color: #333;
          font-size: 28rpx;
        }

        .retry-btn {
          padding: 16rpx 32rpx;
          background: #40acea;
          color: #fff;
          border-radius: 8rpx;
          font-size: 28rpx;
        }
      }
    }
  }
</style>

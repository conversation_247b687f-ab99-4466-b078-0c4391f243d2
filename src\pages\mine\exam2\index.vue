<template>
  <view class="exam">
    <view class="exam-dropdown">
      <zb-dropdown-menu style="width: 100%">
        <zb-dropdown-item
          name="one"
          :options="examStatusList"
          v-model="examStatus"
          @change="fetchExamList"
        ></zb-dropdown-item>
      </zb-dropdown-menu>
    </view>
    <view class="exam-header">
      {{ $store.state.user.nickName }}同学，您有{{ dataTotal }}个考试项目待完成,加油！
    </view>
    <view class="exam-list" v-for="item in examList" :key="item.arrangeId">
      <view class="exam-list-name">{{ item.baseName }}</view>
      <view class="exam-list-state">
        状态：
        <text>{{ item.examStatusDesc }}</text>
      </view>
      <view class="exam-list-time">
        考试时间：
        <text>{{ item.startTime }} - {{ item.endTime }}</text>
      </view>
      <view class="is-pass" v-if="item.examStatus === '2'">
        <view v-if="item.isPass == '1'" class="iconfont icon-yitongguo icon"></view>
        <view v-else class="iconfont icon-weitongguo icon"></view>
      </view>
      <view class="exam-list-button">
        <view>考试</view>
        <button @click="jumpTo(item)"> 进入考场 </button>
      </view>
    </view>
    <xw-empty :isShow="examList.length === 0" text="暂无考试" textColor="#777777"></xw-empty>
  </view>
</template>

<script>
  import { getNeedExamList } from "@/api/exam/arrange.js"
  import { isInTimeRange } from "@/utils/common"
  export default {
    onLoad() {
      this.fetchExamList()
    },

    onReachBottom() {
      if (this.examList.length !== this.dataTotal) {
        this.pageNum += 1
        this.fetchExamList("onReachBottom")
      }
    },

    data() {
      return {
        examList: [],
        dataTotal: 0,
        pageNum: 1,
        pageSize: 10,
        examStatus: "",
        examStatusList: [
          {
            text: "全部",
            value: ""
          },
          {
            text: "未开始",
            value: 0
          },
          {
            text: "进行中",
            value: 1
          },
          {
            text: "已结束",
            value: 2
          }
        ]
      }
    },

    methods: {
      async fetchExamList(flag) {
        if (flag !== "onReachBottom") {
          this.pageNum = 1
        }
        let queryData = {
          pageSize: this.pageSize,
          pageNum: this.pageNum,
          examStatus: this.examStatus
        }
        const { rows, total } = await getNeedExamList(queryData)
        this.dataTotal = total
        this.examList = flag === "onReachBottom" ? this.examList.concat(rows) : rows
      },

      jumpTo(item) {
        if (!isInTimeRange(item.startTime, item.endTime)) {
          return uni.showToast({
            title: "当前时间不在考试时间范围内",
            icon: "error"
          })
        }
        uni.navigateTo({
          url: `/pages/mine/exam2/prepare2/index?baseId=${item.baseId}&arrangeId=${item.arrangeId}`
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
  .exam {
    display: flex;
    align-items: center;
    flex-direction: column;

    .exam-dropdown {
      width: 100%;
    }
    .exam-header {
      margin-top: 10px;
      margin-bottom: 10px;
    }
    .exam-list {
      position: relative;
      width: 100%;
      background-color: #fff;
      margin-bottom: 10px;
      padding: 20px 20px 10px 20px;

      .exam-list-name {
        // font-weight: 550;
        margin-bottom: 10px;
        font-size: 17px;
        color: #333333;
        font-weight: bold;
      }

      .exam-list-state {
        margin-bottom: 10px;
        color: #7f7f7f;
        > text {
          color: #535353;
        }
      }

      .exam-list-time {
        margin-bottom: 10px;
        color: #7f7f7f;
        font-size: 13px;
        > text {
          color: #535353;
        }
      }

      .is-pass {
        position: absolute;
        right: 60rpx;
        top: 30rpx;
        width: 100rpx;
        height: 100rpx;
        .iconfont {
          width: 100%;
          height: 100%;
          font-size: 120rpx;
        }
        .icon-yitongguo {
          color: #5ac3b5;
        }

        .icon-weitongguo {
          color: #e34d65;
        }
      }

      .exam-list-button {
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-top: 1px solid #f2f2f2;
        padding-top: 10px;
        :first-child {
          width: 65%;
          margin-left: 5px;
        }

        > button {
          width: 100px;
          font-size: 10px;
          background-color: #ed9d32;
          color: white;
        }
      }
    }
  }
</style>

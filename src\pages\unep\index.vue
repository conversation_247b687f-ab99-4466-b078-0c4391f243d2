<template>
  <view class="con">
    <view class="rule">
      <view class="rule-title">活动介绍</view>
      <view class="rule-content">
        2024年11月9日是第33个全国消防日，本月是“全国消防宣传月”为大力提升全民消防安全素质，普及消防安全知识，五角场街道开展
        2024年“全国消防安全宣传月”系列活动，营造浓厚的消防安全宣传氛围，特别推出“五角场街
        道线上消防知识达人竞赛”活动。
      </view>
    </view>
    <view class="rule">
      <view class="rule-title">活动规则</view>
      <view class="rule-content">
        活动由视频学习和知识测试两部分组成，
        综合总分100分。视频学习:每学习观看完1个视频，可积3分，最多可积45分;知识测试:随机抽取30道题目，总分55分，限时30分钟，活动期间可随时参与测试,测试机会为3次，以最优成绩作
        为最终记录，测试分数相同时，测试用时较少者
        优先。两部分分数相加，前100名获得本次活动“消防知识达人”称号,并获得“达人礼包”1份。
      </view>
    </view>
    <view class="rule">
      <view class="rule-title">活动时间</view>
      <view class="rule-content" style="text-indent: 0">
        <view class="rule-time">
          {{ dayjs(startTime).format("YYYY年MM月DD日 HH:mm") }} -
          {{ dayjs(endTime).format("YYYY年MM月DD日 HH:mm") }}
        </view>
      </view>
    </view>

    <view class="buttonList">
      <button class="btn-learn" @click="handleLearn">开始学习</button>
      <button class="btn-exam" @click="handleExam">开始测试</button>
    </view>
  </view>
</template>

<script>
  import { mapGetters } from "vuex"
  import dayjs from "dayjs"
  import { assetsPrefixUrl } from "@/utils/constant.js"

  export default {
    data() {
      return {
        assetsPrefixUrl,
        showMore: false
      }
    },
    computed: {
      ...mapGetters({
        domainName: "domainName",
        startTime: "startTime",
        endTime: "endTime"
      })
    },
    methods: {
      dayjs,
      handleExam() {
        uni.navigateTo({ url: "/pages/competition/exam" })
      },
      handleLearn() {
        uni.navigateTo({ url: "/pages/competition/startLearn" })
      },
      handleMore() {
        this.showMore = !this.showMore
      }
    }
  }
</script>

<style lang="scss" scoped>
  .rule {
    width: 100%;
    /*  margin: 12rpx auto; */
    line-height: 46rpx;
    font-size: 16px;
    position: relative;
  }
  .rule-time {
    text-align: center;
  }
  .rule-title {
    text-align: center;
    font-size: 16px;
    line-height: 72rpx;
    padding-bottom: 10px;
    color: #fff;
    transform: translateY(10px);
    z-index: 1;
    position: relative;
    font-weight: bold;
    background: #e33824;
  }
  .rule-content {
    background: #fff;
    z-index: 2;
    text-indent: 60rpx;
    position: relative;
    padding: 16px 16px;
    border: 2px solid #e33824;
    text-align: justify;
  }
  .sub-text {
    font-size: 17px;
  }
  .con {
    min-height: 100vh;
    background: url(/static/images/competition/logbg_3.png) no-repeat;
    background-size: 100% 100%;
    padding: 20rpx;
  }
  .content {
    line-height: 26px;
    font-size: 16px;
    margin-top: 20rpx;
  }
  .title {
    /* line-height: 60rpx;
  font-size: 20px; */
    text-align: center;
    font-weight: bold;
    color: #0f5f84;
    font-size: 46rpx;
    margin-bottom: 20rpx;
  }
  .close-date {
    font-weight: bold;
  }
  .more {
    color: #4098f7;
    text-decoration: underline;
    margin-left: 8rpx;
    font-weight: bold;
  }

  .sub-title {
    color: #d01f25;
    font-size: 18px;
    font-weight: bold;
  }

  .nor-title {
    color: #d01f25;
    font-size: 16px;
    font-weight: bold;
    margin-right: 8rpx;
  }
  .buttonList {
    display: flex;
    justify-content: space-evenly;
    margin-top: 30rpx;
    > button {
      width: 40%;
      color: #fff;
    }
  }
  .completeList {
    text-align: center;
    margin-top: 100rpx;
    font-size: 20px;
    line-height: 40px;
    font-weight: bold;
    color: #f58335;
  }
  .btn-learn {
    background: #75affd;
    box-shadow: 0 0 10rpx rgba(0, 0, 0, 0.1);
  }
  .btn-exam {
    background: #f59a23;
    box-shadow: 0 0 10rpx rgba(0, 0, 0, 0.1);
  }
  .eduxd-bg {
    background-repeat: no-repeat !important;
    background-size: 100% 100% !important;
    background-position: top !important;
  }
</style>

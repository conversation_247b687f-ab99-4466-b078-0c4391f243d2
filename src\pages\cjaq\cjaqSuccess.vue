<template>
  <view>
    <view class="ray-msg-content">
      <text class="ray-msg-icon"></text>
      <view class="ray-msg-title">试卷已提交！</view>
      <view class="fixed-msg-info">
        测试成绩：<text style="color: #d9001b">
          {{ score }}
        </text>
        分
      </view>
      <view class="ray-msg-btn" @click="handelExamDetail">查看考试详情</view>
    </view>
  </view>
</template>

<script>
  export default {
    name: "cjaq-success",
    onLoad(options) {
      this.baseId = options.baseId
      this.arrangeId = options.arrangeId
      this.paperAnswerId = options.paperAnswerId
      this.score = options.score
    },
    data() {
      return {
        baseId: "",
        arrangeId: "",
        paperAnswerId: "",
        score: ""
      }
    },
    methods: {
      handelExamDetail(item) {
        uni.navigateTo({
          url: `/pages/cjaq/examDetail?baseId=${this.baseId}&arrangeId=${this.arrangeId}&paperAnswerId=${this.paperAnswerId}`
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
  @mixin fixed($l: 0, $t: 0, $r: auto, $b: auto) {
    position: fixed;
    left: $l;
    top: $t;
    right: $r;
    bottom: $b;
  }
  .ray-msg-content {
    width: 590rpx;
    height: auto;
    margin: auto;
    padding: 60rpx;
    @include fixed(50%, 20%);
    z-index: 10000;
    transform: translate(-50%, -50%);
    border-radius: 28rpx;
    background-color: #f1f1f1;
    box-sizing: border-box;
    text-align: center;
  }
  .ray-msg-icon {
    display: inline-block;
    width: 152rpx;
    height: 152rpx;
    mask: url(https://static.jiaoyubao.cn/images/common/icon-check-block.svg) no-repeat;
    mask-size: 100% 100%;
    background-color: #53b883;
    &.ray-msg-error {
      mask: url(https://static.jiaoyubao.cn/images/common/icon-cross-block.svg) no-repeat;
      background-color: #fe1940;
    }
  }
  .ray-msg-title {
    margin: 20rpx auto 40rpx;
    font-size: 36rpx;
    line-height: 56rpx;
    color: #000000;
  }
  .fixed-msg-info {
    color: #555555;
    margin-top: 30rpx;
    padding: 0 12rpx;
    font-weight: bold;
    font-size: 45rpx;
    line-height: 40rpx;
  }
  .ray-msg-btn {
    width: 100%;
    height: 88rpx;
    margin: 60rpx auto 0;
    background: #f59a23;
    border-radius: 44rpx;
    font-size: 34rpx;
    line-height: 88rpx;
    color: #fff;
    &.ray-msg-error {
      background: #fe1940;
    }
  }
</style>

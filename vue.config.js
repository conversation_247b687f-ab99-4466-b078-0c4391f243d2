/*
 * @Description: vue.config.js
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-06-26 14:43:57
 * @LastEditTime: 2025-06-19 09:55:46
 */
const TransformPages = require("uni-read-pages")
const { webpack } = new TransformPages()
const UnoCSS = require("@unocss/webpack").default

let pluginsDev = new webpack.DefinePlugin({
  ROUTES: webpack.DefinePlugin.runtimeValue(() => {
    const tfPages = new TransformPages({
      includes: ["path", "name", "aliasPath"]
    })
    return JSON.stringify(tfPages.routes)
  }, true)
})
let publicPath = "/"
module.exports = {
  transpileDependencies: ["uview-ui"],
  configureWebpack: {
    resolve: {
      alias: {
        "/": "@"
      }
    }
  },
  publicPath: publicPath,
  lintOnSave: true,
  productionSourceMap: false, // 生产打包时不输出map文件，增加打包速度
  configureWebpack: config => {
    if (
      process.env.VUE_APP_ENV === "production" ||
      process.env.VUE_APP_ENV === "hwproduction" ||
      process.env.VUE_APP_ENV === "xdproduction"
    ) {
      config.optimization.minimizer[0].options.minimizer.options.compress.warnings = false
      config.optimization.minimizer[0].options.minimizer.options.compress.drop_console = true
      config.optimization.minimizer[0].options.minimizer.options.compress.drop_debugger = true
      config.optimization.minimizer[0].options.minimizer.options.compress.pure_funcs = [
        "console.log"
      ]
    }
    config.plugins.push(pluginsDev)
    config.plugins.push(UnoCSS())
    config.optimization.realContentHash = true
  },
  chainWebpack(config) {
    // config.module.rule("vue").uses.delete("cache-loader")
    // config.merge({
    //   cache: false
    // })
  },
  css: {
    extract: true
  },
  devServer: {
    hot: true,
    port: 9090,
    open: false,
    proxy: {
      "/dev-api": {
        // target: `http://192.168.1.168:1949`, // 阮少川本地
        target: `https://eduapi.bkehs.com`, // 生产
        ws: true,
        changeOrigin: true,
        pathRewrite: {
          "^/dev-api": "/"
        }
      }
    },
    client: {
      //当出现编译错误或警告时，在浏览器中是否显示全屏覆盖。  示例为只显示错误信息
      overlay: {
        runtimeErrors: false
      }
    }
  }
}

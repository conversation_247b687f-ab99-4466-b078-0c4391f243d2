<!--
 * @Description: 北蔡防灾减灾首页
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-10-08 15:16:05
 * @LastEditTime: 2024-03-05 17:28:49
-->

<template>
  <view class="con">
    <image
      class="tenant-icon"
      src="https://training-voc.obs.cn-north-4.myhuaweicloud.com/town_logo.png"
      mode="heightFix"
    ></image>
    <view class="title"> 北蔡镇安全发展和综合减灾知识平台 </view>

    <view class="list" v-if="userType !== '03'">
      <view class="list-a" @click="handleLearn"
        ><img src="@/static/images/town/learn.png" />
        <view>学习</view>
      </view>
      <view class="list-b" @click="handleExam('exercise')">
        <img src="@/static/images/town/topic.png" />
        <view>做题</view>
      </view>
      <view class="list-c" @click="handleExam('exam')"
        ><img src="@/static/images/town/test.png" />
        <view>考试</view>
      </view>
      <view class="list-d" @click="handleCount"
        ><img src="@/static/images/town/count.png" />
        <view>统计数据</view>
      </view>
      <view class="list-e" @click="handleExam('review')"
        ><img src="@/static/images/town/review.png" />
        <view>回顾</view>
      </view>
    </view>
    <view v-else class="tabulation">
      <view class="tabulation-item" style="background-color: #d96773" @click="handleLearn"
        ><img src="@/static/images/town/learn.png" />
        <view>学习</view>
      </view>
      <view class="tabulation-item" style="background-color: #e69f3c" @click="handleCount"
        ><img src="@/static/images/town/count.png" />
        <view>统计数据</view>
      </view>
    </view>
  </view>
</template>

<script>
  import { getTownExamInfo } from "@/api/exam/base.js"
  export default {
    data() {
      return {
        showMore: false,
        exerciseData: {},
        examData: {},
        userType: localStorage.getItem("userType")
      }
    },
    created() {
      this.fetchData()
    },
    methods: {
      handleExam(flag) {
        if (flag === "exercise") {
          if (!this.exerciseData || !this.exerciseData.baseId) {
            return this.$modal.msgError("暂无可用考试")
          }
          uni.navigateTo({
            url: `/pages/town/exam?baseId=${this.exerciseData.baseId}&arrangeId=${this.exerciseData.arrangeId}`
          })
        } else if (flag === "exam") {
          if (!this.examData || !this.examData.baseId) {
            return this.$modal.msgError("暂无可用考试")
          }
          uni.navigateTo({
            url: `/pages/town/exam?baseId=${this.examData.baseId}&arrangeId=${this.examData.arrangeId}`
          })
        } else {
          uni.navigateTo({
            url: `/pages/town/review`
          })
        }
      },

      handleCount() {
        uni.navigateTo({
          url: "/pages/town/count"
        })
      },

      handleLearn() {
        uni.navigateTo({ url: "/pages/competition/startLearn" })
      },
      handleMore() {
        this.showMore = !this.showMore
      },
      async fetchData() {
        const { data } = await getTownExamInfo({
          userType: this.userType ? this.userType : undefined
        })
        if (this.userType) {
          this.exerciseData = data.find(item => item.baseId == "2068")
          this.examData = data.find(item => item.baseId == "2067")
        } else {
          this.exerciseData = data.find(item => item.baseId == "2066")
          this.examData = data.find(item => item.baseId == "2057")
        }
      }
    }
  }
</script>

<style lang="scss" scoped>
  .con {
    min-height: 100vh;
    background-color: #fff;
    padding: 20rpx 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    .tenant-icon {
      height: 150rpx;
      margin-top: 50px;
    }
    .title {
      margin-top: 20px;
      color: #ee1d23;
      font-size: 42rpx;
      font-weight: 700;
    }
    .list {
      margin-top: 40px;
      width: 100%;
      height: 300px;
      font-size: 20px;
      color: white;
      display: grid;
      grid-template-columns: 1fr 1fr;
      grid-template-areas:
        "learn topic"
        "learn test"
        "count review";

      .list-a {
        grid-area: learn;
        background-color: #e16371;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        > view {
          margin-top: 10px;
        }
      }
      .list-b {
        grid-area: topic;
        background-color: #f9ce32;
        display: flex;
        align-items: center;
        justify-content: center;
        > view {
          margin-left: 10px;
        }
      }
      .list-c {
        grid-area: test;
        background-color: #f6882e;
        display: flex;
        align-items: center;
        justify-content: center;
        > view {
          margin-left: 10px;
        }
      }
      .list-d {
        grid-area: count;
        background-color: #9881da;
        display: flex;
        align-items: center;
        justify-content: center;
        > view {
          margin-left: 10px;
        }
      }
      .list-e {
        grid-area: review;
        background-color: #84c25a;
        display: flex;
        align-items: center;
        justify-content: center;
        > view {
          margin-left: 10px;
        }
      }
    }

    .tabulation {
      margin-top: 40px;
      width: 100%;
      display: flex;
      .tabulation-item {
        color: white;
        font-size: 20px;
        width: 50%;
        height: 375rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        > view {
          margin-left: 10px;
        }
      }
    }
  }
</style>

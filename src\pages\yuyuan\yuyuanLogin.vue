<!--
 * @Description: 豫园商城培训登录页
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-06-19 15:25:23
 * @LastEditTime: 2025-06-19 11:09:29
-->

<template>
  <view class="normal-login-container">
    <view class="logincon">
      <view class="title"> 豫园商城培训项目 </view>
      <view class="login-form-container">
        <custom-tabs type="a1" :value="activeTab" @change="tabChange" backgroundColor="transparent">
          <!-- <custom-tab-pane label="确认信息" name="a1_1"> </custom-tab-pane> -->
          <custom-tab-pane label="填写学员信息" name="a1_2"> </custom-tab-pane>
        </custom-tabs>
        <view class="login-form-content">
          <uni-forms v-if="false" ref="loginFormRef" :rules="loginFormRule" :modelValue="loginForm">
            <uni-forms-item name="username">
              <uni-easyinput v-model="loginForm.username" placeholder="请输入您的手机号" />
            </uni-forms-item>
            <uni-forms-item name="password">
              <uni-easyinput
                type="password"
                v-model="loginForm.password"
                placeholder="请输入密码"
              />
            </uni-forms-item>
            <view class="action-btn">
              <button class="login-btn cu-btn block bg-blue lg round" @click="handleLogin"
                >提交</button
              >
            </view>
          </uni-forms>
          <uni-forms
            v-if="activeTab === 0"
            ref="registerFormRef"
            :rules="registerFormRule"
            :modelValue="registerForm"
          >
            <uni-forms-item name="mobile">
              <uni-easyinput
                maxlength="11"
                v-model="registerForm.mobile"
                placeholder="请输入手机号"
                @input="onMobileInput"
              />
            </uni-forms-item>
            <uni-forms-item name="userName">
              <uni-easyinput v-model="registerForm.userName" placeholder="请输入姓名" />
            </uni-forms-item>
            <uni-forms-item name="dept">
              <uniCombox
                labelKey="label"
                valueKey="id"
                :candidates="deptList"
                placeholder="请选择所属单位"
                v-model="registerForm.dept"
                @update:modelValue="selectChange"
                ref="deptCombox"
              ></uniCombox>
            </uni-forms-item>
            <uni-forms-item name="level2Dept">
              <uni-easyinput v-model="registerForm.level2Dept" placeholder="请输入所属部门" />
            </uni-forms-item>
            <view class="action-btn">
              <button class="login-btn cu-btn block bg-blue lg round" @click="handleRegister"
                >提交</button
              >
            </view>
          </uni-forms>
        </view>
      </view>
    </view>
    <yangr-msg
      v-if="yangrMsgShow"
      :title="title"
      :info="info"
      :type="type"
      :btn="btn"
      @yangrMsgEvent="confirmRegister"
    ></yangr-msg>
  </view>
</template>

<script>
  import uniCombox from "@/components/uni-combox/index.vue"
  import yangrMsg from "@/components/yangr-msg/yangr-msg.vue"
  import { zhzgRegister, thirdPartyCheck } from "@/api/system/login.js"
  import { mapGetters } from "vuex"
  import { getExamRange, getAnswerRecord } from "@/api/exam/base"
  import { deptTreeSelect } from "@/api/system/user"
  import { isMobile } from "@/utils/validate"

  export default {
    components: {
      uniCombox,
      yangrMsg
    },
    data() {
      return {
        loginForm: {
          username: "",
          password: ""
        },
        registerForm: {
          dept: "",
          level1Dept: "",
          level2Dept: "",
          userName: "",
          mobile: "",
          jobs: "",
          nature: ""
        },
        canCode: false,
        codeMsg: "获取验证码",
        interval: null,
        activeTab: 0,
        countryList: [],
        registerFormRule: {
          dept: {
            rules: [
              {
                required: true,
                errorMessage: "所属单位不能为空"
              }
            ]
          },
          level2Dept: {
            rules: [
              {
                required: true,
                errorMessage: "部门不能为空"
              }
            ]
          },
          userName: {
            rules: [
              {
                required: true,
                errorMessage: "姓名不能为空"
              }
            ]
          },
          mobile: {
            rules: [
              {
                required: true,
                errorMessage: "手机号不能为空"
              },
              {
                validateFunction: function (rule, value, data, callback) {
                  if (!isMobile(value)) {
                    callback("手机号格式不正确，请重新填写")
                  }
                }
              }
            ]
          }
        },
        title: "",
        info: "",
        type: "",
        yangrMsgShow: false,
        btn: "",
        questionnaireId: "2044",
        questionnaireIssuedId: "2131",
        userType: null,
        deptList: [],
        mobileInputTimer: null
      }
    },
    mounted() {
      if (this.$route.query.userType) {
        this.userType = this.$route.query.userType
      }
      this.getDeptList()
      this.$nextTick(() => {
        // this.$refs.loginFormRef.setRules(this.loginFormRule)
        if (this.$refs.registerFormRef && this.$refs.registerFormRef.setRules) {
          this.$refs.registerFormRef.setRules(this.registerFormRule)
        }
      })
    },
    computed: {
      ...mapGetters({
        domainInfo: "domainInfo",
        domainName: "domainName"
      })
    },
    methods: {
      // 注册
      handleRegister() {
        this.$refs["registerFormRef"].validate().then(async () => {
          // 确保dept是id值
          const params = {
            ...this.registerForm,
            userType: this.userType
          }

          // 确保dept是id值而不是文本
          if (params.dept) {
            // 转换为字符串便于处理
            const deptValue = String(params.dept)

            // 如果dept不是数字格式(可能是文本)，尝试从deptList找到对应的id
            if (isNaN(deptValue)) {
              // 尝试根据label找到对应的id
              const matchedDept = this.deptList.find(item => item.label === deptValue)
              if (matchedDept) {
                params.dept = matchedDept.id
              }
            } else {
              // 确保是数字类型，防止传递字符串格式的数字
              params.dept = Number(deptValue)
            }
          }

          console.log("提交的表单数据:", params)

          const res = await zhzgRegister(params)
          if (res.code === 200) {
            this.$store
              .dispatch("Login", {
                username: this.registerForm.mobile,
                password: "123456"
              })
              .then(async () => {
                await this.loginSuccess()
              })
            // this.title = "确认信息密码为："
            // this.info = "123456"
            // this.type = "success"
            // this.btn = "知道了"
            // this.yangrMsgShow = true
          }
        })
      },
      // 登录
      async handleLogin() {
        this.$refs["loginFormRef"]
          .validate()
          .then(() => {
            this.$store.dispatch("Login", this.loginForm).then(async () => {
              await this.loginSuccess()
            })
          })
          .catch(err => {
            this.$modal.msgError("必填项不能为空")
          })
      },
      // 登录成功后，处理函数
      async loginSuccess() {
        try {
          // 设置用户信息
          await this.$store.dispatch("GetInfo")

          // 获取考试范围信息
          const examRangeRes = await getExamRange({ userType: this.userType })

          if (examRangeRes.code === 200 && examRangeRes.data) {
            const { arrangeId } = examRangeRes.data

            // 获取答卷记录状态
            const recordRes = await getAnswerRecord(arrangeId)

            if (recordRes.code === 200) {
              const recordStatus = recordRes.data

              // 根据返回状态处理不同逻辑
              if (recordStatus === 1) {
                this.title = "已完成培训流程，成绩合格，不需要重复参加"
                this.type = "error"
                this.yangrMsgShow = true
                setTimeout(() => {
                  this.yangrMsgShow = false
                }, 3000)
                return
              } else if (recordStatus === 2) {
                this.title = "暂无考试机会"
                this.type = "error"
                this.yangrMsgShow = true
                setTimeout(() => {
                  this.yangrMsgShow = false
                }, 3000)
                return
              }
              // recordStatus === 0 时继续执行原逻辑
            }
          }

          // 原有的跳转逻辑（recordStatus === 0 时执行）
          let url = `/pages/mine/my-questionnaire/doQuestionnaire/index?questionnaireId=${this.questionnaireId}&questionnaireIssuedId=${this.questionnaireIssuedId}`
          if (this.userType) {
            url += `&userType=${this.userType}`
          }
          uni.navigateTo({
            url: url
          })
        } catch (error) {
          console.error("检查考试状态时出错:", error)
          // 出错时仍执行原逻辑
          let url = `/pages/mine/my-questionnaire/doQuestionnaire/index?questionnaireId=${this.questionnaireId}&questionnaireIssuedId=${this.questionnaireIssuedId}`
          if (this.userType) {
            url += `&userType=${this.userType}`
          }
          uni.navigateTo({
            url: url
          })
        }
      },
      tabChange(item) {
        this.activeTab = item.value
        this.$nextTick(() => {
          this.$refs.registerFormRef.setRules(this.registerFormRule)
        })
      },
      confirmRegister() {
        this.yangrMsgShow = false
        this.$store
          .dispatch("Login", {
            username: this.registerForm.mobile,
            password: "123456"
          })
          .then(async () => {
            await this.loginSuccess()
          })
      },

      async getDeptList() {
        const res = await deptTreeSelect({ parentId: 100 })
        this.deptList = res.data || []
      },
      selectChange(val) {
        // 设置dept为id值
        this.registerForm.dept = val
        // 设置level1Dept为显示文本，仅用于显示
        const matchedDept = this.deptList.find(item => item.id === val)
        if (matchedDept) {
          this.registerForm.level1Dept = matchedDept.label
        }
      },
      onMobileInput(value) {
        // 每次手机号输入变化时，立即清除其他字段内容
        this.clearOtherFields()

        // 清除之前的计时器
        if (this.mobileInputTimer) {
          clearTimeout(this.mobileInputTimer)
        }

        // 设置新的计时器，500ms后执行检查
        this.mobileInputTimer = setTimeout(() => {
          // 当用户停止输入500ms后，执行检查
          if (value && value.length === 11) {
            this.checkUserExists()
          }
        }, 500)
      },

      // 清除其他字段的内容
      clearOtherFields() {
        this.registerForm.userName = ""
        this.registerForm.dept = ""
        this.registerForm.level1Dept = ""
        this.registerForm.level2Dept = ""

        // 同时清除部门选择框的显示状态
        this.$nextTick(() => {
          const combox = this.$refs.deptCombox
          if (combox) {
            combox.dictVal = ""
            combox.inputVal = ""
            this.$forceUpdate()
          }
        })
      },
      async checkUserExists() {
        console.log("checkUserExists")

        // 检查手机号是否为空或格式不正确
        if (!this.registerForm.mobile || !isMobile(this.registerForm.mobile)) {
          return
        }

        try {
          const res = await thirdPartyCheck(this.registerForm.mobile)
          console.log("检查用户存在结果:", res)
          // 如果返回不为null，说明用户已注册
          if (res.data) {
            uni.showModal({
              title: "提示",
              content: "该手机号已注册，是否自动填充您的信息？",
              success: result => {
                if (result.confirm) {
                  const userData = res.data
                  // 设置其他字段
                  this.registerForm.userName = userData.userName || ""
                  this.registerForm.dept = userData.dept || ""
                  this.registerForm.level2Dept = userData.level2Dept || ""

                  // 处理部门选择
                  this.fillDeptInfo(userData)
                }
              }
            })
          }
        } catch (error) {
          console.error("检查用户是否存在时出错:", error)
        }
      },

      // 新增方法专门处理部门填充
      fillDeptInfo(userData) {
        // 基础设置，确保表单数据正确
        const deptId = userData.dept

        // 确保deptList已加载
        if (!this.deptList || this.deptList.length === 0) {
          // 如果部门列表未加载，直接设置表单值，但UI可能无法显示
          this.registerForm.dept = deptId // 保存为id值
          this.registerForm.level1Dept = userData.level1Dept || ""
          return
        }

        // 查找部门在列表中的索引
        const deptIndex = this.deptList.findIndex(item => String(item.id) === String(deptId))

        if (deptIndex === -1) {
          // 未找到匹配项，直接设置值
          this.registerForm.dept = deptId // 保存为id值
          this.registerForm.level1Dept = userData.level1Dept || ""
          return
        }

        // 找到匹配项，执行下面的步骤

        // 1. 基础赋值 - dept始终存储id值，level1Dept存储显示文本
        this.registerForm.dept = deptId // 保存为id值
        this.registerForm.level1Dept = this.deptList[deptIndex].label

        // 2. 直接操作DOM解决uni-combox回显问题（终极方案）
        this.$nextTick(() => {
          // 延迟执行确保组件已渲染
          setTimeout(() => {
            try {
              // 获取combox组件实例
              const combox = this.$refs.deptCombox
              if (!combox) return

              // 直接设置内部状态
              combox.dictVal = deptId // 设置实际值
              combox.inputVal = this.deptList[deptIndex].label // 设置显示文本

              // 模拟选择动作
              combox.filterCandidates = this.deptList // 设置候选项

              // 确保组件更新
              this.$forceUpdate()

              console.log("部门选择框回显设置完成", {
                deptId,
                label: this.deptList[deptIndex].label,
                comboxState: {
                  dictVal: combox.dictVal,
                  inputVal: combox.inputVal
                }
              })
            } catch (err) {
              console.error("操作部门选择框出错", err)
            }
          }, 200)
        })
      }
    }
  }
</script>

<style lang="scss">
  ::v-deep .uni-forms-item {
    margin-bottom: 30rpx;
  }
  ::v-deep .uni-forms-item__label {
    display: none;
  }

  ::v-deep .uni-combox {
    border: 2px solid #e7e8e8;
    .uni-input-placeholder {
      font-size: 32rpx !important;
      padding-left: 28rpx !important;
    }
    .uni-input-wrapper {
      text-align: left;
      padding-left: 28rpx !important;
    }
    .uni-combox__selector-item {
      text-align: left;
      font-size: 26rpx;
    }
  }
  ::v-deep .uni-easyinput__content {
    border-radius: 10rpx;
    height: 70rpx;
    .uni-easyinput__placeholder-class,
    .uni-input-input {
      font-size: 32rpx !important;
      padding-left: 28rpx !important;
    }
  }
  ::v-deep .fixed-msg-info {
    font-size: 58rpx;
    font-weight: bold;
    color: #333;
  }
  page {
    background-color: #f2f2f2;
  }
  .active {
    color: #888 !important;
  }
  .normal-login-container {
    background-color: #fff;
    width: 100%;
    height: 100vh;
    overflow: hidden;
    .logincon {
      padding: 0 60rpx;
      margin-top: 30rpx;
      display: flex;
      justify-content: center;
      flex-direction: column;
      width: 100%;
      align-items: center;
      .tenant-icon {
        height: 150rpx;
      }
      .title {
        text-align: center;
        margin: 20rpx 0 40rpx 0;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 44rpx;
        font-weight: 700;
        margin-bottom: 30rpx;
      }
      .login-form-container {
        width: 100%;
        padding: 0 20rpx;
        .login-form-content {
          padding: 20rpx 0;

          .code-container {
            display: flex;
            align-items: center;
          }
        }
      }
    }

    .login-form-content {
      text-align: center;
      margin: 10px auto 10px auto;
      width: 80%;

      .login-btn {
        background-color: #3a89e6;
        margin-top: 40rpx;
        height: 45px;
      }

      .xieyi {
        color: #333;
        font-size: 14px;
      }

      .login-code {
        height: 38px;
        float: right;

        .login-code-img {
          height: 38px;
          position: absolute;
          margin-left: 10px;
          width: 200rpx;
        }
      }
    }
  }

  .xieyi {
    font-size: 12px;
  }
  .action-btn {
    button {
      font-size: 16px;
    }
  }
</style>

<!--
 * @Description: 我的任务页面
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-12-13 16:29:55
 * @LastEditTime: 2024-12-27 17:03:01
-->

<template>
  <view class="min-h-screen bg-gray-50">
    <navigationBar path="/pages/mine/index" />

    <!-- 搜索栏 -->
    <!-- <view class="flex items-center justify-between px-4 py-3 bg-white sticky top-0 z-50">
      <uni-search-bar
        v-model="searchValue"
        class="flex-1 mr-3"
        radius="50"
        placeholder="请输入标题"
        clearButton="auto"
        cancelButton="none"
        @confirm="getTaskData"
      />
      <view
        class="px-4 py-2 bg-blue-500 text-white rounded-full text-sm active:bg-blue-600"
        @click="getTaskData"
      >
        搜索
      </view>
    </view> -->

    <!-- 下拉菜单 -->
    <view class="w-full bg-white border-b border-gray-100">
      <zb-dropdown-menu class="w-full">
        <zb-dropdown-item
          name="one"
          :options="taskStatusOpts"
          v-model="currentStatus"
          @change="getTaskData"
        ></zb-dropdown-item>
      </zb-dropdown-menu>
    </view>

    <view v-show="taskList.length" class="bg-white">
      <view
        v-for="item in taskList"
        :key="item.taskId"
        class="flex items-center p-4 border-b border-gray-100 hover:bg-gray-50 cursor-pointer"
        @click="goToDetail(item)"
      >
        <!-- 封面图 -->
        <view class="w-[140px] aspect-[4/3] flex-shrink-0 rounded-lg overflow-hidden">
          <image :src="item.coverPhoto" class="w-full h-full object-cover" mode="aspectFill" />
        </view>

        <!-- 内容区 -->
        <view class="flex-1 ml-4">
          <text class="text-lg font-medium text-gray-900 line-clamp-1">
            {{ item.taskName }}
          </text>
          <view class="space-y-1 mb-2">
            <view class="text-sm text-gray-400"
              >培训时间：{{ item.startTime }} ~ {{ item.endTime }}</view
            >
            <view class="flex items-center">
              <text class="text-sm text-gray-400 mr-2">状态：</text>
              <view :class="getStatusStyle(item.taskStatus)">
                {{ taskStatusOpts.find(p => p.value === item.taskStatus).text }}
              </view>
            </view>
          </view>
          <!-- 进度条 -->
          <view class="flex items-center w-full gap-2">
            <view class="w-full h-1 bg-gray-100 rounded-full overflow-hidden">
              <view
                class="h-full rounded-full bg-#c280ff"
                :style="{
                  width: formatPercent(item.rateLearning || 0)
                }"
              ></view>
            </view>
            <view>{{ formatPercent(item.rateLearning || 0) }}</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <xw-empty :isShow="!taskList.length" text="暂无培训任务" textColor="#777777" />
  </view>
</template>

<script>
  import { fetchTaskMyList } from "@/api/trainingTask"
  import xwEmpty from "@/components/xw-empty/xw-empty"

  export default {
    components: { xwEmpty },

    onLoad() {
      this.getTaskData("onLoad")
      this.getTaskStatusList()
    },

    onReachBottom() {
      if (this.taskList.length !== this.dataTotal) {
        this.pageNum += 1
        this.getTaskData("onReachBottom")
      }
    },
    data() {
      return {
        searchValue: "",
        currentStatus: undefined,
        taskStatusOpts: [],
        taskList: [],
        pageSize: 12,
        pageNum: 1,
        dataTotal: 0
      }
    },

    methods: {
      getStatusStyle(status) {
        const baseStyle = "px-2 rounded text-sm inline-flex items-center justify-center"
        const styleMap = {
          1: `${baseStyle} bg-gray-100 text-gray-600`,
          2: `${baseStyle} bg-orange-100 text-orange-600`,
          3: `${baseStyle} bg-green-100 text-green-600`,
          4: `${baseStyle} bg-red-100 text-red-600`
        }
        return styleMap[status] || styleMap["1"]
      },
      async getTaskData(flag) {
        if (flag !== "onReachBottom") {
          this.pageNum = 1
        }
        const queryData = {
          pageSize: this.pageSize,
          pageNum: this.pageNum,
          taskStatus: this.currentStatus
        }
        const { rows, total } = await fetchTaskMyList(queryData)
        this.dataTotal = total || 0
        this.taskList = flag === "onReachBottom" ? this.taskList.concat(rows) : rows
        if (!this.taskList) this.taskList = []
      },
      async getTaskStatusList() {
        this.taskStatusOpts = await this.$getDict("training_task_status")
        if (this.taskStatusOpts[0].text === "全部") return
        this.taskStatusOpts.unshift({
          text: "全部",
          value: undefined
        })
      },
      formatPercent(value) {
        return Math.round(value * 100) + "%"
      },
      goToDetail(item) {
        uni.navigateTo({
          url: `/pages/mine/task/detail/index?id=${item.taskId}`
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
  ::v-deep .uni-searchbar__box {
    justify-content: flex-start !important;
  }

  .hover\:bg-gray-50:hover {
    background-color: rgba(249, 250, 251, 0.8);
  }

  button {
    &:active {
      transform: scale(0.98);
    }
  }
</style>
